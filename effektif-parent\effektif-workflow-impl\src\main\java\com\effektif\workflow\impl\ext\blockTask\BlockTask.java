package com.effektif.workflow.impl.ext.blockTask;

import com.effektif.workflow.api.activities.BlockExecutionTask;
import com.effektif.workflow.api.ext.ExecutionPojo;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.model.Output;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.api.workflow.Extensible;
import com.effektif.workflow.impl.workflow.WorkflowImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import lombok.Data;

import java.io.*;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * wansong oneflow
 * <AUTHOR>
 */
@Data
public class BlockTask extends Extensible implements Serializable {
  private static final long serialVersionUID = 4517415651506340745L;
  public enum  ExecutionType{
    auto,task;
  }
  public enum State{
    /**
     * 待执行
     */
    waiting,
    /**
     * 执行完成
     */
    pass,
    /**
     * 执行失败
     */
    error;
  }
  protected TaskId id;
  protected String tenantId;
  protected String appId;
  protected String type;
  protected String subType;
  protected String name;
  protected String description;
  protected String workflowId;
  protected String workflowName;
  protected String sourceWorkflowId;
  protected String workflowInstanceId;
  protected String applicantId;
  protected String activityId;
  protected String activityInstanceId;
  /**
   * 执行人
   */
  protected String executor;
  /**
   * 执行时间
   */
  protected String executeTime;
  protected Long createTime;
  protected Long modifyTime;
  protected String state;
  /**
   * 执行类型 ： auto or task
   */
  protected String executeType;
  /**
   * 任务类型： 具体的任务类型， 从任务中取得
   */
  protected String taskType;
  /**
   * 自动节点的后动作
   */
  protected ExecutionPojo execution;
  /**
   * 需要人参与的节点内容
   */
  public Map<String,Object> extension;
  /**
   * 输出参数，遵从 json path 语法，
   * 如果当前 值 为 字符串，则直接使用 . 来进行表格
   * [{"to":"custom_variable##a","from":"$"},{"to":"custom_variable##a","from":"$.a"}]
   */
  protected List<Output> outputs;

  private String errMsgI18N;
  private Map triggerSource;
  /**
   * 过期时间
   */
  private Date expireDate;
  public BlockTask() {

  }

  public BlockTask(TaskId taskId, String activityInstanceId) {
    this.id = taskId;
    this.activityInstanceId = activityInstanceId;
  }

  /**
   * @param taskInstance
   * @param execution
   * @return
   */
  public BlockTask buildExecutionTask(ActivityInstanceImpl taskInstance,
                                      ExecutionPojo execution,Map extension) {
    WorkflowInstanceImpl instance = taskInstance.workflowInstance;
    WorkflowImpl workflow = taskInstance.getWorkflow();
    BlockExecutionTask config = (BlockExecutionTask) taskInstance.activity.activity;
    this.tenantId = instance.getTenantId();
    this.appId = taskInstance.getWorkflow().getAppId();
    this.workflowId = taskInstance.getWorkflow().getId().toString();
    this.sourceWorkflowId=taskInstance.getWorkflow().getSourceWorkflowId();
    this.workflowInstanceId = instance.getId().toString();
    this.applicantId= (String) instance.getTransientProperty("applicantId");
    this.triggerSource= (Map) instance.getTransientProperty("triggerSource");
    this.workflowName= workflow.getName();
    this.name = workflow.getName();
    this.type = workflow.getType();
    this.subType = workflow.getSubType();
    this.description = workflow.getDescription();
    this.activityId = taskInstance.getId();
    this.activityInstanceId=taskInstance.getId();
    this.createTime = System.currentTimeMillis();
    this.modifyTime = this.createTime;
    this.state = WorkflowConstants.AutoTaskState.IN_PROGRESS;
    this.executeType=config.getExecuteType();
    this.execution = execution;
    this.extension = extension;
    this.name=config.getName();
    this.description = config.getDescription();
    this.activityId= config.getId();
    this.taskType= Objects.nonNull(execution)?execution.getTaskType():Objects.nonNull(extension)?extension.get("taskType").toString():null;
    this.outputs=config.getOutputList();
    return this;
  }
}
