package com.effektif.workflow.impl.conditions;

import com.effektif.workflow.api.condition.Condition;
import com.effektif.workflow.api.condition.InStrings;
import com.effektif.workflow.impl.data.TypedValueImpl;
import com.google.common.base.Splitter;

public class InStringsImpl extends Comp<PERSON>torImpl {
    @Override
    public Class<? extends Condition> getApiType() {
        return InStrings.class;
    }

    @Override
    public boolean compare(TypedValueImpl leftValue, TypedValueImpl rightValue) {
        if (isNull(leftValue)) {
            return false;
        }
        String left = (String) leftValue.value;
        String right = (String) rightValue.value;

        return Splitter.on(";").splitToList(right).contains(left);
    }

    @Override
    public String getComparatorSymbol() {
        return "<inStrings>";
    }
}
