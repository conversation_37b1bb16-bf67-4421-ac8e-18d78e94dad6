package com.effektif.workflow.impl.ext;

import com.effektif.workflow.api.activities.ErrMsgI18N;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * desc: 阶段推进器解析任务处理人时返回的处理人信息
 * author: liangnan
 * date: 2023/7/25 18:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CandidateParseResult {
    private boolean success;
    private List<String> candidateIds;
    private String message;
    private List<ErrMsgI18N> errMsgI18N;

    public String getMessage() {
        return this.success ? StringUtils.EMPTY : this.message;
    }

    public List<String> getCandidateIds() {
        return !this.success || Objects.isNull(this.candidateIds) ? Lists.newArrayList() : this.candidateIds;
    }

    public static CandidateParseResult success(List<String> candidateIds) {
        return new CandidateParseResult(true, candidateIds, null, null);
    }

    public static CandidateParseResult error(List<ErrMsgI18N> errMsgI18N) {
        return new CandidateParseResult(false, null, null, errMsgI18N);
    }

    public static CandidateParseResult error(String message) {
        return new CandidateParseResult(false, null, message, null);
    }
}
