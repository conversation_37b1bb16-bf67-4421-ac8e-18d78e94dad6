package com.effektif.workflow.impl.ext;

import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.model.Message;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.api.model.TypedValue;
import com.effektif.workflow.api.model.WorkflowInstanceId;
import com.effektif.workflow.api.query.UserTaskQuery;
import com.effektif.workflow.api.query.WorkflowInstanceQuery;
import com.effektif.workflow.api.workflow.Activity;
import com.effektif.workflow.api.workflowinstance.ActivityInstance;
import com.effektif.workflow.impl.WorkflowEngineImpl;
import com.effektif.workflow.impl.WorkflowInstanceStore;
import com.effektif.workflow.impl.activity.types.UserTaskImpl;
import com.effektif.workflow.impl.activity.types.userTask.handler.create.UserTaskCreateStageHandler;
import com.effektif.workflow.impl.configuration.Brewable;
import com.effektif.workflow.impl.configuration.Brewery;
import com.effektif.workflow.impl.util.Lists;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceUpdates;
import com.facishare.paas.workflow.bus.EngineEventBus;
import com.facishare.paas.workflow.bus.api.TaskHandledEvent;
import com.facishare.paas.workflow.bus.api.type.FlowTag;
import com.facishare.paas.workflow.bus.api.type.TaskState;
import com.facishare.paas.workflow.bus.model.BusConstant;
import com.facishare.paas.workflow.bus.model.MQContext;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.*;

import static com.effektif.workflow.api.ext.WorkflowConstants.TaskType.STAGE_TASK_ITEM;

/**
 * zhenghaibo
 * 16/6/12 14:46
 */
public class TaskFactory implements Brewable {

  public static final Logger log = LoggerFactory.getLogger(TaskFactory.class);

  private final String REGENERATE_TASK = "regenerateTask";
  private final String CREATE_SEQUENCE_TASK = "createSequenceTask";
  private final String REGENERATE_TASKS = "regenerateTasks";

  protected TaskStore taskStore;
  protected WorkflowEngineImpl workflowEngine;
  protected WorkflowInstanceStore workflowInstanceStore;

  @Override
  public void brew(Brewery brewery) {
    this.taskStore = brewery.get(TaskStore.class);
    this.workflowEngine = brewery.get(WorkflowEngineImpl.class);
    this.workflowInstanceStore = brewery.get(WorkflowInstanceStore.class);
  }

  public Task createTask(Task task) {
    if (task == null) {
      task = new Task();
    }
    TaskId taskId = task.getId();
    if (taskId == null) {
      taskId = taskStore.generateTaskId();
    }
    task.setId(taskId);
    taskStore.insertTask(task);
    return task;
  }

  public Task assignTask(String tenantId, String taskId, List<String> assigneeIds) {
    Task task = taskStore.assignTask(tenantId,taskId, assigneeIds);
    return task;
  }

  public Task completeTask(String tenantId, String taskId) {
    Task task = taskStore.completeTask(tenantId,taskId, null);
    task.setCompleted(true);
    if (Boolean.TRUE.equals(task.getActivityNotify())) {
      task.setActivityNotify(null);
      Message message = new Message(tenantId).workflowInstanceId(new WorkflowInstanceId(task.getWorkflowInstanceId())).activityInstanceId(task.getActivityInstanceId());
      workflowEngine.send(message);
      taskStore.removeTodoTask(tenantId,taskId);
    }
    return task;
  }

  /**
   * BPM调用过来的
   * @param arg
   * @param opinion
   * @return
   */
  public Task completeTaskWithCondition(CompleteTaskArg arg,ApprovalOpinion opinion) {
    String taskId = arg.getTaskId();
    String actionType =arg.getActionType();
    Map<String, Object> conditionMap = arg.getConditionMap();


    Task task = this.findTaskById(arg.getTenantId(),taskId);

    List<ApprovalOpinion> taskOpinions = task.getOpinions();
    if (taskOpinions == null) {
      taskOpinions = new ArrayList();
    }
    taskOpinions.add(opinion);
    List<String> assigneeIds = task.getAssigneeIds();
    assigneeIds = dealAssigneeIds(opinion, task, assigneeIds);

    TaskState state = TaskState.in_progress;
    if (this.mayComplete(task, opinion.getUserId(), actionType)) {
      if (null == conditionMap) {
        conditionMap = new HashMap<>();
      }
      if (WorkflowConstants.UserTaskType.ALL.equals(task.getTaskType())) {
        actionType = this.getCountersignatureResult(taskOpinions);
      }
      //bpm坚持的key样式 -_-||
      conditionMap.put("activity_" + task.getActivityId() + "##result", actionType);
      task = taskStore.completeTask(arg.getTenantId(),taskId, actionType, taskOpinions, assigneeIds, arg.getNextTaskAssignee());
      state = TaskState.pass;
    } else {
      task = taskStore.updateApprovalOpinion(arg.getTenantId(),taskId, taskOpinions, assigneeIds);
    }


    //完成任务入库后发送事件
    EngineEventBus.post(
            TaskHandledEvent.createBpm(
                    MQContext.create(arg.getTenantId(), arg.getAppId(), arg.getUserId(), null, task.getType()),
                    FlowTag.workflow_bpm,
                    "",
                    taskId,
                    state,
                    task.getTaskType(),
                    task.getNodeType(),
                    actionType,
                    task.getWorkflowInstanceId(),
                    task.getExternalApplyTask(),
                    task.getEntityId(),
                    task.getObjectId(),
                    taskOpinions.get(taskOpinions.size()-1).getId(),
                    task.getLinkApp()
            )
    );

    if (TaskState.pass.equals(state)) {
      activityNotify(arg, task);
    }

    return task;
  }

  /**
   * xt&审批流调用
   * @param arg
   * @param state
   * @param opinionList
   * @return
   */
  public Task completeTaskWithConditionObject(CompleteTaskArg arg, String state, List<ApprovalOpinion> opinionList) {
    Task task = taskStore.completeTask(arg.getTenantId(),arg.getTaskId(), arg.getActionType(), state, opinionList);
    task.setCompleted(true);
    tagAllPassCompletedOperate(task);
    EngineEventBus.post(
            TaskHandledEvent.createApprovalFlowForRequstId(
                    MQContext.create(arg.getTenantId(), arg.getAppId(), arg.getUserId(), null, task.getType()),
                    FlowTag.approvalflow,
                    arg.getTraceId(),
                    task.getId().toString(),
                    TaskState.valueOfSkipNotFound(state),
                    task.getTaskType(),
                    Objects.isNull(task.getMasterTaskId()) ? null : task.getMasterTaskId().toString(),
                    task.getNodeType(),
                    task.getSequence(),
                    arg.getActionType(),
                    task.getWorkflowInstanceId(),
                    task.getEntityId(),
                    task.getObjectId(),
                    arg.getRequestId(),
                    Objects.isNull(opinionList) || opinionList.isEmpty() ? "" : opinionList.get(opinionList.size() - 1).getId(),
                    task.getLinkApp()
            )
    );
    activityNotify(arg, task);
    return task;
  }

  /**
   * 加签节点结束后  需要执行的操作
   * @param task
   */
  private void tagAllPassCompletedOperate(Task task) {
    //只有前加签 需要将之前的任务 设置为进行中
    if (StringUtils.equals(WorkflowConstants.NodeType.TAG, task.getNodeType()) || StringUtils.equals(WorkflowConstants.NodeType.TAG_AFTER, task.getNodeType())) {
      //如果处理人都在审批意见中,则任务当前加签节点结束了,去更新原节点为进行中
      if (task.allProcessed()) {
        Task masterTask = taskStore.findTaskById(task.getTenantId(), task.getMasterTaskId().getInternal());
        List<ApprovalOpinion> opinions = masterTask.getOpinions();
        for (ApprovalOpinion opinion : opinions) {
          if (task.getId().getInternal().equals(opinion.getTaskId())) {
            opinion.setEndTime(System.currentTimeMillis());
            break;
          }
        }
        //890 添加了opinions 加签节点结束,写入原节点加签时的意见
        if (StringUtils.equals(WorkflowConstants.NodeType.TAG, task.getNodeType())) {
          //前加签  更新状态和意见
          taskStore.updateTaskState(task.getTenantId(), task.getMasterTaskId().toString(), WorkflowConstants.UserTaskStatus.IN_PROGRESS, opinions);
        } else {
          //后加签  只需要更新意见
          taskStore.updateTaskState(task.getTenantId(), task.getMasterTaskId().toString(), null, opinions);
        }
      }
    }
  }


  private List<String> dealAssigneeIds(ApprovalOpinion opinion, Task task, List<String> assigneeIds) {
    if (assigneeIds == null) {
      assigneeIds = new ArrayList<>();
      assigneeIds.add(opinion.getUserId());
      task.setAssigneeIds(assigneeIds);
    } else if (!assigneeIds.contains(opinion.getUserId())) {
      assigneeIds.add(opinion.getUserId());
    }
    return assigneeIds;
  }

  /**
   * 当前类调用 业务流&审批流调用
   * @param arg
   * @param task
   */
  private void activityNotify(CompleteTaskArg arg, Task task) {
    if (Boolean.TRUE.equals(task.getActivityNotify())) {
      arg.setType(task.getType());
      Map<String, Object> conditionMap = arg.getConditionMap();
      task.setActivityNotify(null);
      Message message = new Message(task.getTenantId()).workflowInstanceId(new WorkflowInstanceId(task.getWorkflowInstanceId())).activityInstanceId(task.getActivityInstanceId());
      if (conditionMap != null && conditionMap.size() > 0) {
        for (Map.Entry entry : conditionMap.entrySet()) {
          message.data(entry.getKey().toString(), entry.getValue());
        }
      }

      // TODO: hanmz 2017/11/17 生成下一个节点前执行后动作
      conditionMap = executeAfterAction(arg);
      if (conditionMap != null && conditionMap.size() > 0) {
        for (Map.Entry entry : conditionMap.entrySet()) {
          message.data(entry.getKey().toString(), entry.getValue());
        }
      }
      message.setTransientData(arg.getBindingMap());
      message.transientData(BusConstant.REQUEST_ID,arg.getRequestId());

      //如果是审批流且有需要生成后加签任务，不走send方法
      if(!(WorkflowConstants.AppId.APP_ID_CRM.equals(task.getAppId()) && task.needCreateAfterTagTask())){
        workflowEngine.send(message);
      }
      taskStore.removeTodoTask(task.getTenantId(), task.getId().toString());
    }
  }

  /**
   * moto时 阶段推进器调用
   *
   * @param tenantId
   * @param taskId
   * @param conditionMap
   * @param bindingMap
   * @param actionType
   * @param opinion
   * @param state
   * @return
   */
  public Task completeStageTask(String tenantId, String taskId, Map<String, Object> conditionMap, Map<String, Object> bindingMap, String actionType,
                                ApprovalOpinion opinion, String state) {
    Task task = this.findTaskById(tenantId, taskId);

    List<ApprovalOpinion> taskOpinions = task.getOpinions();
    if (taskOpinions == null) {
      taskOpinions = new ArrayList();
    }
    taskOpinions.add(opinion);
    List<String> assigneeIds = task.getAssigneeIds();
    assigneeIds = dealAssigneeIds(opinion, task, assigneeIds);

    if (task.getTaskType().equals("stage")) {
      //某个特殊标志，代表完成
      task = taskStore.completeStageTask(tenantId, taskId, actionType, taskOpinions, assigneeIds, state);
      if (task == null) {
        return null;
      }

      task.setCompleted(true);
      if (Boolean.TRUE.equals(task.getActivityNotify())) {
        task.setActivityNotify(null);
        Message message = new Message(tenantId).workflowInstanceId(new WorkflowInstanceId(task.getWorkflowInstanceId()))
          .activityInstanceId(task.getActivityInstanceId());
        if (conditionMap != null && conditionMap.size() > 0) {
          for (Map.Entry entry : conditionMap.entrySet()) {
            message.data(entry.getKey().toString(), entry.getValue());
          }
        }

        message.setTransientData(bindingMap);

        workflowEngine.send(message);
        taskStore.removeTodoTask(tenantId, taskId);
        return task;
      }
    }
    return task;
  }

  /**
   * 阶段推进器  taskItem调用
   *
   * @param tenantId
   * @param taskId
   * @param conditionMap
   * @param bindingMap
   * @param actionType
   * @param opinion
   * @param state
   * @return
   */
  public Task completeTaskItem(String tenantId, String taskId, Map<String, Object> conditionMap, Map<String, Object> bindingMap, String actionType,
                               ApprovalOpinion opinion, String state) {
    Task task = this.findTaskById(tenantId, taskId);

    List<ApprovalOpinion> taskOpinions = task.getOpinions();
    if (taskOpinions == null) {
      taskOpinions = new ArrayList();
    }
    taskOpinions.add(opinion);
    List<String> assigneeIds = task.getAssigneeIds();
    assigneeIds = dealAssigneeIds(opinion, task, assigneeIds);

    if (task.getTaskType().equals(STAGE_TASK_ITEM)) {
      task = taskStore.completeTaskItem(tenantId,taskId, actionType, taskOpinions, assigneeIds, state);
      if (task == null) {
        return null;
      }
      taskStore.removeTodoTask(tenantId, taskId);
      task.setCompleted(true);
      return task;
    }

    return task;
  }

  /**
   * 串行生成下一个任务
   */
  public void createStageSequenceTask(String tenantId, String workflowInstanceId, String stageTaskId, String itemTaskActivityId){
    createStageItemTaskByActivityId(tenantId, workflowInstanceId, stageTaskId, Lists.of(itemTaskActivityId), CREATE_SEQUENCE_TASK, "阶段任务串行生成任务失败");
  }

  /**
   * 任务生成失败重试
   */
  public void createStageTaskItemByActivityId(String tenantId, String workflowInstanceId, String stageTaskId, String itemTaskActivityId){
    createStageItemTaskByActivityId(tenantId, workflowInstanceId, stageTaskId, Lists.of(itemTaskActivityId), REGENERATE_TASK, "阶段子任务根据ActivityId生成失败");
  }

  public void createStageTaskItemByActivityIdList(String tenantId, String workflowInstanceId, String stageTaskId, List<String> itemTaskActivityIdList){
    createStageItemTaskByActivityId(tenantId, workflowInstanceId, stageTaskId, itemTaskActivityIdList, REGENERATE_TASKS
            , "阶段子任务根据ActivityId生成失败");
  }

  public void createStageItemTaskByActivityId(String tenantId, String workflowInstanceId, String stageTaskId, List<String> taskActivityIdLIst, String executeType, String logMessage){
    Task masterTask = this.findTaskById(tenantId, stageTaskId);
    WorkflowInstanceQuery workflowInstanceQuery = new WorkflowInstanceQuery().workflowInstanceId(new WorkflowInstanceId(workflowInstanceId));
    workflowInstanceQuery.setTenantId(tenantId);
    List<WorkflowInstanceImpl> workflowInstanceList = workflowInstanceStore.findWorkflowInstances(workflowInstanceQuery);
    if(Objects.isNull(workflowInstanceList) || workflowInstanceList.size() == 0){
      log.error(logMessage + "，获取是实例失败，instanceId:{}，upActivityId:{}", workflowInstanceId, taskActivityIdLIst);
      return;
    }
    List<ActivityInstanceImpl> activityInstanceList = workflowInstanceList.get(0).activityInstances;
    Optional<ActivityInstanceImpl> activityInstanceOptional = activityInstanceList.stream().filter(item -> masterTask.getActivityId().equals(item.getActivity().getId())).findFirst();
    if(!activityInstanceOptional.isPresent()){
      log.error(logMessage + "，获取activityInstance失败，instanceId:{}，masterActivityId:{}", workflowInstanceId, masterTask.getActivityId());
      return;
    }
    ActivityInstanceImpl activityInstance = activityInstanceOptional.get();
    if(!(activityInstance.getActivity().activityType instanceof UserTaskImpl)){
      log.error(logMessage + "，activityType非UserTaskImpl类型，instanceId:{}，upActivityId:{}", workflowInstanceId, taskActivityIdLIst);
      return;
    }
    UserTaskImpl userTaskImpl = (UserTaskImpl) activityInstance.getActivity().activityType;
    List<Activity> createTaskItemList = getTasks(userTaskImpl, masterTask, executeType, taskActivityIdLIst);
    UserTaskCreateStageHandler.createStageItemTask(userTaskImpl,masterTask, createTaskItemList, activityInstance);
  }

  private List<Activity> getTasks(UserTaskImpl userTaskImpl, Task masterTask, String executeType, List<String> taskActivityIdLIst) {
    if (CREATE_SEQUENCE_TASK.equals(executeType)) {
      return userTaskImpl.activity.getNextTaskItemList(taskActivityIdLIst.get(0));
    } else if (REGENERATE_TASK.equals(executeType)) {
      return userTaskImpl.activity.getTaskItemListByActivityId(taskActivityIdLIst.get(0), !masterTask.getSequence());
    } else if (REGENERATE_TASKS.equals(executeType)) {
      return userTaskImpl.activity.getTaskItemByActivityIdList(taskActivityIdLIst);
    }
    return new ArrayList<>();
  }


  public void afterAction2NextTask(String tenantId, String instanceId, Map<String, Object> conditionMap, Map<String, Object> bindingMap, String activityInstanceId) {
    Message message = new Message(tenantId).workflowInstanceId(new WorkflowInstanceId(instanceId)).activityInstanceId(activityInstanceId);
    if (conditionMap != null && conditionMap.size() > 0) {
      for (Map.Entry entry : conditionMap.entrySet()) {
        message.data(entry.getKey().toString(), entry.getValue());
      }
    }

    message.setTransientData(bindingMap);

    workflowEngine.send(message);
  }

  public void afterAction2SubProcessNextTask(String tenantId, String instanceId, Map<String, Object> bindingMap, String activityInstanceId,String state) {
    WorkflowInstanceId workflowInstanceId = new WorkflowInstanceId(instanceId);
    WorkflowInstanceImpl superWorkflowInstanceImpl = workflowInstanceStore.getById(tenantId, workflowInstanceId);
    superWorkflowInstanceImpl.updates = new WorkflowInstanceUpdates(true);
    String decide = WorkflowConstants.Action.AGREE;

    Message message = new Message(tenantId).workflowInstanceId(workflowInstanceId).activityInstanceId(activityInstanceId);
    message.setTransientData(bindingMap);

    //子驳回后, 子状态会修改为驳回 由于父驳回后的线,在900时前端删除了,故需要告诉父, 需要驳回到end
    if(WorkflowConstants.Action.REJECT.equals(state)){
      decide = WorkflowConstants.Action.REJECT;
      message.data(WorkflowConstants.TO_ACTIVITY_ID, WorkflowConstants.END);
    }
    message.data(WorkflowConstants.SystemVariable.DECIDE, decide);

    if(WorkflowConstants.Action.REJECT.equals(state)){
      //900 前端删除了 reject的线
      workflowEngine.sendByActivityId(message, superWorkflowInstanceImpl);
    }else{
      workflowEngine.send(message, superWorkflowInstanceImpl);
    }

  }
  public void retryActivityInstance(String tenantId, String workflowInstanceId, String activityInstanceId) {
    Message message = new Message(tenantId).tenantId(tenantId).workflowInstanceId(new WorkflowInstanceId(workflowInstanceId)).activityInstanceId(activityInstanceId);
    workflowEngine.retryActivityInstance(message);
  }

  @Deprecated
  public Task completeTaskWithCondition(String tenantId, String taskId, Map<String, TypedValue> conditionMap, Map<String, Object> bindingMap, String actionType, String state,
                                        List<ApprovalOpinion> opinionList) {
    Task task = taskStore.completeTask(tenantId, taskId, actionType, state, opinionList);
    task.setCompleted(true);
    if (Boolean.TRUE.equals(task.getActivityNotify())) {
      task.setActivityNotify(null);
      Message message = new Message(tenantId).workflowInstanceId(new WorkflowInstanceId(task.getWorkflowInstanceId())).activityInstanceId(task.getActivityInstanceId());
      if (conditionMap != null && conditionMap.size() > 0) {
        for (Map.Entry entry : conditionMap.entrySet()) {
          message.data(entry.getKey().toString(), ((TypedValue) entry.getValue()).getValue(), ((TypedValue) entry.getValue()).getDataType());
        }
      }
      message.setTransientData(bindingMap);
      workflowEngine.send(message);
      taskStore.removeTodoTask(tenantId, taskId);
    }
    return task;
  }

  public Task findTaskById(String tenantId, String taskId) {
    List<Task> tasks = this.findTasks(tenantId,new UserTaskQuery().taskId(taskId));
    if (tasks.isEmpty()) {
      return null;
    }
    Task task = tasks.get(0);
    return task;
  }

  public List<Task> findTasks(String tenantId, UserTaskQuery taskQuery) {
    return taskStore.findTasks(tenantId,taskQuery);
  }


  private boolean mayComplete(Task task, String userId, String actionType) {
    boolean canComplete = true;
    //会签
    if (WorkflowConstants.UserTaskType.ALL.equals(task.getTaskType())) {
      // allPassType = 0 如果第一个人执行了reject，则流程就终止，会签的其他人不需要执行。
      if (WorkflowConstants.Action.REJECT.equals(actionType) && (task.getAllPassType() == null || task.getAllPassType() == 0)) {
        return canComplete;
      }
      List<String> assigneeIds = task.getAssigneeIds() == null ? new ArrayList() : task.getAssigneeIds();
      if (assigneeIds.size() < task.getCandidateIds().size()) {
        canComplete = false;
      }
    }


    return canComplete;
  }

  //会签结果: all agree -> agree; one reject -> reject
  private String getCountersignatureResult(List<ApprovalOpinion> opinionList) {
    String result = WorkflowConstants.Action.AGREE;
    for (ApprovalOpinion opinion : opinionList) {
      if (WorkflowConstants.Action.REJECT.equals(opinion.getActionType())) {
        result = WorkflowConstants.Action.REJECT;
        break;
      }
    }
    return result;
  }

  /**
   * todo 执行task后动作
   */
  @SuppressWarnings("unchecked")
  private Map<String, Object> executeAfterAction(CompleteTaskArg arg) {
    try {
      Class<?> afterActionStandard = Class.forName("com.facishare.paas.workflow.kernel.support.AfterActionSupport");
      Method beforeMethod = afterActionStandard.getMethod("actionTaskAfterAction",CompleteTaskArg.class);
      Map<String, Object> map = (Map<String, Object>) beforeMethod.invoke(afterActionStandard.newInstance(),arg);
      return map;
    } catch (Exception e) {
      throw new RuntimeException("user task after action error", e);
    }
  }

  public Task stageBackCreate(String tenantId, String fromStageTaskId, String toActivityId) {
    Task task = this.findTaskById(tenantId, fromStageTaskId);
    Message message = new Message(tenantId).workflowInstanceId(new WorkflowInstanceId(task.getWorkflowInstanceId())).activityInstanceId(task.getActivityInstanceId());
    message.data(WorkflowConstants.TO_ACTIVITY_ID, toActivityId);
    workflowEngine.send(message);
    return task;
  }

  public String nextTask(String tenantId, String workflowInstanceId, String activityInstanceId) {
    Message message = new Message(tenantId)
            .workflowInstanceId(new WorkflowInstanceId(workflowInstanceId)).activityInstanceId(activityInstanceId);
    List<ActivityInstance> activityInstances = workflowEngine.send(message).getActivityInstances();
    return activityInstances.get(activityInstances.size()-1).getId();
  }
}
