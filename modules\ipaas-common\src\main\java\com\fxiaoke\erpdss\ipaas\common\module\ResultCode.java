package com.fxiaoke.erpdss.ipaas.common.module;

import com.fxiaoke.erpdss.ipaas.common.i18n.I18nBase;
import lombok.Getter;
import lombok.ToString;


/**
 * 错误码枚举
 * cs标识：s服务端
 * 状态分配：1：成功，2：警告，3：错误
 * 业务分配：06  ：fs-open开平
 * 模块：暂无分配，暂时使用24
 * 状态码：四位
 *
 * <AUTHOR> (^_−)☆
 */

@ToString
public enum ResultCode implements I18nBase {
    SUCCESS("s106240000", "成功"),


    PARAM_ILLEGAL("s206240000", "参数不合法"),


    SYSTEM_ERROR("s306240000", "系统异常"),
    BIZ_ERROR("s306240001", "业务异常"),


    ;
    @Getter
    private final String code;
    private final String defaultMsg;

    ResultCode(String code, String defaultMsg) {
        this.code = code;
        this.defaultMsg = defaultMsg;
    }

    public static boolean isSuccess(String code) {
        return code != null && code.startsWith("s1");
    }

    @Override
    public String _GetI18nKey() {
        return commonI18nKeyPrefix + ".resultCode." + name();
    }

    @Override
    public String _GetDefaultMsg() {
        return defaultMsg;
    }

    public <T> Result<T> result(Object... args) {
        return Result.error(code, getI18nMsg(args));
    }
}
