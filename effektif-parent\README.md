[![Quality Gate Status](https://oss.firstshare.cn/sonarqube/api/project_badges/measure?project=com.effektif%3Aeffektif-parent&metric=alert_status&token=sqb_3490349ac9871aab5a04e3ca946bf66a213e5af2)](https://oss.firstshare.cn/sonarqube/dashboard?id=com.effektif%3Aeffektif-parent)
[![Bugs](https://oss.firstshare.cn/sonarqube/api/project_badges/measure?project=com.effektif%3Aeffektif-parent&metric=bugs&token=sqb_3490349ac9871aab5a04e3ca946bf66a213e5af2)](https://oss.firstshare.cn/sonarqube/dashboard?id=com.effektif%3Aeffektif-parent)
[![Coverage](https://oss.firstshare.cn/sonarqube/api/project_badges/measure?project=com.effektif%3Aeffektif-parent&metric=coverage&token=sqb_3490349ac9871aab5a04e3ca946bf66a213e5af2)](https://oss.firstshare.cn/sonarqube/dashboard?id=com.effektif%3Aeffektif-parent)
[![Duplicated Lines (%)](https://oss.firstshare.cn/sonarqube/api/project_badges/measure?project=com.effektif%3Aeffektif-parent&metric=duplicated_lines_density&token=sqb_3490349ac9871aab5a04e3ca946bf66a213e5af2)](https://oss.firstshare.cn/sonarqube/dashboard?id=com.effektif%3Aeffektif-parent)


## 9.0.0 - 2024.4.01
* 并行分支内支持审批节点

## 8.9.0 - 2024.1.10
* 审批流程支持子流程节点

## 8.0.0 - 2022.2.15
* 分支条件 工作流支持变更前和变更后和变更时  

## 6.6.0 - 2019.03.11
* 增加AssigneeType - level_loop
* 增加ExecuteType - trigger_stage
       
## 6.6.0 - 2019.03.13
* 增加-加签时，节点的相关状态
* 增加-加签时，相关字段

## 6.6.0 - 2019.03.21
* opinion 增加 toTaskName
        

# release history
## 6.5.2.1 - 2019.03.07
*   修改ExecutionPojo，添加requestId field

# Effektif doc

[![Gitter](https://badges.gitter.im/Join%20Chat.svg)](https://gitter.im/effektif/effektif?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge) [![Build Status](https://travis-ci.org/effektif/effektif.svg?branch=master)](https://travis-ci.org/effektif/effektif)

# Effektif

Effektif is a flexible workflow engine that you can embed into your own applications.
Developing business applications with a worfklow engine like Effektif
saves time and energy that you can spend on cool features for your own application.

# Documentation

### [The Effektif Wiki](https://github.com/effektif/effektif/wiki)

