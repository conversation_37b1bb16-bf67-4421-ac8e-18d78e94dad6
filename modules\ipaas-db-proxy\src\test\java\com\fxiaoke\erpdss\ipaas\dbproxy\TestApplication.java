package com.fxiaoke.erpdss.ipaas.dbproxy;

import com.fxiaoke.erpdss.ipaas.IPaaS;
import com.fxiaoke.erpdss.ipaas.dbproxy.dao.ErpIPaaSMongoDao;
import com.github.mongo.support.TenantPolicy;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

/**
 * <AUTHOR> (^_−)☆
 */
@Testcontainers
@SpringBootApplication(scanBasePackageClasses = IPaaS.class)
public class TestApplication {
    @Container
    static MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:4.0.21").withExposedPorts(27017);

    @Bean
    public TenantPolicy mongoTenantPolicy() {
        mongoDBContainer.start();
        return tenantId -> mongoDBContainer.getReplicaSetUrl(ErpIPaaSMongoDao.DB_NAME);
    }
}
