<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 使用Spring属性来区分不同应用 -->
    <springProperty scope="context" name="appName" source="spring.application.name" defaultValue="application"/>
    <springProperty scope="context" name="logPath" source="logging.path" defaultValue="${catalina.home:-.}/logs"/>
    <!--不包含类名和行号%class{36}:%line，因为其性能开销大，如果有需要。再增加动态开关控制的不同pattern-->
    <property name="LOG_PATTERN" value="%d{HH:mm:ss.SSS} [%thread] %-5level ${appName} %logger{36} - %msg%n%rEx{full,
                java.lang
                }%n"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 文件输出 没有单独的ERROR_LOG 反正我是基本没从容器看过~~~ -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 使用应用名区分不同日志文件 -->
        <file>${logPath}/fs-app.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/${appName}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>500MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>8GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 异步输出 -->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="FILE"/>
        <!--影响性能 咱不使用-->
        <!--<includeCallerData>true</includeCallerData>-->
    </appender>

    <!-- 开发环境配置 -->
    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_FILE"/>
        </root>
    </springProfile>

    <!-- 生产环境配置 -->
    <springProfile name="prod">
        <root level="WARN">
            <appender-ref ref="ASYNC_FILE"/>
        </root>
    </springProfile>
</configuration>
