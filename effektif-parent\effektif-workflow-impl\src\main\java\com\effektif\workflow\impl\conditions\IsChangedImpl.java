package com.effektif.workflow.impl.conditions;

import com.effektif.workflow.api.condition.Condition;
import com.effektif.workflow.api.condition.IsChanged;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.impl.workflowinstance.ScopeInstanceImpl;

import java.util.Map;
import java.util.Objects;

/**
 * @Description :
 * <AUTHOR> cuiyongxu
 * @Date : 2022/3/2-12:16 PM
 **/
public class IsChangedImpl extends SingleBindingConditionImpl<IsChanged> {


    @Override
    public Class<? extends Condition> getApiType() {
        return IsChanged.class;
    }


    @Override
    public boolean eval(ScopeInstanceImpl scopeInstance) {
        Map changedData = (Map) getTriggerData(scopeInstance, "triggerData");
        if (changedData == null || changedData.size() == 0) {
            return eval(Boolean.FALSE);
        }
        Map data = (Map) changedData.get("data");
        if (data == null || data.size() == 0) {
            return eval(Boolean.FALSE);
        }
        //approvalflow  triggerData.data triggerData.callbackData
        //workflow  triggerData.data.before triggerData.data.after
        String type = scopeInstance.getWorkflow().getType();

        if (WorkflowConstants.WorkflowType.WORKFLOW.equals(type)) {
            Map beforeData = (Map) data.get("before");
            if (beforeData == null || beforeData.size() == 0) {
                return eval(Boolean.FALSE);
            }
            if (beforeData.containsKey(this.left.expression.variableId)) {
                return eval(Boolean.TRUE);
            }
            return eval(Boolean.FALSE);
        } else if (WorkflowConstants.WorkflowType.APPROVAL_FLOW.equals(type)) {
            if (data.containsKey(this.left.expression.variableId)) {
                return eval(Boolean.TRUE);
            }
            return eval(Boolean.FALSE);
        }
        return eval(Boolean.FALSE);
    }

    @Override
    protected boolean eval(Object value) {
        return (boolean) value;
    }

    private Object getTriggerData(ScopeInstanceImpl scopeInstance, String key) {
        Object value = null;
        Map<String, Object> properties = scopeInstance.getWorkflowInstance().getProperties();
        if (Objects.nonNull(properties)) {
            value = properties.get(key);
        }

        if (Objects.isNull(value)) {
            Map<String, Object> transientProperties = scopeInstance.getWorkflowInstance().getTransientProperties();
            if (Objects.nonNull(transientProperties)) {
                value = transientProperties.get(key);
            }
        }
        return value;
    }


    @Override
    public String getComparatorSymbol() {
        return "isChanged";
    }

}
