package com.effektif.workflow.api.condition;

import com.effektif.workflow.api.bpmn.BpmnElement;
import com.effektif.workflow.api.json.TypeName;
import com.effektif.workflow.api.workflow.Binding;

/**
 * 2022年03月02日12:34:06
 * cuiyongxu
 */
@TypeName("isChanged")
@BpmnElement("isChanged")
public class IsChanged extends SingleBindingCondition {

    private static final long serialVersionUID = 2366200536784353330L;

    @Override
    public String toString() {
        return "(" + toString(left) + " is changed)";
    }

    @Override
    public IsChanged left(Binding<?> left) {
        super.left(left);
        return this;
    }

}
