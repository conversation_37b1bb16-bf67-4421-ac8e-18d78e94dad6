package com.effektif.workflow.api.condition;

import com.effektif.workflow.api.bpmn.BpmnElement;
import com.effektif.workflow.api.json.TypeName;

/**
 * 当前左侧的值 是否 在 右侧的字符串中
 * 如； 中国 inStrings "中国;上海;北京" 结果为 true
 */
@TypeName("inStrings")
@BpmnElement("inStrings")
public class InStrings extends Comparator {
    private static final long serialVersionUID = -1737672885372582159L;

    @Override protected String getName() {
        return "inStrings";
    }
}
