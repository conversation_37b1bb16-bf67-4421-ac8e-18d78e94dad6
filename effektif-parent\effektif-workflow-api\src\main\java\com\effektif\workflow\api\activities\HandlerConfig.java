package com.effektif.workflow.api.activities;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/29
 * @description
 */
public class HandlerConfig {

    //任务处理人
    protected Map<String, List<String>> assignee;
    //优先级
    protected int order;
    //条件
    protected Object rule;
    //条件名称
    protected String name;
    //分组id
    protected String id;
    //是否是默认的配置
    protected boolean defaultConfig;

    public Map<String, List<String>> getAssignee() {
        return assignee;
    }

    public void setAssignee(Map<String, List<String>> assignee) {
        this.assignee = assignee;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public Object getRule() {
        return rule;
    }

    public void setRule(Object rule) {
        this.rule = rule;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean isDefaultConfig() {
        return defaultConfig;
    }

    public void setDefaultConfig(boolean defaultConfig) {
        this.defaultConfig = defaultConfig;
    }
}
