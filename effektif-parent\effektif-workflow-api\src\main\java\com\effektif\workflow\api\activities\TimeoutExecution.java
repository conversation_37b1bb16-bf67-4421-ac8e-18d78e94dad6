package com.effektif.workflow.api.activities;

/**
 * 超时执行项
 * <AUTHOR>
 * @date 2021年10月09日16:11:56
 */
public class TimeoutExecution extends Execution {

    private Integer time;
    private Integer timeUnit;


    public Integer getTime() {
        return time;
    }

    public void setTime(Integer time) {
        this.time = time;
    }

    public Integer getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(Integer timeUnit) {
        this.timeUnit = timeUnit;
    }

}
