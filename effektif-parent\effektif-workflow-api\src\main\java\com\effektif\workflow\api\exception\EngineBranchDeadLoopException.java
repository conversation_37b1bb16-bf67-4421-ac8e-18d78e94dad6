package com.effektif.workflow.api.exception;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @creat_date: 2021/9/22
 * @creat_time: 15:17
 * @since 7.5.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EngineBranchDeadLoopException extends EngineException{
  String name;
  String exclusiveGatewayId;
  String sourceWorkflowId;
  String workflowId;
  String workflowName;
  String workflowInstanceId;
  long loopCount;

  @Override
  public String toString() {
    return "{" +
            "name:'" + name + '\'' +
            ", exclusiveGatewayId:'" + exclusiveGatewayId + '\'' +
            ", sourceWorkflowId:'" + sourceWorkflowId + '\'' +
            ", workflowId:'" + workflowId + '\'' +
            ", workflowName:'" + workflowName + '\'' +
            ", workflowInstanceId:'" + workflowInstanceId + '\'' +
            ", loopCount:'" + loopCount + '\'' +
            '}';
  }
}
