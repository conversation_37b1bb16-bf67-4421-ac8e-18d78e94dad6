package com.effektif.workflow.impl.conditions;

import com.effektif.workflow.api.condition.Condition;
import com.effektif.workflow.api.condition.NotInStrings;
import com.effektif.workflow.impl.data.TypedValueImpl;

public class NotInStringsImpl extends InStringsImpl {
    @Override
    public Class<? extends Condition> getApiType() {
        return NotInStrings.class;
    }

    @Override
    public boolean compare(TypedValueImpl leftValue, TypedValueImpl rightValue) {
        return !super.compare(leftValue, rightValue);
    }

    @Override
    public String getComparatorSymbol() {
        return "<notInStrings>";
    }
}
