/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.impl.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
public class Lists {

  @SafeVarargs
  public static <T> List<T> of(T... elements) {
    // Arrays.asList produces unmodifyable lists
    // Sometimes we need mofyable lists
    return new ArrayList<T>(Arrays.asList(elements));
  }

  
  public static <T> void collect(Collection<T> collector, T element) {
    if (element!=null) {
      collector.add(element);
    }
  }
  public static <T> void collect(Collection<T> collector, Collection<T> elements) {
    if (elements!=null) {
      for (T element: elements) {
        if (element!=null) {
          collector.add(element);
        }
      }
    }
  }


  public static <K,V> List<V> getValues(List<K> keys, Map<K,V> map) {
    if (keys==null
        || keys.isEmpty()) {
      return null;
    }
    List<V> values = new ArrayList<>(keys.size());
    for (K key: keys) {
      V value = map.get(key);
      if (value!=null) {
        values.add(value);
      } 
    }
    return values;
  }

  public static <K,V> V getValue(K key, Map<K,V> map) {
    if (key==null) {
      return null;
    }
    return map.get(key);
  }
}
