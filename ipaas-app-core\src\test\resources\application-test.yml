# 触发器测试环境配置

spring:
  # 禁用数据库自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# 服务器配置
server:
  port: 8081

# 日志配置
logging:
  level:
    com.fxiaoke.erpdss.ipaas.base: DEBUG
    org.quartz: WARN
    org.springframework.web: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Quartz配置
spring:
  quartz:
    job-store-type: memory
    properties:
      org:
        quartz:
          scheduler:
            instanceName: TestScheduler
            instanceId: AUTO
          threadPool:
            threadCount: 5
