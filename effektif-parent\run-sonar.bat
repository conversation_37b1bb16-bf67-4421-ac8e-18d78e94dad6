@echo off
setlocal

REM 设置临时 JAVA_HOME 变量，可以根据需要进行设置
set JAVA_HOME_TEMP=C:\Program Files\Java\jdk-11.0.8

REM 设置项目键和 SonarQube 登录令牌
set projectKey=com.effektif:effektif-parent
set sonarLogin=squ_02db0ac7ddbf2257d36692b3ae54d2571a4bcacc

REM 如果 JAVA_HOME_TEMP 未设置或为空，则使用系统环境变量中的 JAVA_HOME
if "%JAVA_HOME_TEMP%"=="" (
    set JAVA_HOME=%JAVA_HOME%
) else (
    set JAVA_HOME=%JAVA_HOME_TEMP%
)

echo "%JAVA_HOME%"
echo "%M2_HOME%"

REM 显示 Maven 版本
call "%M2_HOME%\bin\mvn.cmd -v"

REM 执行 Maven 命令
call "%M2_HOME%\bin\mvn.cmd clean verify sonar:sonar -Dsonar.projectKey=%projectKey% -Dsonar.host.url=https://oss.firstshare.cn/sonarqube -Dsonar.login=%sonarLogin%"

endlocal
