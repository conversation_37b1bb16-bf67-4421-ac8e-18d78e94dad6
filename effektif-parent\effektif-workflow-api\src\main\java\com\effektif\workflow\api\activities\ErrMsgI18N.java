package com.effektif.workflow.api.activities;

import java.io.Serializable;
import java.util.List;

/**
 * 超时执行项
 *
 * <AUTHOR>
 * @date 2021年10月09日16:11:56
 */
public class ErrMsgI18N implements Serializable {

    private static final long serialVersionUID = 829161159969241846L;
    /**
     * i18nkey
     */
    private String key;
    //参数,多为渲染后的,例如员工:张三
    private List<Object> params;

    //未渲染的数据,例如员工:1007
    private List<Object> values;


    public static ErrMsgI18N create(String key) {
        return create(key, null, null);
    }

    public static ErrMsgI18N create(String key, List<Object> params, List<Object> values) {
        ErrMsgI18N errMsgI18N = new ErrMsgI18N();
        errMsgI18N.setKey(key);
        errMsgI18N.setParams(params);
        errMsgI18N.setValues(values);
        return errMsgI18N;
    }


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<Object> getParams() {
        return params;
    }

    public void setParams(List<Object> params) {
        this.params = params;
    }

    public List<Object> getValues() {
        return values;
    }

    public void setValues(List<Object> values) {
        this.values = values;
    }
}
