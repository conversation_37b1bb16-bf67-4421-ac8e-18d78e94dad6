package com.effektif.workflow.impl.memory;

import com.effektif.workflow.api.model.Id;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.ext.*;

import java.util.*;


public class MemorySubProcessTaskStore implements SubProcessTaskStore {

    protected int nextId = 1;
    protected Map<Id, SubProcessTask> tasks = Collections.synchronizedMap(new LinkedHashMap<Id, SubProcessTask>());

    @Override
    public TaskId generateTaskId() {
        return new TaskId(Integer.toString(nextId++));
    }

    @Override
    public void insertTask(SubProcessTask task) {
        if (task.getId() == null) {
            String taskId = Integer.toString(nextId++);
            task.setId(new TaskId(taskId));
        }
        task.setModifyTime(System.currentTimeMillis());
        tasks.put(task.getId(), task);
    }


    @Override
    public SubProcessTask updateState(String tenantId, String superWorkflowInstanceId, String subProcessInstanceId, String actionType, Long end) {
        for (SubProcessTask value : tasks.values()) {
            if(Objects.equals(value.getSubWorkflowInstanceId(),subProcessInstanceId)){
                value.setEndTime(end);
                value.setState(actionType);
                return value;
            }
        }
        return null;
    }

    @Override
    public SubProcessTask findById(String tenantId, String taskId) {
        return tasks.get(new TaskId(taskId));
    }


}
