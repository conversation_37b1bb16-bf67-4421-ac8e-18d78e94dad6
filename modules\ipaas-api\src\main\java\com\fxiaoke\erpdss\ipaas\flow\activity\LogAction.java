package com.fxiaoke.erpdss.ipaas.flow.activity;

import com.effektif.workflow.api.bpmn.BpmnElement;
import com.effektif.workflow.api.bpmn.BpmnTypeAttribute;
import com.effektif.workflow.api.json.TypeName;
import com.effektif.workflow.api.workflow.Binding;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

/**
 * 日志组件
 *
 * <AUTHOR> (^_−)☆
 */
@NoArgsConstructor
@Accessors(fluent = true)
@TypeName("logAction")
@BpmnElement("logAction")
@BpmnTypeAttribute(attribute="type", value="log")
public class LogAction extends AbsAction<LogAction> {

    @Setter
    @Getter
    protected List<Binding<String>> logBindings;

    @Serial
    private static final long serialVersionUID = 7030979468129620604L;
    private boolean enabled;

    public LogAction enable() {
        this.enabled = true;
        return this;
    }

    public LogAction disable() {
        this.enabled = false;
        return this;
    }

    public LogAction addLogBinding(Binding<String> logBinding) {
        if (this.logBindings == null) {
            this.logBindings = new ArrayList<>();
        }
        this.logBindings.add(logBinding);
        return this;
    }

    public LogAction logValue(String value) {
        return addLogBinding(new Binding<String>().value(value));
    }

    public LogAction logExpression(String expression) {
        return addLogBinding(new Binding<String>().expression(expression));
    }

    public LogAction logTemplate(String template) {
        return addLogBinding(new Binding<String>().template(template));
    }
}
