package com.fxiaoke.erpdss.ipaas.common.module;

import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一Result；<br/>
 * 内部不处理多语，msg应该是多语转换后放进来。
 *
 * <AUTHOR> (^_−)☆
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Result<T> {

    /**
     * 状态码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 时间戳
     */
    @Builder.Default
    private long timestamp = System.currentTimeMillis();

    // ==================== 成功响应 ====================

    /**
     * 成功响应（无数据）
     */
    public static <T> Result<T> success() {
        return success(null, ResultCode.SUCCESS.getI18nMsg());
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> Result<T> success(T data) {
        return success(data, ResultCode.SUCCESS.getI18nMsg());
    }

    /**
     * 成功响应（带数据和消息）
     */
    public static <T> Result<T> success(T data, String message) {
        return Result.<T>builder()
                .code(ResultCode.SUCCESS.getCode())
                .message(message)
                .data(data)
                .build();
    }

    // ==================== 失败响应 ====================

    /**
     * 失败响应（使用默认失败状态码）
     */
    public static <T> Result<T> error() {
        return error(ResultCode.SYSTEM_ERROR.getI18nMsg());
    }

    /**
     * 失败响应（带消息）
     */
    public static <T> Result<T> error(String message) {
        return error(ResultCode.SYSTEM_ERROR.getCode(), message);
    }

    /**
     * 失败响应（带状态码和消息）
     */
    public static <T> Result<T> error(String code, String message) {
        return Result.<T>builder()
                .code(code)
                .message(message)
                .build();
    }

    /**
     * 失败响应（使用ResultCode枚举）
     */
    public static <T> Result<T> error(ResultCode resultCode) {
        return error(resultCode.getCode(), resultCode.getI18nMsg());
    }

    /**
     * 失败响应（使用ResultCode枚举，带自定义消息）
     */
    public static <T> Result<T> error(ResultCode resultCode, String message) {
        return error(resultCode.getCode(), message);
    }

    // ==================== 异常响应 ====================

    /**
     * 异常响应
     */
    public static <T> Result<T> error(IPaaSException e) {
        return error(e.getCode(), e.getMessage());
    }


    // ==================== 判断方法 ====================

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return ResultCode.isSuccess(code);
    }

    /**
     * 是否失败
     */
    public boolean isFailed() {
        return !isSuccess();
    }
}