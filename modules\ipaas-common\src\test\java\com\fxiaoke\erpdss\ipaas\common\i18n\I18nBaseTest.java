package com.fxiaoke.erpdss.ipaas.common.i18n;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.function.BiFunction;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * I18nBase 单元测试
 * 
 * <AUTHOR> (^_−)☆
 */
class I18nBaseTest {

    private BiFunction<String, String, String> originalFunction;

    /**
     * 测试实现类
     */
    private static class TestI18nBase implements I18nBase {
        private final String key;
        private final String defaultMsg;

        public TestI18nBase(String key, String defaultMsg) {
            this.key = key;
            this.defaultMsg = defaultMsg;
        }

        @Override
        public String _GetI18nKey() {
            return key;
        }

        @Override
        public String _GetDefaultMsg() {
            return defaultMsg;
        }
    }

    @BeforeEach
    void setUp() {
        // 保存原始的i18n函数
        originalFunction = getI18nFunction();
    }

    @AfterEach
    void tearDown() {
        // 恢复原始的i18n函数
        I18nUtil._SetI18nFunction(originalFunction);
    }

    @Test
    void testCommonI18nKeyPrefix() {
        // When & Then
        assertThat(I18nBase.commonI18nKeyPrefix).isEqualTo("erpdss.ipaas");
    }

    @Test
    void testGetI18nMsgWithDefaultFunction() {
        // Given
        String key = "test.key";
        String defaultMsg = "默认消息";
        TestI18nBase testBase = new TestI18nBase(key, defaultMsg);

        // When
        String result = testBase.getI18nMsg();

        // Then
        assertThat(result).isEqualTo(defaultMsg);
    }

    @Test
    void testGetI18nMsgWithCustomFunction() {
        // Given
        String key = "test.key";
        String defaultMsg = "默认消息";
        String i18nMsg = "国际化消息";
        TestI18nBase testBase = new TestI18nBase(key, defaultMsg);
        
        BiFunction<String, String, String> customFunction = (k, def) -> {
            if (key.equals(k)) {
                return i18nMsg;
            }
            return def;
        };
        I18nUtil._SetI18nFunction(customFunction);

        // When
        String result = testBase.getI18nMsg();

        // Then
        assertThat(result).isEqualTo(i18nMsg);
    }

    @Test
    void testGetI18nMsgWithArgs() {
        // Given
        String key = "test.key";
        String defaultMsg = "Hello {0}, welcome to {1}!";
        TestI18nBase testBase = new TestI18nBase(key, defaultMsg);
        Object[] args = {"张三", "iPaaS系统"};

        // When
        String result = testBase.getI18nMsg(args);

        // Then
        assertThat(result).isEqualTo("Hello 张三, welcome to iPaaS系统!");
    }

    @Test
    void testGetI18nMsgWithCustomFunctionAndArgs() {
        // Given
        String key = "test.key";
        String defaultMsg = "Hello {0}!";
        String i18nMsg = "你好 {0}!";
        TestI18nBase testBase = new TestI18nBase(key, i18nMsg);
        Object[] args = {"世界"};
        
        BiFunction<String, String, String> customFunction = (k, def) -> {
            if (key.equals(k)) {
                return i18nMsg;
            }
            return def;
        };
        I18nUtil._SetI18nFunction(customFunction);

        // When
        String result = testBase.getI18nMsg(args);

        // Then
        assertThat(result).isEqualTo("你好 世界!");
    }

    @Test
    void testGetI18nMsgWithNullKey() {
        // Given
        String key = null;
        String defaultMsg = "默认消息";
        TestI18nBase testBase = new TestI18nBase(key, defaultMsg);

        // When
        String result = testBase.getI18nMsg();

        // Then
        assertThat(result).isEqualTo(defaultMsg);
    }

    @Test
    void testGetI18nMsgWithEmptyKey() {
        // Given
        String key = "";
        String defaultMsg = "默认消息";
        TestI18nBase testBase = new TestI18nBase(key, defaultMsg);

        // When
        String result = testBase.getI18nMsg();

        // Then
        assertThat(result).isEqualTo(defaultMsg);
    }

    @Test
    void testGetI18nMsgWithNullDefaultMsg() {
        // Given
        String key = "test.key";
        String defaultMsg = null;
        TestI18nBase testBase = new TestI18nBase(key, defaultMsg);

        // When
        String result = testBase.getI18nMsg();

        // Then
        assertThat(result).isNull();
    }

    @Test
    void testGetI18nMsgWithNullArgs() {
        // Given
        String key = "test.key";
        String defaultMsg = "默认消息";
        TestI18nBase testBase = new TestI18nBase(key, defaultMsg);

        // When
        String result = testBase.getI18nMsg((Object[]) null);

        // Then
        assertThat(result).isEqualTo(defaultMsg);
    }

    @Test
    void testGetI18nMsgWithEmptyArgs() {
        // Given
        String key = "test.key";
        String defaultMsg = "默认消息";
        TestI18nBase testBase = new TestI18nBase(key, defaultMsg);

        // When
        String result = testBase.getI18nMsg(new Object[]{});

        // Then
        assertThat(result).isEqualTo(defaultMsg);
    }

    @Test
    void testMultipleArgsFormatting() {
        // Given
        String key = "test.key";
        String defaultMsg = "用户 {0} 在 {1} 执行了 {2} 操作";
        TestI18nBase testBase = new TestI18nBase(key, defaultMsg);
        Object[] args = {"张三", "2023-12-01", "登录"};

        // When
        String result = testBase.getI18nMsg(args);

        // Then
        assertThat(result).isEqualTo("用户 张三 在 2023-12-01 执行了 登录 操作");
    }

    /**
     * 通过反射获取当前的i18n函数（用于测试）
     */
    private BiFunction<String, String, String> getI18nFunction() {
        try {
            var field = I18nUtil.class.getDeclaredField("i18nFunction");
            field.setAccessible(true);
            return (BiFunction<String, String, String>) field.get(null);
        } catch (Exception e) {
            // 如果获取失败，返回默认函数
            return (i18nKey, defaultValue) -> defaultValue;
        }
    }
}
