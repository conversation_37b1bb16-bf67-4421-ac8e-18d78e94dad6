# FS-ERP-iPaaS 集成平台

> 本文档存在uml图，需要安装相应插件显示

## 项目概述

FS-ERP-iPaaS 是纷享销客（FXiaoKe）的企业级集成平台即服务（iPaaS）项目，基于 Spring Boot 架构，提供企业应用间的数据集成和系统连接能力。

## 技术栈

- **Java**: 21
- **框架**: Spring Boot
- **构建工具**: Maven

## 项目结构

```
fs-erp-ipaas/
├── pom.xml                   # 根项目配置文件
├── README.md                 # 项目说明文档
├── docs/                     # 项目文档
│   ├── testing-standards.md # 测试规范
│   ├── git-commit-standards.md # Git提交规范
│   └── java-coding-standards.md # Java编码规范
├── common/                   # 公共模块目录
│   ├── ipaas-common/         # 基础公共模块
│   ├── ipaas-api/            # API定义模块
│   └── ipaas-spring-common/  # Spring公共模块
├── ipaas-core/               # 平台基座
├── ipaas-web/                # Web服务模块
├── ipaas-flow-engine/        # 流程引擎模块
├── ipaas-app-all/            # 聚合部署模块
└── modules/                  # 功能模块目录
    ├── ipaas-db-proxy/       # 数据库代理模块（规划中）
    ├── ipaas-connector-proxy/ # 连接器代理模块（规划中）
    ├── ipaas-limiter/        # 限流控制模块（规划中）
    ├── ipaas-log/            # 日志管理模块（规划中）
    ├── ipaas-monitor/        # 监控模块（规划中）
    ├── ipaas-quota/          # 配额管理模块（规划中）
    └── ipaas-template/       # 应用模板模块（规划中）
```

### 模块依赖关系

```plantuml
@startuml
skinparam componentStyle rectangle
' 直线
skinparam linetype ortho
' 使用自上而下的布局
left to right direction
 
' 应用层
package "应用层" as apps #d0ebff {
  [ipaas-app-all（应用）] as appAll
  [ipaas-web（应用）] as web
  [ipaas-core（应用）] as base
  [ipaas-flow-engine（引擎）] as flowEngine
}
 
' 功能模块
package "功能模块" as modules #d3f9d8 {
  [ipaas-db-proxy] as dbProxy
  [ipaas-connector-proxy] as connectorProxy
  [ipaas-limiter] as limiter
  [ipaas-log] as log
  [ipaas-monitor] as monitor
  [ipaas-quota] as quota
  [ipaas-template] as template
}
 
' 公共模块
package "公共模块" as commons #ffe3e3 {
  [ipaas-common] as common
  [ipaas-api] as api
  [ipaas-spring-common] as springCommon
}
 
' 应用层内部依赖 - 使用不同颜色
appAll -[#blue]-> web
appAll -[#blue]-> base
web -[#blue]-> flowEngine
base -[#blue]-> flowEngine
 
' 核心应用依赖 - 使用不同样式和颜色
apps .[#red].> api
apps .[#green].> dbProxy
apps .[#red].> springCommon
 
' 公共模块依赖 - 使用虚线
springCommon -[#red]-> common
api -[#red]-> common
dbProxy .[#red].> common
 
@enduml

```
imgurl:https://img.plantuml.biz/plantuml/png/TLJFRzCm6Br7olzWjGTEcP1wWSiqXBWZSQnwS9elcJN73echKQ594wN0nlWX62064uDJXRWKWQYcZlupTPkTzYzWnsddjkFclFS-Vz_x_1JNsSW4JaKhfgwJRH6Mu1J7gCdZX3DWufveKq0fD0Lc4GNNkOQcdtvDJqUscX86efq0ugdOv5epFXjD3tlvq-_ZmNw-Up0UF9yUFPQ3ZkplkWw5K231KKgYJO42ecyddBaEqgNoz52LoluZrqbmSmj7W2fph89mXd2IPAWQN0S_3D53LORg9C4uynJXOKelXizCmSLmhr4MtABK4cw3lo9IcANmSGOh4WrQcf3oRGzOfDnhwV3TvEtBcLHpTmhATNPcplQ_v9spIU-hFFvWcxFWmb_CWnO5RR4MtWnkB5WCV2z9-ODsqIJmx-gpHJSvOsgRFBLKSsnPJ4bC1AI5g3pRB8yCmoCBZJaZwgg6cRCuM-pz5XUuu8gJnGY84ue55EJimrfDzuVizbTNOy4LuoLMNTLgmX1gK5jOZU4QbxA5P90o2SH2ioGbBB8B3NAxBDzPZgHyqZtlzCP_Z_FVxv5NfdmyU25V7vn_-vpl_NGTapNarQi-RK73MpV1MeLDc1HfWtPijE1_N37Mv6GWHnqpt9KpJKx-oE4h-SOUBaDhzMeAGMDjtMnZXaKf0DFeF5LBwiNTh3pRqWJvnoFpbx2gj1jzcJ8pUo4rmXLeEOBLl7dPTWDOe7vN_m00

## 模块详细说明

### 公共模块 (common)

#### ipaas-common
- **功能**: 基础公共组件和工具类
- **特点**: 轻量级，无重依赖（不依赖Spring等框架）
- **包含**:
  - 通用工具类和常量
  - 基础数据模型
  - 异常定义
  - 国际化支持
- **依赖**: Jackson、Hutool、SLF4J

#### ipaas-api
- **功能**: API接口定义模块
- **作用**: 定义系统间的接口契约
- **依赖**: ipaas-common

#### ipaas-spring-common
- **功能**: Spring相关的公共组件
- **作用**: 提供Spring Boot相关的公共配置和组件
- **包含**: 配置中心集成、Spring公共配置
- **依赖**: ipaas-common

### 核心服务模块

#### ipaas-core
- **功能**: 平台基座服务
- **主要类**: IPaaSBaseApp (Spring Boot 启动类)
- **接口**: `/base/check` - 健康检查接口
- **作用**: 提供平台基础服务和核心功能
- **依赖**: ipaas-spring-common

#### ipaas-web
- **功能**: Web服务层
- **主要类**: IPaaSWebApp (Spring Boot 启动类)
- **接口**: `/web/check` - 健康检查接口
- **作用**: 提供HTTP接口和Web服务
- **依赖**: ipaas-spring-common

#### ipaas-flow-engine
- **功能**: 流程引擎模块
- **技术栈**: 基于Effektif工作流引擎
- **作用**: 提供业务流程编排和执行能力
- **依赖**: Effektif工作流引擎、MongoDB

### 功能模块 (modules/) - 规划中

以下模块目前为空模块，处于规划阶段：

#### ipaas-db-proxy
- **功能**: 数据库代理服务（规划中）
- **作用**: 提供数据库连接代理和数据访问服务

#### ipaas-connector-proxy
- **功能**: 连接器代理服务（规划中）
- **作用**: 处理连接器请求并转发到实际的连接器应用

#### ipaas-limiter
- **功能**: 限流控制模块（规划中）
- **作用**: 提供API限流和流量控制功能

#### ipaas-log
- **功能**: 日志管理模块（规划中）
- **作用**: 统一日志收集、存储和查询

#### ipaas-monitor
- **功能**: 监控模块（规划中）
- **作用**: 系统监控、指标收集和告警

#### ipaas-quota
- **功能**: 配额管理模块（规划中）
- **作用**: 资源配额管理和使用量统计

#### ipaas-template
- **功能**: 应用模板模块（规划中）
- **作用**: 提供应用模板和快速部署能力

### 部署模块

#### ipaas-app-all
- **功能**: 应用聚合部署
- **作用**: 将核心模块打包成一个可部署的应用
- **包含**: ipaas-core + ipaas-web 模块
- **依赖**: ipaas-core、ipaas-web

## 快速开始

### 环境要求

- JDK 21+
- Maven 3.6+

### 构建项目

```bash
# 克隆项目
git clone https://git.firstshare.cn/fs-open/fs-erp-ipaas.git
cd fs-erp-ipaas

# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package
```

### 运行应用

```bash
# 运行基础服务（单独运行）
cd ipaas-core
mvn spring-boot:run

# 运行Web服务（单独运行）
cd ipaas-web
mvn spring-boot:run

# 运行完整应用（推荐 - 包含base和web服务）
cd ipaas-app-all
mvn spring-boot:run
```

### 验证部署

应用启动后，可以通过以下接口验证：

```bash
# 验证基础服务（如果单独运行ipaas-core）
curl http://localhost:8080/base/check

# 验证Web服务（如果单独运行ipaas-web）
curl http://localhost:8080/web/check

# 验证完整应用（如果运行ipaas-app-all）
curl http://localhost:8080/base/check
curl http://localhost:8080/web/check
```

## 配置说明

### 配置中心

- 引入了公司 cms-spring-cloud-starter 读取配置中心配置
- 配置统一通过 `ipaas-spring-common` 模块管理
- 配置中心文件名为：spring-cloud-ipaas-common
- 使用方法可参考 `com.fxiaoke.erpdss.ipaas.springcommon.properties.CommonProperties`
- 详细使用文档：https://wiki.firstshare.cn/pages/viewpage.action?pageId=209170055

### 公共配置

- **基础配置**: 通过 `ipaas-common` 模块提供轻量级公共组件
- **Spring配置**: 通过 `ipaas-spring-common` 模块的 `CommonProperties` 类管理Spring相关配置
- **API定义**: 通过 `ipaas-api` 模块定义系统接口契约

## 开发指南

### 📋 开发规范文档

为了确保代码质量和团队协作效率，项目提供了完整的开发规范文档：

- **[测试规范](./docs/testing-standards.md)** - 测试命名、技术栈和 CI 配置
- **[Git 提交规范](./docs/git-commit-standards.md)** - Git 提交消息和分支命名规范
- **[Java 编码规范](./docs/java-coding-standards.md)** - Java 代码编写规范

### 模块开发状态

#### 已开发模块
- ✅ **common/ipaas-common** - 基础公共组件（已完成，包含完整测试）
- ✅ **common/ipaas-api** - API接口定义
- ✅ **common/ipaas-spring-common** - Spring公共组件
- ✅ **ipaas-core** - 平台基座服务
- ✅ **ipaas-web** - Web服务层
- ✅ **ipaas-app-all** - 聚合部署模块

#### 开发中模块
- 🚧 **ipaas-flow-engine** - 流程引擎（基础框架已搭建）

#### 规划中模块
- 📋 **modules/ipaas-db-proxy** - 数据库代理服务
- 📋 **modules/ipaas-connector-proxy** - 连接器代理服务
- 📋 **modules/ipaas-limiter** - 限流控制模块
- 📋 **modules/ipaas-log** - 日志管理模块
- 📋 **modules/ipaas-monitor** - 监控模块
- 📋 **modules/ipaas-quota** - 配额管理模块
- 📋 **modules/ipaas-template** - 应用模板模块

## 部署说明

### 聚合部署（推荐）

使用 `ipaas-app-all` 模块进行单体应用部署，包含所有核心功能：

```bash
cd ipaas-app-all
mvn clean package
java -jar target/ipaas-app-all-2.0-SNAPSHOT.jar
```

### 分模块部署

各核心模块也可分别独立部署：

- **ipaas-core**: 基础服务（端口：8080）
- **ipaas-web**: Web服务（端口：8080）

> **注意**: 分模块部署时需要注意端口冲突，建议通过配置文件指定不同端口。

## 监控和运维

### 健康检查

- 基础服务健康检查：`/base/check`
- Web服务健康检查：`/web/check`

## 许可证

本项目采用企业内部许可证，仅供纷享销客内部使用。
