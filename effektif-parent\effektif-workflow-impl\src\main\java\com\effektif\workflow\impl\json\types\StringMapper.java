/* Copyright (c) 2014, Effektif GmbH.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License. */
package com.effektif.workflow.impl.json.types;

import com.effektif.workflow.impl.data.InvalidValueException;
import com.effektif.workflow.impl.json.*;

import java.lang.reflect.Type;


/**
 * Maps a {@link String} to a JSON string field for serialisation and deserialisation.
 *
 * <AUTHOR>
 */
public class StringMapper extends AbstractTypeMapper<String> implements JsonTypeMapperFactory {

  @Override
  public JsonTypeMapper createTypeMapper(Type type, Class< ? > clazz, Mappings mappings) {
    if (String.class==type) {
      return this;
    }
    return null;
  }

  @Override
  public void write(String objectValue, JsonWriter jsonWriter) {
    jsonWriter.writeString(objectValue);
  }

  @Override
  public String read(Object jsonValue, JsonReader jsonReader) {
    if (jsonValue instanceof Number) {
      return jsonValue.toString();
    }
    if (!String.class.isAssignableFrom(jsonValue.getClass())) {
      throw new InvalidValueException(String.format("Invalid text value ‘%s’ (%s)", jsonValue, jsonValue.getClass().getName()));
    }
    return (String) jsonValue;
  }
}
