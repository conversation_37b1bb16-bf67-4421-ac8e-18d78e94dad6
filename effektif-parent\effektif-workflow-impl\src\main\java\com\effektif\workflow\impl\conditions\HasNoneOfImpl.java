package com.effektif.workflow.impl.conditions;

import com.effektif.workflow.api.condition.Condition;
import com.effektif.workflow.api.condition.HasNoneOf;
import com.effektif.workflow.impl.data.TypedValueImpl;

import java.util.Collections;
import java.util.List;

/**
 * zhenghaibo
 * 2017/4/25 11:59
 */
public class HasNoneOfImpl extends ComparatorImpl {
    @Override
    public Class<? extends Condition> getApiType() {
        return HasNoneOf.class;
    }

    @Override
    public boolean compare(TypedValueImpl leftValue, TypedValueImpl rightValue) {
        if (isNull(leftValue) && isNull(rightValue))
            return false;
        if (isNotNull(leftValue) && isNull(rightValue))
            return true;
        if (isNull(leftValue) && isNotNull(rightValue))
            return true;
        Object left =  leftValue.value;
        List right = (List) rightValue.value;
        if(left instanceof String){
            return !right.contains(left);
        }
        if (left instanceof List){
            return Collections.disjoint((List)left, right);
        }
        return false;
    }

    @Override
    public String getComparatorSymbol() {
        return "<hasNoneOf>";
    }
}
