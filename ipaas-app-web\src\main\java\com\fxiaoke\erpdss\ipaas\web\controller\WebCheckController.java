package com.fxiaoke.erpdss.ipaas.web.controller;

import com.fxiaoke.erpdss.ipaas.springcommon.properties.CommonProperties;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (^_−)☆
 */
@RestController
@RequestMapping("/web")
public class WebCheckController {
    private final CommonProperties commonProperties;

    public WebCheckController(CommonProperties commonProperties) {
        this.commonProperties = commonProperties;
    }

    @GetMapping("/check")
    public String check() {
        String welcomeMessage = commonProperties.getWelcomeMessage();
        return (welcomeMessage == null ? "hello" : welcomeMessage) + ", erpdss web!";
    }
}
