${AnsiColor.CYAN}

  ______ _____   ______ _____  _____    _ _____             _____
 |  ____/ ____| |  ____|  __ \|  __ \  (_)  __ \           / ____|
 | |__ | (___   | |__  | |__) | |__) |  _| |__) |_ _  __ _| (___
 |  __| \___ \  |  __| |  _  /|  ___/  | |  ___/ _` |/ _` |\___ \
 | |    ____) | | |____| | \ \| |      | | |  | (_| | (_| |____) |
 |_|   |_____/  |______|_|  \_\_|      |_|_|   \__,_|\__,_|_____/


${AnsiColor.GREEN}:: ${spring.application.name} :: ${AnsiColor.DEFAULT} Use Spring Boot(v${spring-boot.version})
