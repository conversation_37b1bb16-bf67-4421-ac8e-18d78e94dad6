package com.effektif.workflow.impl.ext;

import com.effektif.workflow.api.activities.ErrMsgI18N;
import com.effektif.workflow.api.activities.SubProcess;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.api.workflow.Extensible;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.effektif.workflow.impl.activity.types.SubProcessImpl;
import com.effektif.workflow.impl.workflow.WorkflowImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;


import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 主流程的子流程节点生成的节点信息
 */
public class SubProcessTask extends Extensible implements Serializable {



    private static final long serialVersionUID = -1236248759155217976L;
    protected TaskId id;
    protected String subWorkflowInstanceId;
    protected String subWorkflowId;
    protected String subSourceWorkflowId;
    protected String subWorkflowName;
    protected String workflowId;
    protected String sourceWorkflowId;
    protected String activityId;
    protected String activityInstanceId;
    protected String workflowInstanceId;
    protected String tenantId;
    protected String type;
    protected String appId;
    protected String entityId;
    protected String objectId;
    protected Long createTime;
    protected Long endTime;
    protected Long modifyTime;
    protected String state;
    protected String applicantId;
    protected String errMsg;
    protected List<ErrMsgI18N> errMsgI18N;
    protected String name;
    protected String description;
    protected String workflowName;
    protected String workflowDescription;

    protected List<ApprovalOpinion> opinions;
    protected String submitter;
    protected String subProcessRejectStrategy;
    protected Boolean moveToCurrentActivityWhenReject;
    protected String subProcessLossStrategy;
    protected String subProcessEndrejectToBeforeTaskActivityId;

    protected String errorState;
    //如果是自动跳过的  需要设置为true
    protected boolean autoAgreed;


    public SubProcessTask() {

    }


    /**
     * 触发子流程后需要入库
     */
    public static SubProcessTask create(TaskId taskId,
                                        SubProcessImpl subProcessImpl,
                                        ActivityInstanceImpl parentActivityInstance,
                                        WorkflowInstance subWorkflowInstance, String state) {
        SubProcessTask task = new SubProcessTask();

        WorkflowImpl parentWorkflow = parentActivityInstance.workflow;
        WorkflowInstanceImpl parentWorkflowInstance = parentActivityInstance.workflowInstance;
        SubProcess parentSubProcessActivity = subProcessImpl.getActivity();
//        //企业配置
        task.setTenantId(parentWorkflow.getTenantId());
        task.setType(parentWorkflow.getType());
        task.setAppId(parentWorkflow.getAppId());

//        //子流程配置
        task.setId(taskId);
        if (Objects.nonNull(subWorkflowInstance)) {
            task.setSubWorkflowInstanceId(subWorkflowInstance.getId().toString());
            task.setSubWorkflowId(subWorkflowInstance.getWorkflowId().toString());
            task.setSubWorkflowName(subWorkflowInstance.getWorkflowName());
        }

        task.setSubProcessRejectStrategy(parentSubProcessActivity.getSubProcessRejectStrategy());
        task.setMoveToCurrentActivityWhenReject(parentSubProcessActivity.getMoveToCurrentActivityWhenReject());
        task.setSubProcessLossStrategy(parentSubProcessActivity.getSubProcessLossStrategy());
        task.setSubProcessEndrejectToBeforeTaskActivityId(parentSubProcessActivity.getSubProcessEndrejectToBeforeTaskActivityId());
//
//        //主流程任务节点配置
        task.setSubSourceWorkflowId(parentSubProcessActivity.getSourceWorkflowId());
        task.setName(parentSubProcessActivity.getName());
        task.setDescription(parentSubProcessActivity.getDescription());
        task.setDescription(parentSubProcessActivity.getDescription());
        task.setWorkflowInstanceId(parentWorkflowInstance.getId().toString());
        task.setWorkflowId(parentWorkflow.id.getInternal());
        task.setSourceWorkflowId(parentWorkflow.sourceWorkflowId);
        task.setActivityId(parentSubProcessActivity.getId());
        task.setActivityInstanceId(parentActivityInstance.id);
        task.setWorkflowName(parentSubProcessActivity.getName());
        task.setWorkflowDescription(parentSubProcessActivity.getDescription());
        task.setState(state);
        task.setApplicantId(getWorkflowInstanceProperty(parentWorkflowInstance, WorkflowBindingEnum.applicantId.toString()));
        task.setSubmitter(getWorkflowInstanceProperty(parentWorkflowInstance, WorkflowBindingEnum.submitter.toString()));

//
//        //数据配置
        task.setEntityId(getWorkflowInstanceProperty(parentWorkflowInstance, WorkflowBindingEnum.entityId.toString()));
        task.setObjectId(getWorkflowInstanceProperty(parentWorkflowInstance, WorkflowBindingEnum.objectId.toString()));
        //系统配置
        task.setCreateTime(System.currentTimeMillis());
        task.setModifyTime(System.currentTimeMillis());
        return task;
    }

    public static String getWorkflowInstanceProperty(WorkflowInstanceImpl workflowInstance, String key) {
        if (workflowInstance == null || key == null) {
            return null;
        }
        if (workflowInstance.getProperty(key) != null) {
            return getObjectHelper(workflowInstance.getProperty(key));
        }
        return getObjectHelper(workflowInstance.getTransientProperty(key));
    }

    public static String getObjectHelper(Object obj) {
        String result = null;
        if (obj != null) {
            result = obj.toString();
        }
        return result;
    }


    public TaskId getId() {
        return id;
    }

    public void setId(TaskId id) {
        this.id = id;
    }

    public String getSubWorkflowInstanceId() {
        return subWorkflowInstanceId;
    }

    public void setSubWorkflowInstanceId(String subWorkflowInstanceId) {
        this.subWorkflowInstanceId = subWorkflowInstanceId;
    }

    public String getSubWorkflowId() {
        return subWorkflowId;
    }

    public void setSubWorkflowId(String subWorkflowId) {
        this.subWorkflowId = subWorkflowId;
    }

    public String getSubSourceWorkflowId() {
        return subSourceWorkflowId;
    }

    public void setSubSourceWorkflowId(String subSourceWorkflowId) {
        this.subSourceWorkflowId = subSourceWorkflowId;
    }

    public String getSubWorkflowName() {
        return subWorkflowName;
    }

    public void setSubWorkflowName(String subWorkflowName) {
        this.subWorkflowName = subWorkflowName;
    }

    public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public String getSourceWorkflowId() {
        return sourceWorkflowId;
    }

    public void setSourceWorkflowId(String sourceWorkflowId) {
        this.sourceWorkflowId = sourceWorkflowId;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getActivityInstanceId() {
        return activityInstanceId;
    }

    public void setActivityInstanceId(String activityInstanceId) {
        this.activityInstanceId = activityInstanceId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getApplicantId() {
        return applicantId;
    }

    public void setApplicantId(String applicantId) {
        this.applicantId = applicantId;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public List<ErrMsgI18N> getErrMsgI18N() {
        return errMsgI18N;
    }

    public void setErrMsgI18N(List<ErrMsgI18N> errMsgI18N) {
        this.errMsgI18N = errMsgI18N;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getWorkflowName() {
        return workflowName;
    }

    public void setWorkflowName(String workflowName) {
        this.workflowName = workflowName;
    }

    public String getWorkflowDescription() {
        return workflowDescription;
    }

    public void setWorkflowDescription(String workflowDescription) {
        this.workflowDescription = workflowDescription;
    }

    public List<ApprovalOpinion> getOpinions() {
        return opinions;
    }

    public void setOpinions(List<ApprovalOpinion> opinions) {
        this.opinions = opinions;
    }

    public String getSubmitter() {
        return submitter;
    }

    public void setSubmitter(String submitter) {
        this.submitter = submitter;
    }

    public String getSubProcessRejectStrategy() {
        return subProcessRejectStrategy;
    }

    public void setSubProcessRejectStrategy(String subProcessRejectStrategy) {
        this.subProcessRejectStrategy = subProcessRejectStrategy;
    }

    public Boolean getMoveToCurrentActivityWhenReject() {
        return moveToCurrentActivityWhenReject;
    }

    public void setMoveToCurrentActivityWhenReject(Boolean moveToCurrentActivityWhenReject) {
        this.moveToCurrentActivityWhenReject = moveToCurrentActivityWhenReject;
    }

    public String getSubProcessLossStrategy() {
        return subProcessLossStrategy;
    }

    public void setSubProcessLossStrategy(String subProcessLossStrategy) {
        this.subProcessLossStrategy = subProcessLossStrategy;
    }

    public String getWorkflowInstanceId() {
        return workflowInstanceId;
    }

    public void setWorkflowInstanceId(String workflowInstanceId) {
        this.workflowInstanceId = workflowInstanceId;
    }

    public String getSubProcessEndrejectToBeforeTaskActivityId() {
        return subProcessEndrejectToBeforeTaskActivityId;
    }

    public void setSubProcessEndrejectToBeforeTaskActivityId(String subProcessEndrejectToBeforeTaskActivityId) {
        this.subProcessEndrejectToBeforeTaskActivityId = subProcessEndrejectToBeforeTaskActivityId;
    }

    public String getErrorState() {
        return errorState;
    }

    public void setErrorState(String errorState) {
        this.errorState = errorState;
    }

    public boolean getAutoAgreed() {
        return autoAgreed;
    }

    public void setAutoAgreed(boolean autoAgreed) {
        this.autoAgreed = autoAgreed;
    }
}
