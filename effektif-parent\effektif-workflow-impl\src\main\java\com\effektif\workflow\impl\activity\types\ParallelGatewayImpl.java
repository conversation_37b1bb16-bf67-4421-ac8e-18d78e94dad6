/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.impl.activity.types;

import com.effektif.workflow.api.activities.ParallelGateway;
import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.activity.AbstractActivityType;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflow.TransitionImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
public class ParallelGatewayImpl extends AbstractActivityType<ParallelGateway> {

    /**
     * 连入到并行网关的线的条数
     */
    int nbrOfIncomingTransitions = -1;
    /**
     * 当前并行网关是否可以连出
     */
    boolean hasOutgoingTransitions = false;
    //并行网关中的线如果存在 condition 则设置为true
    boolean saveTransitionsTaken = false;

    public ParallelGatewayImpl() {
        super(ParallelGateway.class);
    }

    @Override
    public void parse(ActivityImpl activityImpl, ParallelGateway activityApi, WorkflowParser parser) {
        super.parse(activityImpl, activityApi, parser);
        // at least one in, at least one out
        List<TransitionImpl> incomingTransitions = activityImpl.getIncomingTransitions();
        if (incomingTransitions == null || incomingTransitions.isEmpty()) {
            parser.addWarning("Parallel gateway '%s' does not have incoming transitions", activityImpl.id);
        } else {
            nbrOfIncomingTransitions = incomingTransitions.size();
        }
        List<TransitionImpl> outgoingTransitions = activityImpl.getOutgoingTransitions();
        if (outgoingTransitions == null || outgoingTransitions.isEmpty()) {
            parser.addWarning("Parallel gateway '%s' does not have outgoing transitions", activityImpl.id);
        } else {
            hasOutgoingTransitions = true;
            for (TransitionImpl outgoingTransition : activityImpl.getOutgoingTransitions()) {
                if (outgoingTransition.condition != null) {
                    saveTransitionsTaken = true;
                }
            }
        }
    }

    /**
     *      ┌──→[t1]───┐
     *      │          ↓
     *  ◯─→<+A1>─→[t2]─→<+A2>─→◯
     *      │          ↑
     *      └──→[t3]───┘
     */
    @Override
    public void execute(ActivityInstanceImpl activityInstance) {
        activityInstance.end();
        boolean hasOtherUnfinishedActivities = false;

        /**
         * t1,t2完成后,均会在activityInstances中增加一条workState=joining的activityInstance
         */
        List<ActivityInstanceImpl> otherJoiningActivityInstances = new ArrayList<>();
        //遍历目前实例上的activityInstances
        for (ActivityInstanceImpl siblingActivityInstance : activityInstance.parent.activityInstances) {
            //判断activityInstances是否有没完成的节点
            if (!siblingActivityInstance.isEnded()) {
                hasOtherUnfinishedActivities = true;
            }

            //只有join时,才会走以下逻辑
            if (siblingActivityInstance != activityInstance
                && siblingActivityInstance.getActivity() == activityInstance.getActivity()
                && siblingActivityInstance.isJoining()) {
                //如果t1,t2,t3都完成了,otherJoiningActivityInstances中会有2条
                otherJoiningActivityInstances.add(siblingActivityInstance);
            }
        }

        /**
         *
         *  A1执行,会立即完成,执行到onwards
         *  t1 t2 t3均会创建,并路由到userTask
         *  t1完成后,会执行到setJoining
         *  t2完成后,会执行到setJoining
         *  t3完成后,会执行到onwards
         *  A2执行,会立即完成,执行到onwards
         */
        // 如果当前并行节点没有连出的线
        if (!hasOutgoingTransitions) {
            activityInstance.propagateToParent();

        } else if (otherJoiningActivityInstances.size() == (nbrOfIncomingTransitions - 1) || !hasOtherUnfinishedActivities) {
            //otherJoiningActivityInstances.size() == (nbrOfIncomingTransitions - 1)
            // 其中  otherJoiningActivityInstances.size() 为t1,t2完成后,workState=joining的个数
            // 其中 nbrOfIncomingTransitions - 1 ,要减1的原因是,最后一次执行,不会再执行activityInstance.setJoining(),故里面只有2条
            if (log.isDebugEnabled())
                log.debug("Firing parallel gateway");
            //设置 t1,t2完成后,所生成的  workState=joining 为workState=null
            for (ActivityInstanceImpl otherJoiningActivityInstance : otherJoiningActivityInstances) {
                activityInstance.removeJoining(otherJoiningActivityInstance);
            }
            //并行结束后,继续向下执行
            activityInstance.onwards();

        } else {
            //如果并行内的节点完成了,但是整个并行下其他的分支没有完成,则生成一条workState=joining
            activityInstance.setJoining();
        }
    }

    @Override
    public boolean isFlushSkippable() {
        return true;
    }

    @Override
    public boolean saveTransitionsTaken() {
        return saveTransitionsTaken;
    }

}
