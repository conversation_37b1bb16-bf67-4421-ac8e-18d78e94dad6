<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<templates>
  <template autoinsert="false" context="gettercomment_context"
    deleted="false" description="Comment for getter method" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.gettercomment" name="gettercomment" />
  <template autoinsert="false" context="settercomment_context"
    deleted="false" description="Comment for setter method" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.settercomment" name="settercomment" />
  <template autoinsert="false" context="constructorcomment_context"
    deleted="false" description="Comment for created constructors"
    enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.constructorcomment"
    name="constructorcomment" />
  <template autoinsert="false" context="filecomment_context"
    deleted="false" description="Comment for created Java files"
    enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.filecomment"
    name="filecomment">/* Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 * http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License. */</template>
  <template autoinsert="false" context="typecomment_context"
    deleted="false" description="Comment for created types" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.typecomment" name="typecomment">/**
    * <AUTHOR>
    */</template>
  <template autoinsert="false" context="fieldcomment_context"
    deleted="false" description="Comment for fields" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.fieldcomment" name="fieldcomment" />
  <template autoinsert="false" context="methodcomment_context"
    deleted="false" description="Comment for non-overriding methods"
    enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.methodcomment"
    name="methodcomment" />
  <template autoinsert="false" context="overridecomment_context"
    deleted="false" description="Comment for overriding methods"
    enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.overridecomment"
    name="overridecomment" />
  <template autoinsert="false" context="delegatecomment_context"
    deleted="false" description="Comment for delegate methods" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.delegatecomment" name="delegatecomment" />
  <template autoinsert="true" context="newtype_context"
    deleted="false" description="Newly created files" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.newtype" name="newtype">${filecomment}
    ${package_declaration}

    ${typecomment}
    ${type_declaration}</template>
  <template autoinsert="true" context="classbody_context"
    deleted="false" description="Code in new class type bodies" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.classbody" name="classbody">
  </template>
  <template autoinsert="true" context="interfacebody_context"
    deleted="false" description="Code in new interface type bodies"
    enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.interfacebody"
    name="interfacebody">
  </template>
  <template autoinsert="true" context="enumbody_context"
    deleted="false" description="Code in new enum type bodies" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.enumbody" name="enumbody">
  </template>
  <template autoinsert="true" context="annotationbody_context"
    deleted="false" description="Code in new annotation type bodies"
    enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.annotationbody"
    name="annotationbody">
  </template>
  <template autoinsert="false" context="catchblock_context"
    deleted="false" description="Code in new catch blocks" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.catchblock" name="catchblock">${exception_var}.printStackTrace();
  </template>
  <template autoinsert="false" context="methodbody_context"
    deleted="false" description="Code in created method stubs" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.methodbody" name="methodbody">${body_statement}
  </template>
  <template autoinsert="false" context="constructorbody_context"
    deleted="false" description="Code in created constructor stubs"
    enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.constructorbody"
    name="constructorbody">${body_statement}</template>
  <template autoinsert="true" context="getterbody_context"
    deleted="false" description="Code in created getters" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.getterbody" name="getterbody">return ${field};</template>
  <template autoinsert="true" context="setterbody_context"
    deleted="false" description="Code in created setters" enabled="true"
    id="org.eclipse.jdt.ui.text.codetemplates.setterbody" name="setterbody">${field} = ${param};</template>
</templates>