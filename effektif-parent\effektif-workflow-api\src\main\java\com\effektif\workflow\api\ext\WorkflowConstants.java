package com.effektif.workflow.api.ext;

/**
 * Created by liuyl on 2016/9/23.
 */
public interface WorkflowConstants {
    // 任务上标识来源
    String SOURCE_TRANSITION="sourceTransition";
    /**
     * 一个流程实例中允许的最大activity instance数量,避免节点死循环
     */
    int MAX_ACTIVITY_INSTANCES = 199;
    /**
     * oneFlow节点实例最大数量
     */
    int MAX_ACTIVITY_INSTANCES_OF_ONE = 5000;
    /**
     * 15分钟内执行次数>50次， 或总次数>500次，则抛出异常 920
     */
    String CHECK_EXCLUSIVE_GATEWAY_LOOP="checkExclusiveGateWayLoop";
    int EXCLUSIVE_GATEWAY_LOOP_MAX=50;
    String SYSTEM_USER = "-10000";
    String SUB_PROCESS_TASK_SKIP = "skip";
    String SUB_PROCESS = "subProcess";

    String PARALLEL_REJECT_TO_END_STRATEGY = "parallelRejectToEndStrategy";
    String PARALLEL_REJECT_TO_BEFORE_TASK_STRATEGY = "parallelRejectToBeforeTaskStrategy";

    String TO_ACTIVITY_ID = "toActivityId";

    String END = "end";

    String ACTION_TYPE = "actionType";

    String PARALLEL_GATEWAY_ID = "parallelGateWayId";
    String PARALLEL_GATEWAY_NO = "parallelGateWayNo";
    String PARALLEL_GATEWAY_NAME = "parallelGateWayName";
    String DELAY = "delay";
    String AUTO = "auto";
    /**
     * 过期时间
     */
    String EXPIRE_DATE = "expireDate";
    /**
     * 实例和任务的默认过期时间
     */
    int DEFAULT_EXPIRE_DATE = 30;

    interface Base{
        String TENANT_ID="tenantId",APP_ID="appId",TYPE="type",ENTITY_ID="entityId",OBEJCT_ID="objectId",SUBMITTER="submitter",OUTER_SUBMITTER = "outerSubmitter";
    }

    interface InstanceStatus {
        String IN_PROGRESS = "in_progress";
        String PASS = "pass";
        String REJECT = "reject";
        String CANCEL = "cancel";
        String ERROR = "error";
        String DESTROY = "destroy";//stage instance destroy
    }

    interface InstanceSubState {
        String NORMAL = "normal";
        String BUILD_TASK_EXCEPTION = "BuildTaskException";
        String BPM_GET_LASTED_VARIABLE_VALUE_ERROR = "bpmGetLastedVariableValueError";
        String ANALYZE_PERSON_EXCEPTION = "AnalyzePersonException";
        String EXECUTE_TASK_AFTER_ACTION_EXCEPTION = "ExecuteTaskAfterActionException";
        String CALL_BACK_EXCEPTION = "CallBackException";
        String EXECUTE_INSTANCE_AFTER_ACTION_EXCEPTION = "ExecuteInstanceAfterActionException";
        String CALL_BACK_WAITING = "CallBackWaiting";//755 解决最后一个节点  callback 导致时间过长的问题
    }

    interface EndState{
        String EXCLUSIVE_GATEWAY_LOOP = "exclusiveGatewayLoop";//920 one flow
    }

    interface UserTaskStatus {
        String IN_PROGRESS = "in_progress";
        String PASS = "pass";
        String AUTO_PASS = "auto_pass";
        String REJECT = "reject";
        String CANCEL = "cancel";
        String GO_BACK = "go_back";
        String AUTO_GO_BACK = "auto_go_back";
        String SCHEDULE = "schedule";
        String ERROR = "error";
        String TAG_WAITING = "tag_waiting";
        String RETRIEVE = "retrieve";
    }

    interface UserTaskSubState{
        /**
         * 并行网关中支持审批节点,其中一个节点被驳回后,当前并行网关下  其他分支进行中的任务,状态变为驳回,子状态修改为parallelReject
         */
        String PARALLEL_REJECT  ="parallelReject";
    }

    interface StageStatus {
        String SKIPPED = "skipped";//已跳过
        String IN_PROGRESS = "in_progress";//进行中
        String UNCOMPLETED = "uncompleted";//未完成
        String PASS = "pass";//完成
        String UNSTART = "unstart";//未开始
        String CANCEL = "cancel";//取消
        String REPEAT = "repeat";//重复
        /**
         * 未完成被跳过
         */
        String UNCOMPLETED_SKIPPED = "uncompletedSkipped";
    }

    interface StageTaskStatus {
        String ERROR = "error";
        String IN_PROGRESS = "in_progress";//进行中
        String UNCOMPLETED = "uncompleted";//未完成
        String PASS = "pass";//完成
        String CANCEL = "cancel";//取消
    }


    interface UserTaskType {
        String SINGLE = "single";
        String ONE_PASS = "one_pass";
        String ALL_PASS = "all_pass";

        /* 引擎自身只处理以下组合 */
        String ONE = "one";//[person,applicant,dept_leader,leader,ext_bpm]
        String ANYONE = "anyone";//[person,dept,group,role,ext_bpm]
        String ONE_BY_ONE = "one_by_one";//[person,level,grade,ext_bpm]
        String ALL = "all";//[person,group,ext_bpm]
    }


    interface AssigneeType {
        String PERSON = "person";
        String DEPT = "dept";
        String GROUP = "group";
        String ROLE = "role";
        String APPLICANT = "applicant";
        String DEPT_LEADER = "dept_leader";
        String SUPERVISOR = "supervisor";
        String LEVEL = "level";
        String GRADE = "grade";
        String LOOP = "loop";
        String LEVEL_LOOP = "level_loop";
        String LEADER_LEVEL = "leader_level";
        String EXT_BPM = "ext_bpm";
        String extUserType = "extUserType";
        String ext_process = "ext_process";
        String EXTERNAL_ROLE = "external_role";
        String APPROVAL_ROLE = "approval_role";
        String ABSOLUTE_LEVEL = "absolute_level";
        String PERSON_LIST = "person_list";
        String ASSIGNEE_FUNCTION = "assigneeFunction";
        String ASSIGNEE_EMPTY = "assigneeEmpty";
        String GROUP_HANDLER="groupHandler";
    }


    interface ExecuteType {
        String SEND_EMAIL = "send_email";
        //发给crm的提醒
        String SEND_QIXIN = "send_qixin";
        //将定义时保存的一段json透传给crm做字段更新,过程中不解析json结构
        String UPDATES = "updates";
        String TRIGGER_BPM = "trigger_bpm";
        String TRIGGER_OPERATION = "trigger_operation";

        String CUSTOM_FUNCTION = "custom_function";
        String AI = "ai";
        String SEND_SMS = "send_sms";
        String EDIT_TEAM_MEMBER = "edit_team_member";
        String CUSTOM_VARIABLE_EVALUATION = "custom_variable_evaluation";
        String EXECUTE_CONVERT_RULE = "execute_convert_rule";
    }


    interface Action {
        String AGREE = "agree";
        //历史原因，审批流agree之后Action为pass
        String PASS = "pass";
        String AUTO_AGREE = "auto_agree";
        String REJECT = "reject";
        String GO_BACK = "go_back";
        String AUTO_GO_BACK = "auto_go_back";
        String CANCEL = "cancel";
        String ADD_TAG = "addTag";
        String TAG_AFTER = "tagAfter";
        String RETRIEVE = "retrieve";
        String SKIP_TASK_VALIDATE = "skip_task_validate";
    }


    interface SystemVariable {
        //审批操作
        String DECIDE = "action";
        //最近的一次审批节点id
        String LATEST_USER_TASK_ID = "latest_user_task_id";
        //审批过的节点activityIds
        String PROVIOUS_USER_TASK_ACTIVITY_ID_LIST = "previous_user_task_activity_id_list";
        //执行auto_go_back的标识
        String AUTO_GO_BACK_FLAG="auto_go_back_flag";
    }


    interface WorkflowType {
        String APPROVAL_FLOW = "approvalflow";
        String WORKFLOW = "workflow";
        String BPM = "workflow_bpm";
        String STAGE = "stage";
        String ONE = "one";
    }


    interface AppId {
        String APP_ID_XT = "facishare-xt";
        String APP_ID_CRM = "CRM";
        String APP_ID_BPM = "BPM";
//        String APP_ID_STAGE = "STAGE";
    }


    interface ActivityType {
        String USER_TASK = "user_task";
        String EXCLUSIVE_GATEWAY = "exclusive_gateway";
        String PARALLEL_GATEWAY = "parallel_gateway";
        String EXECUTION_TASK = "execution_task";
    }


    interface GatewayType {
        //默认独占网关
        int DEFAULT = 0;
        //用户设置的独占网关
        int CONDITIONAL = 1;
    }

    interface TaskType {
        String STAGE = "stage";
        String STAGE_TASK_ITEM = "taskItem";
    }

    interface AutoTaskState {
        String COMPLETED = "pass";
        String ERROR = "error";
        String IN_PROGRESS = "in_progress";
    }

    interface AutoTaskParaKey {
        String TASK_ID = "TASK_ID";
    }

    interface NodeType {
        String LOOP = "loop";
        String TAG = "tag";
        String TAG_AFTER = "tagAfter";
    }
    interface EngineVariableKey{
        String submitter="engineVariables_submitter";
    }

    interface AfterActionExecuteType{
        int retry = 1;
        int ignore = 0;

        static boolean isRetry(int executeType){
            return executeType==retry;
        }

        static boolean isIgnore(int executeType){
            return executeType==ignore;
        }
    }

}
