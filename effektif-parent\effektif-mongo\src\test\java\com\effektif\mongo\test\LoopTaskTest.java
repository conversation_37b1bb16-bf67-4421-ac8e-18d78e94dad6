/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.mongo.test;

import com.effektif.mongo.DefaultMongoObjectMapper;
import com.effektif.workflow.api.activities.*;
import com.effektif.workflow.api.condition.And;
import com.effektif.workflow.api.condition.Equals;
import com.effektif.workflow.api.condition.Or;
import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.types.TextType;
import com.effektif.workflow.api.workflow.Binding;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflow.MultiInstance;
import com.effektif.workflow.api.workflow.Transition;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.effektif.workflow.impl.data.types.ObjectType;
import com.effektif.workflow.impl.util.Lists;
import com.google.common.collect.Maps;
import com.mongodb.BasicDBObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;

/**
 * <AUTHOR>
 */
public class LoopTaskTest {

    /**
     * Tests a Loop cantain subprocess.
     * <pre>
     *
     *           ┌───────────────────────┐
     *           │ loop :subprocess1     │
     *  [start]──┤                       ├─→[t3]─>[end]
     *           │ [s1]                  │
     *           └───────────────────────┘
     *
     * </pre>
     */
    @Test
    public void testNestedSubprocess() {
        // @formatter:off
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("start", new StartEvent()
                        .transitionTo("loopTask"))
                .activity("loopTask", new LoopTask()
                        .activity("subProcess", new EmbeddedSubprocess()
                                .multiInstance(new MultiInstance().sequential(true)
                                .valuesExpression("custom_variable##list__c")
                                .variable("custom_variable##list_item__c", TextType.INSTANCE))
                                .activity("s1", new BlockExecutionTask().executeType("auto").transitionTo("s2"))
                                .activity("s2", new ReceiveTask().transitionTo("s3"))
                                .activity("s3", new ReceiveTask())
                        )
                        .transitionTo("t3")
                )
                .activity("t3", new ReceiveTask()
                        .transitionTo("end"))
                .activity("end", new EndEvent());
        BasicDBObject workflowJson = new DefaultMongoObjectMapper().write(workflow);
        System.out.println(workflowJson);
    }

    @Test
    public void testNestedSubprocessVariablesTest() {
        // @formatter:off
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("start", new StartEvent()
                        .transitionTo("loopTask"))

                .activity("loopTask", new LoopTask()
                        .activity("subProcess", new EmbeddedSubprocess()
                                .multiInstance(
                                        new MultiInstance().sequential(true).asc(true)
                                                .variable("custom_variable##list_item__c", TextType.INSTANCE)
                                                .valuesExpression("custom_variable##list__c")
                                )
                                .activity("N-start", new StartEvent()
                                        .transitionTo("N-E"))

                                .activity("N-E",new ExclusiveGateway())
                                .transitionWithConditionTo(new Or().condition(new And().condition(
                                        new Equals()
                                                .left(new Binding<>().expression("custom_variable##data.field_Al22Z__c"))
                                                .right(new Binding<>().value("cui1").type(new TextType())))),"N-BET-01")
                                .transitionWithConditionTo(new Or().condition(new And().condition(
                                        new Equals()
                                                .left(new Binding<>().expression("custom_variable##data.field_Al22Z__c"))
                                                .right(new Binding<>().value("cui2").type(new TextType())))),"N-BET-02")

                                .activity("N-BET-01", new BlockExecutionTask()
                                        .extension("assignment",Arrays.asList(new AssignmentItem("custom_variable##data.field_Xxb9v__c","assign",0)))
                                        .transitionTo("N-end"))

                                .activity("N-BET-02", new BlockExecutionTask().executeType("auto")
                                        .extension("assignment",Arrays.asList(new AssignmentItem("custom_variable##data.field_2vCEF__c","assign","999999")))
                                        .transitionTo("N-end"))

                                .activity("N-end", new EndEvent())

                                .variable("custom_variable##data.field_Al22Z__c", TextType.INSTANCE)
                        )
                        .transitionTo("t3")
                )
                .activity("t3", new ReceiveTask()
                        .transitionTo("end"))
                .activity("end", new EndEvent());
        BasicDBObject workflowJson = new DefaultMongoObjectMapper().write(workflow);
        System.out.println(workflowJson);
    }


    @Test
    public void testNestedSubprocessVariablesTest112() {
        // @formatter:off
        ExecutableWorkflow workflow = new ExecutableWorkflow().subType("auto").sourceWorkflowId("aMMtPBercA__oneflow").type("one").appId("CRM")
                .activity("1730345019377", new StartEvent()
                        .transitionTo("1730345186554"))

                .activity("1730345186554", new BlockExecutionTask()
                        .extension("entityId", "object_RlhY0__c")
                        .extension("searchType", "all")
                        .extension("taskType", "query")
                        .extension("selectData", "first")
                        .extension("useFields", Arrays.asList("id", "name", "field_tJ24V__c", "field_pOF05__c", "field_2vCEF__c", "field_d5xM7__c", "field_w67vK__c", "field_Xxb9v__c"))
                        .outputList("$", "custom_variable##data")
                        .transitionTo("1730345059586"))

                .activity("1730345059586", new LoopTask()
                        .activity("subProcess", new EmbeddedSubprocess()
                                .multiInstance(
                                        new MultiInstance().sequential(true).asc(true)
                                                .variable("custom_variable##list_item__c", TextType.INSTANCE)
                                                .valuesExpression("custom_variable##list__c")
                                )
                                .activity("1730345060409", new StartEvent()
                                        .transitionTo("1730345087037"))

                                .activity("1730345087037",new ExclusiveGateway()
                                        .transitionWithConditionTo(new Or().condition(new And().condition(
                                                new Equals()
                                                        .left(new Binding<>().expression("custom_variable##data##field_666__c"))
                                                        .right(new Binding<>().value("cui1").type(new TextType())))),"1730345114597")


                                        .transitionWithConditionTo(new Or().condition(new And().condition(
                                                new Equals()
                                                        .left(new Binding<>().expression("custom_variable##data##field_Al22Z__c"))
                                                        .right(new Binding<>().value("cui2").type(new TextType())))),"1730345120403"))

                                .activity("1730345120403", new BlockExecutionTask()
                                        .extension("assignment",Arrays.asList(new AssignmentItem("custom_variable##data##field_Xxb9v__c","assign",0)))
                                        .transitionTo("1730345060410"))

                                .activity("1730345114597", new BlockExecutionTask()
                                        .extension("assignment",Arrays.asList(new AssignmentItem("custom_variable##data##field_2vCEF__c","assign","999999")))
                                        .transitionTo("1730345060410"))

                                .activity("1730345060410", new EndEvent())

                                .variable("custom_variable##data##field_Al22Z__c", TextType.INSTANCE)
                                .variable("custom_variable##list__c", TextType.INSTANCE)
                                .variable("custom_variable##data##field_666__c", TextType.INSTANCE)
                        )
                        .transitionTo("1730345155443")
                )

                .activity("1730345155443", new EndEvent())
                .variable("custom_variable##data", ObjectType.INSTANCE)
                .variable("custom_variable##aaa", TextType.INSTANCE)

                .property("customVariableTable", Arrays.asList(
                        new CustomVariable("object", "查询赋值","custom_variable##data","","object_RlhY0__c","",""),
                        new CustomVariable("list", "test","custom_variable##aaa","","","object","object_RlhY0__c")
                ));
        BasicDBObject workflowJson = new DefaultMongoObjectMapper().write(workflow);
        System.out.println(workflowJson);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AssignmentItem{
        private String key;
        private String operator;
        private Object value;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CustomVariable{
        private String type;
        private String label;
        private String id;
        private Object default_value;
        private String objectApiName;
        private String elementType;
        private String elementObjectApiName;
    }


}
