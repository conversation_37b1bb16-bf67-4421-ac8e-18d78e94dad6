package com.effektif.mongo;

/**
 * liugh
 * 2019/3/3 15:00
 */
public interface AutoTaskFields {
  String _ID = "_id";
  String NAME = "name";
  String SOURCE_WORKFLOW_ID = "sourceWorkflowId";
  String WORKFLOW_ID = "workflowId";
  String WORKFLOW_INSTANCE_ID = "workflowInstanceId";
  String TENANT_ID = "tenantId";
  String ACTIVITY_ID = "activityId";
  String ACTIVITY_INSTANCEID = "activityInstanceId";
  String TYPE = "type";
  String ACTION_TYPE = "actionType";
  String TASK_TYPE = "taskType";

  String APP_ID = "appId";
  String ENTITY_ID = "entityId";
  String OBJECT_ID = "objectId";
  String APPLICANT_ID = "applicantId";
  String CREATE_TIME = "createTime";
  String MODIFY_TIME = "modifyTime";
  String MODIFIER = "modifier";
  String STATE = "state";
  String WORKFLOW_NAME = "workflowName";
  String EXECUTION = "executionList";
  String DELETED = "deleted";
  String DESCRIPTION = "description";
  String EVENT_EXTENSION = "eventExtension";
  String BPM_EXTENSION = "bpmExtension";
  String LATENCY_UNIT = "latencyUnit";
  String LATENCY_TIME = "latencyTime";
  String TIME_TYPE = "timeType";
  String ERR_MSG = "errMsg";
  String TASK_EXECUTE_STATE = "taskExecuteState";
  String REMIND = "remind";
  String REMIND_LATENCY = "remindLatency";
  String EXECUTE_TIME = "executeTime";
  String FINISHED_TIME = "finishedTime";
  String DELAY = "delay";
  String EXPRESSION = "expression";
  String EXECUTOR = "executor";
  String OPINIONS = "opinions";
  String EXECUTION_TIMES = "executionTimes";
  String LINK_APP = "linkApp";
  String LINK_APP_ENABLE= "linkAppEnable";
  String LINK_APP_NAME= "linkAppName";
  String LINK_APP_TYPE= "linkAppType";
  String OUTER_TENANT_FIELD = "outerTenantField";
  String OUTER_TENANT_ID = "outerTenantId";
}
