package com.effektif.workflow.impl.ext;

import java.util.List;
import java.util.Map;

/**
 * desc:完成任务传递的参数
 * version: 6.7
 * Created by cuiyongxu on 2019/8/22 2:31 PM
 */
public class CompleteTaskArg {

    private String taskId;
    private Map<String, Object> conditionMap;
    private Map<String, Object> bindingMap;
    private String actionType;
    private Map<String, List<String>> nextTaskAssignee;
    private String tenantId;
    private String appId;
    private String type;
    private String userId;
    /**
     * 审批流批量完成任务 需要审批流更新  完成任务是否成  此值传递给审批流
     */
    private String requestId;
    private String traceId;

    /**
     * 调用方与引擎的共享参数
     */
    private Map<String, Object> extraData;
    private Object clientInfo;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Map<String, Object> getConditionMap() {
        return conditionMap;
    }

    public void setConditionMap(Map<String, Object> conditionMap) {
        this.conditionMap = conditionMap;
    }

    public Map<String, Object> getBindingMap() {
        return bindingMap;
    }

    public void setBindingMap(Map<String, Object> bindingMap) {
        this.bindingMap = bindingMap;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }


    public Map<String, List<String>> getNextTaskAssignee() {
        return nextTaskAssignee;
    }

    public void setNextTaskAssignee(Map<String, List<String>> nextTaskAssignee) {
        this.nextTaskAssignee = nextTaskAssignee;
    }

    public Map<String, Object> getExtraData() {
        return extraData;
    }

    public void setExtraData(Map<String, Object> extraData) {
        this.extraData = extraData;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public Object getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(Object clientInfo) {
        this.clientInfo = clientInfo;
    }

    public static CompleteTaskArg create(
            String tenantId,
            String appId,
            String userId, String taskId,
            Map<String, Object> conditionMap,
            Map<String, Object> bindingMap,
            String actionType,
            Map<String, List<String>> nextTaskAssignee,
            Map<String, Object> extraData, Object clientInfo) {
        CompleteTaskArg arg = new CompleteTaskArg();
        arg.setTenantId(tenantId);
        arg.setAppId(appId);
        arg.setUserId(userId);
        arg.setTaskId(taskId);
        arg.setConditionMap(conditionMap);
        arg.setBindingMap(bindingMap);
        arg.setActionType(actionType);
        arg.setNextTaskAssignee(nextTaskAssignee);
        arg.setExtraData(extraData);
        arg.setClientInfo(clientInfo);
        return arg;
    }
}
