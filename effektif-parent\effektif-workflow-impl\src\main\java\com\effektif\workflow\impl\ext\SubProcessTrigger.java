package com.effektif.workflow.impl.ext;

import com.effektif.workflow.api.activities.SubProcess;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.impl.activity.types.SubProcessImpl;
import com.effektif.workflow.impl.memory.TestConfiguration;
import com.effektif.workflow.impl.util.WorkflowPropertiesUtil;
import com.effektif.workflow.impl.workflow.WorkflowImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;

public class SubProcessTrigger {

    private SubProcessTrigger() {
    }

    /**
     * 企业信息
     */
    private String tenantId;


    /**
     * 主流程任务信息
     */
    private String name;
    private String description;

    private String subProcessRejectStrategy;
    private String subProcessEndRejectToBeforeTaskActivityId;
    private Boolean moveToCurrentActivityWhenReject;
    private String subProcessLossStrategy;
    private String subProcessSourceWorkflowId;

    /**
     * 主流程定义信息
     */
    private String entityId;
    private String objectId;

    /**
     * 主流程实例信息
     */
    private String workflowInstanceId;
    private String activityInstanceId;
    private String activityId;

    /**
     * todo 单测使用
     */
    private WorkflowInstanceImpl workflowInstanceImpl;
    /**
     * todo 单测使用
     */
    private ActivityInstanceImpl activityInstance;
    /**
     * todo 单测使用
     */
    private SubProcessImpl subProcessImpl;

    public static SubProcessTrigger create(ActivityInstanceImpl parentActivityInstance, SubProcessImpl subProcessImpl) {
        WorkflowImpl parentWorkflow = parentActivityInstance.workflow;
        WorkflowInstanceImpl parentWorkflowInstance = parentActivityInstance.workflowInstance;
        SubProcess parentSubProcessActivity = subProcessImpl.getActivity();

        SubProcessTrigger subProcessTrigger = new SubProcessTrigger();

        if (parentActivityInstance.getConfiguration() instanceof TestConfiguration) {
            subProcessTrigger.setWorkflowInstanceImpl(parentWorkflowInstance);
            subProcessTrigger.setActivityInstance(parentActivityInstance);
            subProcessTrigger.setSubProcessImpl(subProcessImpl);
        }
        /**
         * 企业信息
         */
        subProcessTrigger.setTenantId(parentWorkflow.getTenantId());

        /**
         * 主流程任务信息
         */
        subProcessTrigger.setName(parentSubProcessActivity.getName());
        subProcessTrigger.setDescription(parentSubProcessActivity.getDescription());
        subProcessTrigger.setSubProcessRejectStrategy(parentSubProcessActivity.getSubProcessRejectStrategy());
        subProcessTrigger.setSubProcessEndRejectToBeforeTaskActivityId(parentSubProcessActivity.getSubProcessEndrejectToBeforeTaskActivityId());
        subProcessTrigger.setMoveToCurrentActivityWhenReject(parentSubProcessActivity.getMoveToCurrentActivityWhenReject());
        subProcessTrigger.setSubProcessLossStrategy(parentSubProcessActivity.getSubProcessLossStrategy());
        subProcessTrigger.setSubProcessSourceWorkflowId(parentSubProcessActivity.getSourceWorkflowId());

        subProcessTrigger.setEntityId(WorkflowPropertiesUtil.getWorkflowInstanceProperty(parentWorkflowInstance, WorkflowBindingEnum.entityId.toString()));
        subProcessTrigger.setObjectId(WorkflowPropertiesUtil.getWorkflowInstanceProperty(parentWorkflowInstance, WorkflowBindingEnum.objectId.toString()));

        /**
         * 主流程实例信息
         */
        subProcessTrigger.setWorkflowInstanceId(parentWorkflowInstance.getId().getInternal());
        subProcessTrigger.setActivityInstanceId(parentActivityInstance.getId());
        subProcessTrigger.setActivityId(parentSubProcessActivity.getId());

        return subProcessTrigger;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSubProcessRejectStrategy() {
        return subProcessRejectStrategy;
    }

    public void setSubProcessRejectStrategy(String subProcessRejectStrategy) {
        this.subProcessRejectStrategy = subProcessRejectStrategy;
    }

    public String getSubProcessEndRejectToBeforeTaskActivityId() {
        return subProcessEndRejectToBeforeTaskActivityId;
    }

    public void setSubProcessEndRejectToBeforeTaskActivityId(String subProcessEndRejectToBeforeTaskActivityId) {
        this.subProcessEndRejectToBeforeTaskActivityId = subProcessEndRejectToBeforeTaskActivityId;
    }

    public Boolean getMoveToCurrentActivityWhenReject() {
        return moveToCurrentActivityWhenReject;
    }

    public void setMoveToCurrentActivityWhenReject(Boolean moveToCurrentActivityWhenReject) {
        this.moveToCurrentActivityWhenReject = moveToCurrentActivityWhenReject;
    }

    public String getSubProcessLossStrategy() {
        return subProcessLossStrategy;
    }

    public void setSubProcessLossStrategy(String subProcessLossStrategy) {
        this.subProcessLossStrategy = subProcessLossStrategy;
    }

    public String getSubProcessSourceWorkflowId() {
        return subProcessSourceWorkflowId;
    }

    public void setSubProcessSourceWorkflowId(String subProcessSourceWorkflowId) {
        this.subProcessSourceWorkflowId = subProcessSourceWorkflowId;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getActivityInstanceId() {
        return activityInstanceId;
    }

    public void setActivityInstanceId(String activityInstanceId) {
        this.activityInstanceId = activityInstanceId;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public boolean isSubProcessTaskSkip() {
        return WorkflowConstants.SUB_PROCESS_TASK_SKIP.equals(getSubProcessLossStrategy());
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getWorkflowInstanceId() {
        return workflowInstanceId;
    }

    public void setWorkflowInstanceId(String workflowInstanceId) {
        this.workflowInstanceId = workflowInstanceId;
    }

    public WorkflowInstanceImpl getWorkflowInstanceImpl() {
        return workflowInstanceImpl;
    }

    public void setWorkflowInstanceImpl(WorkflowInstanceImpl workflowInstanceImpl) {
        this.workflowInstanceImpl = workflowInstanceImpl;
    }

    public ActivityInstanceImpl getActivityInstance() {
        return activityInstance;
    }

    public void setActivityInstance(ActivityInstanceImpl activityInstance) {
        this.activityInstance = activityInstance;
    }

    public SubProcessImpl getSubProcessImpl() {
        return subProcessImpl;
    }

    public void setSubProcessImpl(SubProcessImpl subProcessImpl) {
        this.subProcessImpl = subProcessImpl;
    }
}
