# iPaaS App All模块集成测试配置
spring:
  application:
    name: ipaas-app-all-test
  
  # Web相关配置
  web:
    resources:
      add-mappings: false
  
  # 禁用不必要的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# 日志配置
logging:
  level:
    com.fxiaoke.erpdss.ipaas: DEBUG
    org.springframework.web: DEBUG
    org.springframework.test: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 服务器配置
server:
  port: 0  # 随机端口，避免测试冲突

# 测试专用配置
test:
  mock:
    enabled: true
  integration:
    timeout: 30000
  modules:
    base:
      enabled: true
    web:
      enabled: true
  all:
    health-check:
      enabled: true
      endpoints:
        - "/base/check"
        - "/web/check"
