# Action组件开发指南

## 概述

本文档详细说明了如何在iPaaS流程引擎中开发新的Action组件，包括组件的创建、注册和集成测试。

## 架构概览

iPaaS流程引擎基于Effektif工作流引擎构建，Action组件采用以下架构：

```plantuml
@startuml
package "API层 (ipaas-api)" {
  abstract class AbsAction<T> {
    +id(String): T
    +name(String): T
    +description(String): T
    +transitionTo(String): T
    +transitionToNext(): T
  }
  
  class LogAction {
    -List<Binding<String>> logBindings
    +logValue(String): LogAction
    +logExpression(String): LogAction
    +logTemplate(String): LogAction
  }
  
  AbsAction <|-- LogAction
}

package "实现层 (ipaas-flow-engine)" {
  abstract class AbstractActivityType<T> {
    +parse(ActivityImpl, T, WorkflowParser): void
    +execute(ActivityInstanceImpl): void
  }
  
  class LogActionImpl {
    -List<BindingImpl<String>> logBindings
    +parse(): void
    +execute(): void
  }
  
  AbstractActivityType <|-- LogActionImpl
}

package "注册机制" {
  file "META-INF/services/ActivityType" {
    'LogActionImpl'
  }
}

LogAction ..> LogActionImpl : 实现
LogActionImpl ..> "META-INF/services" : 注册
@enduml
```

## 开发步骤

### 1. 创建API接口类

在 `modules/ipaas-api/src/main/java/com/fxiaoke/erpdss/ipaas/flow/activity/` 目录下创建Action接口类。

**关键要素：**
- 继承 `AbsAction<T>` 基类
- 使用 `@TypeName` 注解定义类型名称
- 使用 `@BpmnElement` 和 `@BpmnTypeAttribute` 注解支持BPMN
- 实现流式API方法

**示例代码：**
```java
@NoArgsConstructor
@Accessors(fluent = true)
@TypeName("yourAction")  // 定义组件类型名称
@BpmnElement("yourAction")
@BpmnTypeAttribute(attribute="type", value="your")
public class YourAction extends AbsAction<YourAction> {
    
    @Setter
    @Getter
    protected List<Binding<String>> yourBindings;
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    // 添加配置方法
    public YourAction addYourBinding(Binding<String> binding) {
        if (this.yourBindings == null) {
            this.yourBindings = new ArrayList<>();
        }
        this.yourBindings.add(binding);
        return this;
    }
    
    // 便捷方法
    public YourAction yourValue(String value) {
        return addYourBinding(Binding.ofValue(value));
    }
    
    public YourAction yourExpression(String expression) {
        return addYourBinding(Binding.ofExpression(expression));
    }
    
    public YourAction yourTemplate(String template) {
        return addYourBinding(Binding.ofTemplate(template));
    }
}
```

### 2. 创建实现类

在 `ipaas-flow-engine/src/main/java/com/fxiaoke/erpdss/ipaas/flow/activity/types/` 目录下创建实现类。

**关键要素：**
- 继承 `AbstractActivityType<T>` 基类
- 实现 `parse()` 方法解析配置
- 实现 `execute()` 方法执行逻辑

**示例代码：**
```java
@Slf4j
public class YourActionImpl extends AbstractActivityType<YourAction> {
    
    List<BindingImpl<String>> yourBindings;
    
    public YourActionImpl() {
        super(YourAction.class);
    }
    
    @Override
    public void parse(ActivityImpl activityImpl, YourAction activity, WorkflowParser parser) {
        super.parse(activityImpl, activity, parser);
        this.yourBindings = parser.parseBindings(activity.yourBindings(), "yourBindings");
    }
    
    @Override
    public void execute(ActivityInstanceImpl activityInstance) {
        if (yourBindings != null) {
            for (BindingImpl<String> binding : yourBindings) {
                String value = activityInstance.getValue(binding);
                // 实现具体的业务逻辑
                log.info("YourActionImpl.execute: {}", value);
                // TODO: 添加您的业务逻辑
            }
        }
    }
}
```

### 3. 注册组件

在 `ipaas-flow-engine/src/main/resources/META-INF/services/com.effektif.workflow.impl.activity.ActivityType` 文件中添加实现类的完整类名：

```
com.fxiaoke.erpdss.ipaas.flow.activity.types.LogActionImpl
com.fxiaoke.erpdss.ipaas.flow.activity.types.YourActionImpl
```

## 集成测试

### 测试文件命名规范

根据项目测试规范：
- 集成测试文件使用 `*IT.java` 命名
- 测试类放在 `ipaas-flow-engine/src/test/java/` 目录下

### 测试示例

创建 `YourActionIT.java` 集成测试：

```java
@ActiveProfiles("test")
@Slf4j
@SpringBootTest
public class YourActionIT {
    
    @Autowired
    private WorkflowEngine workflowEngine;
    
    @Test
    void testYourAction() {
        // 创建工作流
        ExecutableWorkflow workflow = new ExecutableWorkflow();
        workflow.name("test-your-action");
        workflow.description("测试您的Action组件");
        
        // 定义变量
        workflow.variable("input", new TextType());
        
        // 构建流程
        workflow.activity(new StartEvent().id("start").transitionToNext());
        workflow.activity(new YourAction()
            .id("yourAction")
            .transitionToNext()
            .yourTemplate("处理数据: {{input}}"));
        workflow.activity(new EndEvent().id("end"));
        
        // 部署工作流
        WorkflowId workflowId = workflowEngine
            .deployWorkflow(workflow)
            .checkNoErrorsAndNoWarnings()
            .getWorkflowId();
        
        // 启动工作流实例
        WorkflowInstance workflowInstance = workflowEngine
            .start(new TriggerInstance("")
                .workflowId(workflowId)
                .data("input", "测试数据"));
        
        // 验证执行结果
        assertThat(workflowInstance).isNotNull();
        assertThat(workflowInstance.isEnded()).isTrue();
    }
}
```

### 运行测试

使用Maven运行集成测试：

```bash
# 运行所有集成测试
mvn verify

# 运行特定的集成测试
mvn test -Dtest=YourActionIT
```

## 最佳实践

### 1. 错误处理
- 在 `execute()` 方法中添加适当的异常处理
- 使用日志记录关键信息和错误

### 2. 性能考虑
- 避免在 `execute()` 方法中进行耗时操作
- 考虑异步处理长时间运行的任务

### 3. 配置验证
- 在 `parse()` 方法中验证必要的配置参数
- 提供清晰的错误信息

### 4. 测试覆盖
- 编写单元测试覆盖核心逻辑
- 编写集成测试验证完整流程
- 测试异常情况和边界条件

## 常见问题

### Q: 组件没有被识别？
A: 检查以下几点：
1. 确认实现类已在 `META-INF/services` 文件中注册
2. 确认 `@TypeName` 注解值正确
3. 确认类路径配置正确

### Q: 绑定解析失败？
A: 检查：
1. `parse()` 方法中的绑定解析代码
2. API类中的属性名称与解析时使用的名称一致

### Q: 测试无法启动？
A: 确认：
1. 测试配置文件正确
2. 必要的依赖已注入
3. 测试环境配置正确

## 参考资料

- [LogAction示例](../ipaas-flow-engine/src/main/java/com/fxiaoke/erpdss/ipaas/flow/activity/types/LogActionImpl.java)
- [LogAction API](../modules/ipaas-api/src/main/java/com/fxiaoke/erpdss/ipaas/flow/activity/LogAction.java)
- [集成测试示例](../ipaas-flow-engine/src/test/java/com/fxiaoke/erpdss/ipaas/flow/LogActivityIT.java)
- [项目测试规范](../docs/testing-standards.md)
