package com.effektif.mongo;/* Copyright (c) 2015, Effektif GmbH.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License. */

import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.json.*;
import com.effektif.workflow.impl.json.types.AbstractTypeMapper;
import org.bson.types.ObjectId;

import java.lang.reflect.Type;

/**
 * zhenghaibo 2016/6/21
 */
public class TaskIdMongoMapper extends AbstractTypeMapper<TaskId> implements JsonTypeMapperFactory {

    @Override
    public JsonTypeMapper createTypeMapper(Type type, Class<?> clazz, Mappings mappings) {
        if (clazz == TaskId.class) {
            return this;
        }
        return null;
    }

    @Override
    public void write(TaskId objectValue, JsonWriter jsonWriter) {
        JsonObjectWriter jsonObjectWriter = (JsonObjectWriter) jsonWriter;
        jsonObjectWriter.writeValue(new ObjectId(objectValue.getInternal()));
    }

    @Override
    public TaskId read(Object jsonValue, JsonReader jsonReader) {
        return new TaskId(jsonValue.toString());
    }
}
