package com.effektif.workflow.impl.activity.types.userTask.handler.create;

import com.effektif.workflow.api.activities.ErrMsgI18N;
import com.effektif.workflow.api.activities.HandlerConfig;
import com.effektif.workflow.api.activities.UserTask;
import com.effektif.workflow.api.ext.AssigneeParserPojo;
import com.effektif.workflow.api.ext.ExecutionPojo;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.impl.activity.types.UserTaskImpl;
import com.effektif.workflow.impl.ext.Task;
import com.effektif.workflow.impl.ext.TaskConfigSupportPojo;
import com.effektif.workflow.impl.workflow.WorkflowImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.VariableInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import com.facishare.paas.workflow.bus.api.type.TaskState;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/1
 * @apiNote
 **/
@Slf4j
public class UserTaskCreateBpmHandler extends UserTaskCreateHandler {
    public static volatile String BPM_SERVICE_SUPPORT_CLASS_NAME = "com.facishare.paas.workflow.kernel.support.BPMServiceSupport";

    @Override
    @SneakyThrows
    public Task generateTask(UserTaskImpl userTaskImpl, ActivityInstanceImpl activityInstance, Task task) {

        WorkflowInstanceImpl workflowInstance = activityInstance.workflowInstance;
        UserTask userTaskActivityConfig = userTaskImpl.getActivity();

        Map<String, Object> variableMap = new HashMap<>();

        setTaskEntityIdAndObjectIdAndBpmExtension(workflowInstance, userTaskImpl, task, variableMap);
        setBpmTaskCandidateIds(userTaskImpl, activityInstance, task, variableMap);

        //对task后动作进行处理  --  pass有后动作存在，reject后动作不存在。但reject在库中为 reject[] 时. 获取task，会把paas的值复制到reject中。 导致reject时会执行pass的后动作
        Map<String, List<ExecutionPojo>> executions = task.getExecution();
        if (executions != null && !executions.isEmpty()) {
            task.setExecution(executions.keySet().stream()
                    .filter(key -> executions.get(key) != null && executions.get(key).size() > 0)
                    .collect(Collectors.toMap(key -> key, executions::get)));
        }

        task.setExternalApplyTask(userTaskActivityConfig.getExternalApplyTask());
        task.setAssignNextTask(userTaskActivityConfig.getAssignNextTask());
        task.setExternalFlow(workflowInstance.getExternalFlow());

        if (userTaskActivityConfig.getProperty(WorkflowBindingEnum.nextTaskAssigneeScope.toString()) != null &&
                userTaskActivityConfig.getProperty(WorkflowBindingEnum.nextTaskAssigneeScope.toString()) instanceof Map) {
            task.setNextTaskAssigneeScope((Map<String, List<String>>) userTaskActivityConfig.getProperty(WorkflowBindingEnum.nextTaskAssigneeScope.toString()));
        }

        //查询委托设置
        Consumer<Task> taskDelegateSetting = setDelegateCandidateAndLogs(task);

        //不是应用节点时，提示人员解析异常； 应用节点允许处理人为空
        if ((task.getExternalApplyTask() == null || task.getExternalApplyTask() == 0) &&
                (null == task.getCandidateIds() || task.getCandidateIds().size() < 1)) {
            task.setErrMsg("任务没有处理人");
            if(Objects.isNull(task.getErrMsgI18N())){
                task.setErrMsgI18N(Lists.newArrayList(ErrMsgI18N.create("engin_approval_task_not_person_check")));
            }
            task.setState(WorkflowConstants.UserTaskStatus.ERROR);
        }

        parseReminders(task, userTaskActivityConfig, variableMap);

        userTaskImpl.insertTask(task);

        taskDelegateSetting.accept(task);

        Class<?> bpmFlowServiceSupport = Class.forName(BPM_SERVICE_SUPPORT_CLASS_NAME);
        Method method = bpmFlowServiceSupport.getMethod("afterTaskInsert", String.class, String.class);
        method.invoke(bpmFlowServiceSupport.newInstance(), task.getTenantId(), task.getId().getInternal());

        return task;
    }

    /**
     * 获取业务流程的任务处理人
     */
    private Task setBpmTaskCandidateIds(UserTaskImpl userTaskImpl, ActivityInstanceImpl activityInstance, Task task, Map<String, Object> variableMap) {
        UserTask activity = userTaskImpl.getActivity();

        WorkflowImpl workflow = activityInstance.workflow;
        WorkflowInstanceImpl workflowInstance = activityInstance.workflowInstance;

        Map<String, List<String>> assigneeMap = activity.getAssignee();
        List<HandlerConfig> groupHandler = activity.getGroupHandler();
        List<String> candidateIds = new ArrayList();
        try {
            if (activity.isCustomCandidateConfig()) {
                return setCustomCandidateIds(task);
            }

            String applicantId = getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.applicantId.toString());
            String taskId = task.getId().toString();
            AssigneeParserPojo assigneeParserPojo = new AssigneeParserPojo()
                    .assigneeMap(assigneeMap)
                    .tenantId(workflow.getTenantId())
                    .appId(workflow.getAppId())
                    .type(workflow.getType())
                    .userId(applicantId)
                    .taskName(task.getName())
                    .taskId(taskId)
                    .activityId(activity.getId())
                    .workflowInstanceId(workflowInstance.getId().toString())
                    .groupHandler(groupHandler)
                    .submitter(Objects.nonNull(workflowInstance.transientProperties) ? (String) workflowInstance.transientProperties.get("submitter") : null);


            assigneeParserPojo.setApplicantId(applicantId);
            assigneeParserPojo.setWorkflowId(workflow.id.getInternal());

            //设置主要是为了调用后动作解析角色的人员
            if (task.isLinkAppEnable()) {
                //设置外部互联应用相关的属性
                assigneeParserPojo.setLinkAppId(task.getLinkApp());
                assigneeParserPojo.setLinkAppType(task.getLinkAppType());
            }

            assigneeParserPojo.setVariables(variableMap);
            assigneeParserPojo.setObjectId(task.getObjectId());
            assigneeParserPojo.setEntityId(task.getEntityId());
            assigneeParserPojo.bpmExtension(task.getBpmExtension());
            assigneeParserPojo.setAssigneeFunction(task.getAssigneeFunction());
            assigneeParserPojo.setAssigneeType(task.getAssigneeType());
            assigneeParserPojo.setSourceWorkflowId(task.getSourceWorkflowId());
            assigneeParserPojo.setWorkflowName(task.getWorkflowName());

            Class<?> userTaskSupport = Class.forName(USERTASK_SUPPORT_CLASS_NAME);
            Method parseMethod = userTaskSupport.getMethod("parseAssignee", AssigneeParserPojo.class);
            Map<String, Object> result = (Map<String, Object>) parseMethod.invoke(userTaskSupport.newInstance(), assigneeParserPojo);
            if(Objects.nonNull(result)){
                if(result.containsKey("users")){
                    candidateIds.addAll((Set<String>) result.get("users"));
                }
                if(result.containsKey("i18nError")){
                    task.setErrMsgI18N((List<ErrMsgI18N>) result.get("i18nError"));
                }
            }
        } catch (Exception e) {
            log.error("任务处理人解析失败", e);
        }
        task.setCandidateIds(new ArrayList(new HashSet(candidateIds)));
        return task;
    }

    private void setTaskEntityIdAndObjectIdAndBpmExtension(WorkflowInstanceImpl workflowInstance, UserTaskImpl userTaskImpl,
                                            Task task,
                                            Map<String, Object> variableMap) {
        Map<String, VariableInstanceImpl> variableInstancesMap = workflowInstance.variableInstancesMap;
        variableInstancesMap.keySet().forEach(key -> {
            if (null != variableInstancesMap.get(key) && null != variableInstancesMap.get(key).value) {
                variableMap.put(key, variableInstancesMap.get(key).value);
            }
        });
        UserTask userTaskActivityConfig = userTaskImpl.getActivity();
        TaskConfigSupportPojo.BpmEntityIdAndObjectId bpmEntityIdAndObjectId = userTaskImpl.getBpmEntityIdAndObjectId(userTaskActivityConfig.getProperties(), variableMap);
        task.setEntityId(bpmEntityIdAndObjectId.getEntityId());
        task.setObjectId(bpmEntityIdAndObjectId.getObjectId());
        task.setBpmExtension(bpmEntityIdAndObjectId.getBpmExtension());
        if (Strings.isNullOrEmpty(bpmEntityIdAndObjectId.getObjectId())) {
            task.setState(TaskState.error.name());
        } else {
            //设置当前节点的对象信息
            String currentObjectIdVariableIdKey = "activity_" + userTaskActivityConfig.getId() + "##" + task.getEntityId();
            variableMap.put(currentObjectIdVariableIdKey, task.getObjectId());
            if (variableInstancesMap.containsKey(currentObjectIdVariableIdKey)) {
                variableInstancesMap.get(currentObjectIdVariableIdKey).setValue(task.getObjectId());
            }
        }
    }

    @Override
    public String getType() {
        return WorkflowConstants.WorkflowType.BPM;
    }
}
