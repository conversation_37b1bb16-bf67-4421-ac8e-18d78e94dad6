package com.fxiaoke.erpdss.ipaas.flow.service;

import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.query.WorkflowQuery;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.fxiaoke.erpdss.ipaas.common.module.Result;

import java.util.List;

/**
 * 流程管理服务
 *
 * <AUTHOR> (^_−)☆
 */
public interface FlowManagementService {

    /**
     * 发布流程
     *
     * @param workflow
     * @return
     */
    Result<ExecutableWorkflow> deployFlow(ExecutableWorkflow workflow);

    /**
     * 更新流程
     * @param workflow
     * @return
     */
    Result<ExecutableWorkflow> updateFlow(ExecutableWorkflow workflow);


    Result<WorkflowInstance> start(TriggerInstance triggerInstance);

    /**
     * 根据Id查找流程
     *
     * @param tenantId
     * @param workflowQuery
     * @return
     */
    Result<List<ExecutableWorkflow>> findWorkflows(String tenantId, WorkflowQuery workflowQuery);

    /**
     * 根据Id查找流程
     * @param tenantId
     * @param workflowSource
     * @return
     */
    Result<ExecutableWorkflow> getFlow(String tenantId, String workflowSource);


}
