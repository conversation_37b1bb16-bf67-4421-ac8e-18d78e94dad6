package com.fxiaoke.erpdss.ipaas.springcommon.aspect;

import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSBizException;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSSystemException;
import com.fxiaoke.erpdss.ipaas.common.module.Result;
import com.fxiaoke.erpdss.ipaas.springcommon.annotation.ServiceLog;
import com.fxiaoke.erpdss.ipaas.springcommon.constans.CommonLabel;
import org.springframework.stereotype.Service;

/**
 * 测试Service类
 * <p>
 * 用于测试ServiceLogAspect切面功能
 * 
 * <AUTHOR> (^_−)☆
 */
@Service
public class TestService {

    /**
     * 正常执行的方法
     */
    @ServiceLog("测试正常执行")
    public String normalMethod(String input) {
        return "处理结果: " + input;
    }

    /**
     * 抛出业务异常的方法
     */
    @ServiceLog("测试业务异常")
    public String bizExceptionMethod(String input) {
        throw new IPaaSBizException("业务处理失败: " + input);
    }

    /**
     * 抛出系统异常的方法
     */
    @ServiceLog("测试系统异常")
    public String systemExceptionMethod(String input) {
        throw new IPaaSSystemException("系统处理失败: " + input);
    }

    /**
     * 抛出其他异常的方法
     */
    @ServiceLog("测试其他异常")
    public String otherExceptionMethod(String input) {
        throw new RuntimeException("运行时异常: " + input);
    }

    /**
     * 不记录参数的方法
     */
    @ServiceLog(value = "不记录参数", logParams = false)
    public String noParamsLogMethod(String sensitiveData) {
        return "处理完成";
    }

    /**
     * 不记录返回值的方法
     */
    @ServiceLog(value = "不记录返回值", logResult = false)
    public String noResultLogMethod(String input) {
        return "敏感返回数据: " + input;
    }

    /**
     * 不记录执行时间的方法
     */
    @ServiceLog(value = "不记录执行时间", logExecutionTime = false)
    public String noTimeLogMethod(String input) {
        return "快速处理: " + input;
    }

    /**
     * 没有注解的Service方法（应该被切面拦截）
     */
    public String noAnnotationMethod(String input) {
        return "无注解方法: " + input;
    }

    /**
     * 私有方法（不应该被切面拦截）
     */
    private String privateMethod(String input) {
        return "私有方法: " + input;
    }

    // ========== 返回Result类型的方法（测试异常转换功能） ==========

    /**
     * 返回Result类型的正常方法
     */
    @ServiceLog("返回Result的正常方法")
    public Result<String> resultNormalMethod(String input) {
        return Result.success("处理结果: " + input);
    }

    /**
     * 返回Result类型但抛出业务异常的方法
     */
    @ServiceLog("返回Result但抛出业务异常")
    public Result<String> resultBizExceptionMethod(String input) {
        throw new IPaaSBizException("业务处理失败: " + input);
    }

    /**
     * 返回Result类型但抛出系统异常的方法
     */
    @ServiceLog("返回Result但抛出系统异常")
    public Result<String> resultSystemExceptionMethod(String input) {
        throw new IPaaSSystemException("系统处理失败: " + input);
    }

    /**
     * 返回Result类型但抛出其他异常的方法
     */
    @ServiceLog("返回Result但抛出其他异常")
    public Result<String> resultOtherExceptionMethod(String input) {
        throw new RuntimeException("运行时异常: " + input);
    }
}
