# Effektif 工作流引擎生命周期分析

## 概述

本文档详细分析了 effektif-parent/effektif-workflow-impl 模块中工作流的完整生命周期，包括工作流定义、实例创建、执行、状态管理和结束清理等各个阶段。

## 1. Memory 存储实现概览

### 1.1 核心存储类

在 `com.effektif.workflow.impl.memory` 包中包含以下9个核心类：

- **MemoryConfiguration**: 内存工作流引擎配置类
- **MemoryWorkflowStore**: 工作流定义存储
- **MemoryWorkflowInstanceStore**: 工作流实例存储
- **MemoryJobStore**: 作业调度存储
- **MemoryTaskStore**: 用户任务存储
- **MemoryAutoTaskStore**: 自动任务存储
- **MemorySubProcessTaskStore**: 子流程任务存储
- **MemoryBlockTaskStore**: 阻塞任务存储
- **TestConfiguration**: 测试配置类

### 1.2 存储架构

```java
// MemoryConfiguration 注册所有存储组件
public class MemoryConfiguration extends DefaultConfiguration {
  public MemoryConfiguration() {
    brewery.ingredient(new MemoryWorkflowStore());
    brewery.ingredient(new MemoryWorkflowInstanceStore());
    brewery.ingredient(new MemoryJobStore());
    brewery.ingredient(new MemoryTaskStore());
    brewery.ingredient(new MemoryAutoTaskStore());
    brewery.ingredient(new MemorySubProcessTaskStore());
    brewery.ingredient(new MemoryBlockTaskStore());
    brewery.ingredient(new JobServiceImpl());
  }
}
```

## 2. 工作流生命周期详细分析

### 2.1 工作流定义阶段

#### 工作流部署 (Deployment)

```java
@Override
public Deployment deployWorkflow(ExecutableWorkflow workflow) {
  WorkflowParser parser = new WorkflowParser(configuration);
  parser.parse(workflow);
  
  if (!parser.hasErrors()) {
    WorkflowId workflowId = workflowStore.generateWorkflowId();
    workflow.setId(workflowId);
    workflow.setCreateTime(System.currentTimeMillis());
    workflowStore.insertWorkflow(workflow);
    workflowCache.put(workflowImpl);
  }
}
```

**部署阶段包括**：

- 工作流定义解析和验证
- 生成唯一的工作流ID
- 存储到工作流存储器
- 缓存编译后的工作流实现

### 2.2 工作流实例生命周期

#### 实例创建和初始化

```java
public WorkflowInstanceImpl startInitialize(TriggerInstance triggerInstance) {
  WorkflowId workflowId = getLatestWorkflowId(triggerInstance);
  WorkflowImpl workflow = getWorkflowImpl(triggerInstance.getTenantId(), workflowId);
  
  WorkflowInstanceId workflowInstanceId = workflowInstanceStore.generateWorkflowInstanceId();
  WorkflowInstanceImpl workflowInstance = new WorkflowInstanceImpl(
    configuration, workflow, workflowInstanceId, triggerInstance, lock, 
    triggerInstance.getTransientData());
  
  notifyWorkflowInstanceStarted(workflowInstance);
  return workflowInstance;
}
```

#### 实例启动和执行

```java
public WorkflowInstance startExecute(WorkflowInstanceImpl workflowInstance) {
  if (workflow.startActivities != null) {
    for (ActivityImpl startActivityDefinition : workflow.startActivities) {
      workflowInstance.execute(startActivityDefinition);
    }
  }
  workflowInstance.executeWork();
  return workflowInstance.toWorkflowInstance();
}
```

## 3. 工作流实例状态定义

### 3.1 主要状态枚举

```java
interface InstanceStatus {
  String IN_PROGRESS = "in_progress";  // 进行中
  String PASS = "pass";               // 通过
  String REJECT = "reject";           // 驳回
  String CANCEL = "cancel";           // 取消
  String ERROR = "error";             // 错误
  String DESTROY = "destroy";         // 销毁
}

interface InstanceSubState {
  String NORMAL = "normal";                                    // 正常
  String BUILD_TASK_EXCEPTION = "BuildTaskException";         // 构建任务异常
  String ANALYZE_PERSON_EXCEPTION = "AnalyzePersonException"; // 分析人员异常
  String CALL_BACK_EXCEPTION = "CallBackException";           // 回调异常
  String CALL_BACK_WAITING = "CallBackWaiting";              // 回调等待
}

interface EndState{
  String EXCLUSIVE_GATEWAY_LOOP = "exclusiveGatewayLoop"; // 排他网关循环
}
```

### 3.2 用户任务状态

```java
interface UserTaskStatus {
  String IN_PROGRESS = "in_progress";  // 进行中
  String PASS = "pass";               // 通过
  String AUTO_PASS = "auto_pass";     // 自动通过
  String REJECT = "reject";           // 驳回
  String CANCEL = "cancel";           // 取消
  String GO_BACK = "go_back";         // 回退
  String AUTO_GO_BACK = "auto_go_back"; // 自动回退
  String SCHEDULE = "schedule";       // 调度
  String ERROR = "error";             // 错误
  String TAG_WAITING = "tag_waiting"; // 标签等待
  String RETRIEVE = "retrieve";       // 撤回
}
```

## 4. 活动实例状态管理

### 4.1 活动实例工作状态

```java
public static final String STATE_STARTING = "starting";
public static final String STATE_STARTING_MULTI_CONTAINER = "startingMultiParent";
public static final String STATE_STARTING_MULTI_INSTANCE = "startingMultiInstance";
public static final String STATE_PROPAGATE_TO_PARENT = "propagateToParent";
public static final String STATE_JOINING = "joining";
```

### 4.2 活动实例执行流程

```java
public void executeWork() {
  while (hasWork()) {
    ActivityInstanceImpl activityInstance = getNextWork();
  
    if (STATE_STARTING.equals(activityInstance.workState)) {
      activityInstance.execute();
    } else if (STATE_PROPAGATE_TO_PARENT.equals(activityInstance.workState)) {
      activityInstance.propagateToParent();
    }
  }
}
```

## 5. 工作流生命周期状态转换图

[![工作流生命周期状态转换图](https://img.plantuml.biz/plantuml/svg/RPNVJXD15CRlzobMBaru0IyCf3PAeeG0aGlZnMOxb0tBjjdT4dj74RI5QeibY5W4exM4fAqXKfH_landjdqBPsTcP-Ui98ITC-SttvapCrywuVc6wnTMRErU1YrQ3j9dqwdvoUd7JzEwQHkUfscKy94-XYzQWvicEI_Xxi7WJsLCDppz4LiI4QFN9y7DsUYqEkoLH3flvufHEhXkuCv--BT-mj8fmroIYuVj7xYsW-izlCMpywwLpI8thb_-ZAykSVVBu7QRBtSHJKmvDE-OACOESNiRL_TmRN_qjiR8ASVodnkk9nbMXVowYchDXOSmQVhMgkKNPKLIQG-_LddV34k_GcR1HmivTraoJ8JlIjbyfCM48SjL0ILqavfLKgwJRslag0Ner18ob-KcWqu5tsu6MtsonWzzmR3ykJ0dvTwto6wVzCkar6F4B3AHjOgU8SypiX8RN1-J3nrS_XcK-y5bcv4pRYvlP6bwnd2HuyTtSRrEEYtn8fGTUaN7vDi3eDfoY2JwnVLggCRwvTSHokHMyZQYaAJO6GTN3N9ufDHCEnbGaHoiurAJdmeNAtXvv6IWZ59C5efUyTf1SDJYTHWmdVEjnM8wbY2_DuFwc-7v1haK4ZOod48-tcfjBppknhleuQJ25shBfzwz3Mww758kRWxvd0EtDzhzDEpryFSDMf8txBevzudXP6m41Ja91HdCzRGNzr_gu-CF8zFoZvXXFkKVDiEjoVwpM9fH33W43GX3HaZZHGCsct0R21cW-amPinnq5eX0FdfZYHWmmaaWP83qaFv09yrJ_bgqU3Pa4Yw2uNzjt54aOIvQ2b-SYLB9N4AEiiE_-wHPuHQy4u_D63RnyLYWcZ8liUXmmcuhghara6QN9-oKr54zAmggEY3DkebygqKZLPwVEpSX19GQzBW4eG7fsB1on9SAcuelcwEceAkPxNXzQ48O0XLDCMAqLB4RYE322TeW_SNz1m00)](https://editor.plantuml.com/uml/RPNVJXD15CRlzobMBaru0IyCf3PAeeG0aGlZnMOxb0tBjjdT4dj74RI5QeibY5W4exM4fAqXKfH_landjdqBPsTcP-Ui98ITC-SttvapCrywuVc6wnTMRErU1YrQ3j9dqwdvoUd7JzEwQHkUfscKy94-XYzQWvicEI_Xxi7WJsLCDppz4LiI4QFN9y7DsUYqEkoLH3flvufHEhXkuCv--BT-mj8fmroIYuVj7xYsW-izlCMpywwLpI8thb_-ZAykSVVBu7QRBtSHJKmvDE-OACOESNiRL_TmRN_qjiR8ASVodnkk9nbMXVowYchDXOSmQVhMgkKNPKLIQG-_LddV34k_GcR1HmivTraoJ8JlIjbyfCM48SjL0ILqavfLKgwJRslag0Ner18ob-KcWqu5tsu6MtsonWzzmR3ykJ0dvTwto6wVzCkar6F4B3AHjOgU8SypiX8RN1-J3nrS_XcK-y5bcv4pRYvlP6bwnd2HuyTtSRrEEYtn8fGTUaN7vDi3eDfoY2JwnVLggCRwvTSHokHMyZQYaAJO6GTN3N9ufDHCEnbGaHoiurAJdmeNAtXvv6IWZ59C5efUyTf1SDJYTHWmdVEjnM8wbY2_DuFwc-7v1haK4ZOod48-tcfjBppknhleuQJ25shBfzwz3Mww758kRWxvd0EtDzhzDEpryFSDMf8txBevzudXP6m41Ja91HdCzRGNzr_gu-CF8zFoZvXXFkKVDiEjoVwpM9fH33W43GX3HaZZHGCsct0R21cW-amPinnq5eX0FdfZYHWmmaaWP83qaFv09yrJ_bgqU3Pa4Yw2uNzjt54aOIvQ2b-SYLB9N4AEiiE_-wHPuHQy4u_D63RnyLYWcZ8liUXmmcuhghara6QN9-oKr54zAmggEY3DkebygqKZLPwVEpSX19GQzBW4eG7fsB1on9SAcuelcwEceAkPxNXzQ48O0XLDCMAqLB4RYE322TeW_SNz1m00)

```plantuml
@startuml
!define RECTANGLE class

state "工作流定义" as Define
state "部署验证" as Deploy
state "缓存存储" as Cache
state "触发启动" as Trigger

state "创建实例" as CreateInstance
state "初始化变量" as InitVars
state "启动活动" as StartActivity

state "执行工作" as ExecuteWork
state "活动实例启动" as ActivityStart
state "活动执行" as ActivityExecute
state "活动完成检查" as ActivityCheck

state "等待状态" as WaitState
state "接收消息" as ReceiveMessage
state "传播到父级" as PropagateParent

state "异步工作检查" as AsyncCheck
state "异步执行" as AsyncExecute
state "同步完成" as SyncComplete

state "实例结束检查" as EndCheck
state "暂停等待" as Suspend
state "实例完成" as Complete
state "通知结束" as NotifyEnd
state "清理资源" as Cleanup

state "取消操作" as Cancel
state "设置取消状态" as SetCancelState
state "错误处理" as ErrorHandle
state "设置错误状态" as SetErrorState

[*] --> Define
Define --> Deploy
Deploy --> Cache
Cache --> Trigger

Trigger --> CreateInstance
CreateInstance --> InitVars
InitVars --> StartActivity

StartActivity --> ExecuteWork
ExecuteWork --> ActivityStart
ActivityStart --> ActivityExecute
ActivityExecute --> ActivityCheck

ActivityCheck --> WaitState : 未完成
WaitState --> ReceiveMessage
ReceiveMessage --> ExecuteWork

ActivityCheck --> PropagateParent : 已完成
PropagateParent --> ExecuteWork : 还有工作
PropagateParent --> AsyncCheck : 无工作

AsyncCheck --> AsyncExecute : 有异步工作
AsyncExecute --> Suspend

AsyncCheck --> SyncComplete : 无异步工作
SyncComplete --> EndCheck

EndCheck --> Suspend : 未结束
EndCheck --> Complete : 已结束

Complete --> NotifyEnd
NotifyEnd --> Cleanup
Cleanup --> [*]

Cancel --> SetCancelState
SetCancelState --> Cleanup

ErrorHandle --> SetErrorState
SetErrorState --> Cleanup

@enduml
```

## 6. 实例锁定和并发控制

### 6.1 实例锁定机制

```java
public synchronized void lockWorkflowInstance(WorkflowInstanceImpl workflowInstance) {
  WorkflowInstanceId workflowInstanceId = workflowInstance.getId();
  if (lockedWorkflowInstanceIds.contains(workflowInstanceId)) {
    throw new RuntimeException("Process instance "+workflowInstanceId+" is already locked");
  }
  lockedWorkflowInstanceIds.add(workflowInstanceId);
  LockImpl lock = new LockImpl();
  lock.setTime(Time.now());
  lock.setOwner(workflowEngineId);
  workflowInstance.setLock(lock);
}
```

### 6.2 实例解锁和刷新

```java
@Override
public void flushAndUnlock(WorkflowInstanceImpl workflowInstance) {
  lockedWorkflowInstanceIds.remove(workflowInstance.id);
  workflowInstance.removeLock();
  workflowInstance.notifyUnlockListeners();
}
```

### 6.3 并发控制特性

- **线程安全**: 使用 `ConcurrentHashMap` 和 `synchronized` 方法
- **锁定机制**: 防止同一实例的并发修改
- **解锁通知**: 支持解锁监听器机制
- **重试机制**: 锁定冲突时的重试策略

## 7. 实例结束和清理

### 7.1 正常结束流程

```java
public void endAndPropagateToParent() {
  if (this.end == null) {
    if (hasOpenActivityInstances()) {
      throw new RuntimeException("Can't end this process instance. There are open activity instances: " + this);
    }
    setEnd(System.currentTimeMillis());
    workflowInstanceEnded();
  }
}

public void workflowInstanceEnded() {
  workflow.workflowEngine.notifyWorkflowInstanceEnded(workflowInstance);
  destroyScopeInstance();
}
```

### 7.2 取消操作

```java
public void cancel() {
  super.cancel();
  if (updates != null) {
    getUpdates().isEndStateChanged = true;
    getUpdates().isEndChanged = true;
    WorkflowInstanceStore workflowInstanceStore = configuration.get(WorkflowInstanceStore.class);
    workflowInstanceStore.flushAndUnlock(this);
  }
}
```

### 7.3 错误处理

工作流引擎提供了完善的错误处理机制：

- **异常状态**: 通过 `InstanceSubState` 记录具体异常类型
- **错误恢复**: 支持从错误状态恢复执行
- **资源清理**: 确保异常情况下的资源正确释放

## 8. 存储策略对比

### 8.1 Memory vs MongoDB 存储


| 特性         | Memory 存储        | MongoDB 存储      |
| ------------ | ------------------ | ----------------- |
| **性能**     | 高性能，内存访问   | 中等性能，磁盘I/O |
| **持久化**   | 不持久化，重启丢失 | 持久化存储        |
| **扩展性**   | 单机内存限制       | 支持分布式        |
| **适用场景** | 开发测试、小规模   | 生产环境、大规模  |
| **数据安全** | 数据易丢失         | 数据安全可靠      |

### 8.2 混合存储策略

```java
// MongoMemoryConfiguration - 混合模式
public class MongoMemoryConfiguration extends MongoConfiguration {
  public MongoMemoryConfiguration() {
    brewery.ingredient(new MemoryWorkflowStore()); // 工作流定义用内存
    // 其他存储用MongoDB
  }
}
```

## 9. 生命周期总结

### 9.1 主要阶段

1. **定义阶段**: 工作流设计、解析、验证、部署
2. **实例化阶段**: 触发启动、创建实例、初始化数据
3. **执行阶段**: 活动执行、状态转换、消息传递
4. **等待阶段**: 用户任务、定时器、外部事件
5. **完成阶段**: 正常结束、异常终止、资源清理

### 9.2 关键特性

- **并发控制**: 通过实例锁定机制保证线程安全
- **异步执行**: 支持同步和异步两种执行模式
- **状态管理**: 完整的状态枚举和转换机制
- **错误处理**: 异常状态和恢复机制
- **资源管理**: 自动的资源分配和清理

### 9.3 设计优势

- **模块化设计**: 清晰的存储层抽象和接口定义
- **可扩展性**: 支持多种存储策略的插拔式架构
- **高可靠性**: 完善的锁定机制和错误处理
- **易测试性**: 专门的测试配置和内存存储
- **高性能**: 优化的内存存储和异步执行机制

这个生命周期设计确保了工作流实例能够可靠、高效地执行，同时提供了丰富的状态信息和控制机制，满足了企业级工作流引擎的需求。
