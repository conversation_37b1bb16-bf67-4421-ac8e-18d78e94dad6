import com.google.common.collect.Lists;
import com.mongodb.BasicDBObjectBuilder;
import com.mongodb.DBObject;

import java.util.Date;

public class Temp {

  public static void main(String[] args) {
    Date lastExpiredDate = new Date(System.currentTimeMillis() - 5L*60L*1000L);


    DBObject filter1 = BasicDBObjectBuilder.start().push("lock").add("$exists", false).pop().get();
    DBObject filter2 = BasicDBObjectBuilder.start().push("lock").push("time").add("$lte", lastExpiredDate).pop().pop().get();
    DBObject query = BasicDBObjectBuilder.start().add("$or", Lists.newArrayList(filter1, filter2)).get();
                                         

    System.out.println(query.toString());

  }
  
  
}
