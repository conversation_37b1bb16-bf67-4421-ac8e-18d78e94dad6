package com.fxiaoke.erpdss.ipaas.common.i18n;

import java.text.MessageFormat;

/**
 * 统一的工具类
 * 都放到一个里面ide加载性能很差
 *
 * <AUTHOR> (^_−)☆
 */
public interface I18nBase {
    String commonI18nKeyPrefix = "erpdss.ipaas";


    /**
     * 国际码，业务代码不应该调用，只在子类实现
     */
    String _GetI18nKey();

    /**
     * 默认值，业务代码不应该调用，只在子类实现
     */
    String _GetDefaultMsg();

    /**
     * 获取文本，I18N转换的结果！<br>
     * 使用 {@link MessageFormat} 对国际化场景更合理
     * <br/>
     * 请注意，不是使用的%s这种java语法，这个在不同语言翻译难保证顺序！！！<br>
     * 通常使用：当value为 "this is {1} for {0}"，format( "a", "b") =》 this is b for a<br>
     */
    default String getI18nMsg(Object... args) {
        return I18nUtil.get(_GetI18nKey(), _GetDefaultMsg(), args);
    }
}
