package com.fxiaoke.erpdss.ipaas.springcommon.aspect;

import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSBizException;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSSystemException;
import com.fxiaoke.erpdss.ipaas.common.module.Result;
import com.fxiaoke.erpdss.ipaas.springcommon.annotation.ServiceLog;
import com.fxiaoke.erpdss.ipaas.springcommon.constans.CommonLabel;
import com.fxiaoke.erpdss.ipaas.springcommon.util.JsonUtil;
import com.fxiaoke.erpdss.ipaas.springcommon.util.TraceUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * Service层日志切面
 * <p>
 * 功能：
 * 1. 执行方法后统一打印日志
 * 2. 异常处理：
 *    - IPaaSBizException：不另外打印日志，返回统一Result
 *    - IPaaSSystemException：打印error日志和异常堆栈，返回统一Result
 *    - 其他异常：包装为IPaaSSystemException处理
 * <p>
 * 拦截规则：
 * 1. 带有@ServiceLog注解的方法
 * 2. com.fxiaoke.erpdss.ipaas包下以"Service"结尾的类的public方法
 *
 * <AUTHOR> (^_−)☆
 */
@Aspect
@Component
@Slf4j
public class ServiceLogAspect {

    /**
     * 切点：匹配所有带有@ServiceLog注解的方法
     */
    @Pointcut("@annotation(com.fxiaoke.erpdss.ipaas.springcommon.annotation.ServiceLog)")
    public void serviceLogAnnotation() {}

    /**
     * 切点：匹配 com.fxiaoke.erpdss.ipaas 包下所有以Service结尾的类的public方法
     */
    @Pointcut("execution(public * com.fxiaoke.erpdss.ipaas..*Service.*(..))")
    public void serviceMethod() {}

    /**
     * 组合切点：匹配带有@ServiceLog注解的方法，或者指定包下的Service类的方法
     */
    @Pointcut("serviceLogAnnotation() || serviceMethod()")
    public void serviceLogPointcut() {}

    /**
     * 环绕通知：处理Service方法的日志记录和异常处理
     */
    @Around("serviceLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String traceId = TraceUtil.getTraceId();

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = method.getName();
        Object[] args = joinPoint.getArgs();

        // 检查方法返回类型是否为Result类型
        Class<?> returnType = method.getReturnType();
        boolean isResultReturnType = Result.class.isAssignableFrom(returnType);

        // 获取@ServiceLog注解配置
        ServiceLog serviceLog = AnnotationUtils.findAnnotation(method, ServiceLog.class);
        if (serviceLog == null) {
            serviceLog = AnnotationUtils.findAnnotation(joinPoint.getTarget().getClass(), ServiceLog.class);
        }

        // 记录方法开始执行日志
        logMethodStart(className, methodName, args, serviceLog, traceId);

        try {
            // 执行目标方法
            Object result = joinPoint.proceed();

            // 记录方法执行成功日志
            long executionTime = System.currentTimeMillis() - startTime;
            logMethodSuccess(className, methodName, result, executionTime, serviceLog, traceId);

            return result;

        } catch (IPaaSBizException e) {
            // 业务异常处理
            long executionTime = System.currentTimeMillis() - startTime;
            logBizException(className, methodName, e, executionTime, traceId);

            // 只有当方法返回类型是Result时才返回Result，否则重新抛出异常
            if (isResultReturnType) {
                return Result.error(e);
            } else {
                throw e;
            }

        } catch (IPaaSSystemException e) {
            // 系统异常处理
            long executionTime = System.currentTimeMillis() - startTime;
            logSystemException(className, methodName, e, executionTime, traceId);

            // 只有当方法返回类型是Result时才返回Result，否则重新抛出异常
            if (isResultReturnType) {
                return Result.error(e);
            } else {
                throw e;
            }

        } catch (Exception e) {
            // 其他异常处理
            long executionTime = System.currentTimeMillis() - startTime;
            IPaaSSystemException systemException = new IPaaSSystemException(CommonLabel.systemInternalError.getI18nMsg(), e);
            logSystemException(className, methodName, systemException, executionTime, traceId);

            // 只有当方法返回类型是Result时才返回Result，否则重新抛出异常
            if (isResultReturnType) {
                return Result.error(systemException);
            } else {
                throw systemException;
            }
        }
    }

    /**
     * 记录方法开始执行日志
     */
    private void logMethodStart(String className, String methodName, Object[] args,
                               ServiceLog serviceLog, String traceId) {
        if (serviceLog != null && !serviceLog.logParams()) {
            log.info("{}.{} started", className, methodName);
            return;
        }

        try {
            String argsJson = JsonUtil.toJsonSafe(args);
            log.info("{}.{} started with params: {}", className, methodName, argsJson);
        } catch (Exception e) {
            log.info("{}.{} started, params serialization failed", className, methodName);
        }
    }

    /**
     * 记录方法执行成功日志
     */
    private void logMethodSuccess(String className, String methodName, Object result,
                                 long executionTime, ServiceLog serviceLog, String traceId) {
        boolean logResult = serviceLog == null || serviceLog.logResult();
        boolean logTime = serviceLog == null || serviceLog.logExecutionTime();

        if (!logResult && !logTime) {
            log.info("{}.{} completed successfully", className, methodName);
            return;
        }

        StringBuilder logMsg = new StringBuilder();
        logMsg.append(String.format("%s.%s completed successfully", className, methodName));

        if (logTime) {
            logMsg.append(String.format(", execution time: %dms", executionTime));
        }

        if (logResult) {
            try {
                String resultJson = JsonUtil.toJsonSafe(result);
                logMsg.append(String.format(", result: %s", resultJson));
            } catch (Exception e) {
                logMsg.append(", result serialization failed");
            }
        }

        log.info(logMsg.toString());
    }

    /**
     * 记录业务异常日志
     */
    private void logBizException(String className, String methodName, IPaaSBizException e,
                                long executionTime, String traceId) {
        // 业务异常只记录简单的info日志，不打印堆栈
        log.info("{}.{} failed, execution time: {}ms, business exception: [{}] {}",
                className, methodName, executionTime, e.getCode(), e.getMessage());
    }

    /**
     * 记录系统异常日志
     */
    private void logSystemException(String className, String methodName, IPaaSSystemException e,
                                   long executionTime, String traceId) {
        // 系统异常打印error日志和完整堆栈
        log.error("{}.{} failed, execution time: {}ms, system exception: [{}] {}",
                 className, methodName, executionTime, e.getCode(), e.getMessage(), e);
    }
}
