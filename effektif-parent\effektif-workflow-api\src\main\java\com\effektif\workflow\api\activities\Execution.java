package com.effektif.workflow.api.activities;

import com.effektif.workflow.api.ext.ExecutionPojo;

import java.util.List;

/**
 * @Description :
 * <AUTHOR> cuiyongxu
 * @Date : 2022/2/18-2:02 PM
 **/
public class Execution {

    protected String id;
    protected List<ExecutionPojo> execution;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<ExecutionPojo> getExecution() {
        return execution;
    }

    public void setExecution(List<ExecutionPojo> execution) {
        this.execution = execution;
    }
}
