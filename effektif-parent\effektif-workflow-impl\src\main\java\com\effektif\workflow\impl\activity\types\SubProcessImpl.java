/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.impl.activity.types;

import com.effektif.workflow.api.Configuration;
import com.effektif.workflow.api.activities.SubProcess;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.model.*;
import com.effektif.workflow.api.workflow.Binding;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflow.Variable;
import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.WorkflowStore;
import com.effektif.workflow.impl.data.TypedValueImpl;
import com.effektif.workflow.impl.ext.SubProcessTask;
import com.effektif.workflow.impl.ext.SubProcessTaskStore;
import com.effektif.workflow.impl.ext.SubProcessTrigger;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflow.BindingImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.VariableInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import com.facishare.paas.workflow.bus.EngineEventBus;
import com.facishare.paas.workflow.bus.api.InstanceEndEvent;
import com.facishare.paas.workflow.bus.api.TaskHandledEvent;
import com.facishare.paas.workflow.bus.api.type.FlowTag;
import com.facishare.paas.workflow.bus.api.type.TaskState;
import com.facishare.paas.workflow.bus.model.MQContext;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.Method;
import java.util.*;


/**
 * <AUTHOR> Baeyens
 */
public class SubProcessImpl extends AbstractBindableActivityImpl<SubProcess> {

    private String SUB_PROCESS_TASK_NEW_SUPPORT= "com.facishare.paas.workflow.kernel.support.SubProcessTaskSupport";
    protected SubProcessTaskStore taskStore;
    // IDEA Boolean waitTillSubWorkflowEnds; add a configuration property to specify if this is fire-and-forget or wait-till-subworkflow-ends
    protected String subWorkflowSourceId;



    public SubProcessImpl() {
        super(SubProcess.class);
    }

    public SubProcessImpl(Class<SubProcess> activityApiClass) {
        super(activityApiClass);
    }

    @Override
    public void parse(ActivityImpl activityImpl, SubProcess subProcess, WorkflowParser parser) {
        super.parse(activityImpl, subProcess, parser);
        this.taskStore = parser.getConfiguration(SubProcessTaskStore.class);

        String tenantId = activityImpl.getWorkflow().getTenantId();
        //只会有sourceWorkflowId
        this.subWorkflowSourceId = subProcess.getSourceWorkflowId();

        Configuration configuration = activityImpl.getConfiguration();
        WorkflowStore workflowStore = configuration.get(WorkflowStore.class);

        ExecutableWorkflow subProcessExecutableWorkflow = workflowStore.findLatestWorkflowBySource(tenantId, subWorkflowSourceId);
        //如果查询到了子流程的workflowId
        if (Objects.nonNull(subProcessExecutableWorkflow)) {

            List<Variable> subWorkflowVariables = subProcessExecutableWorkflow.getVariables();
            Map<String, Binding> inputBindingsApi = subProcess.getSubWorkflowInputs();
            if (subWorkflowVariables != null && inputBindingsApi != null) {
                for (Variable subWorkflowVariable : subWorkflowVariables) {
                    String subWorkflowVariableId = subWorkflowVariable.getId();
                    Binding inputBindingApi = inputBindingsApi.get(subWorkflowVariableId);
                    parser.pushContext("inputBindings[" + subWorkflowVariableId + "]", inputBindingApi, null, null);
                    BindingImpl<?> bindingImpl = parser.parseBinding(inputBindingApi, subWorkflowVariableId, false, subWorkflowVariable.getType());
                    if (bindingImpl != null) {
                        if (inputBindings == null) {
                            inputBindings = new HashMap<>();
                        }
                        inputBindings.put(subWorkflowVariableId, bindingImpl);
                    }
                    parser.popContext();
                }
            }
        }
    }

    @Override
    public void execute(ActivityInstanceImpl activityInstance) {
        try {
            SubProcessTrigger subProcessTrigger = SubProcessTrigger.create(activityInstance, this);
            Class<?> subProcessTaskSupportClassBean = Class.forName(SUB_PROCESS_TASK_NEW_SUPPORT);
            Object subProcessTaskSupport = subProcessTaskSupportClassBean.newInstance();
            Method method = subProcessTaskSupportClassBean.getMethod("execute", SubProcessTrigger.class);
            method.invoke(subProcessTaskSupport, subProcessTrigger);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 子流程结束后回调,需要驱动主流程继续向下流转
     * 需要将子流程的处理结果传递给主流程
     */
    public void calledWorkflowInstanceEnded(final ActivityInstanceImpl subActivityInstance, WorkflowInstanceImpl subWorkflowInstance) {
        mapOutputVariables(subActivityInstance, subWorkflowInstance);

        //获取子的decide
        VariableInstanceImpl actionVariableInstance=null;
        if (Objects.nonNull(subWorkflowInstance.variableInstances)) {
            actionVariableInstance = subWorkflowInstance.variableInstances.stream().filter(item -> {
                return WorkflowConstants.SystemVariable.DECIDE.equals(item.getVariable().id);
            }).findFirst().orElse(null);
        }

        String action = Objects.nonNull(actionVariableInstance)?(String) actionVariableInstance.getValue():null;//agree and reject
        //更新主流程该变量的值,作为分支向下流转的条件
        String actionType = WorkflowConstants.Action.AGREE.equals(action) ? WorkflowConstants.UserTaskStatus.PASS : action;
        subActivityInstance.setVariableValue(WorkflowConstants.SystemVariable.DECIDE, actionType);

        //subProcessTask设置为pass
        SubProcessTask subProcessTask = taskStore.updateState(subWorkflowInstance.workflow.getTenantId(), subActivityInstance.workflowInstance.getId().toString(), subWorkflowInstance.id.toString(), actionType, subWorkflowInstance.end);
        sendHandlerEventBus(subProcessTask);
        //至此,子流程节点相关的执行已结束,下面是主流程的流转逻辑
        SubProcess subProcess = this.getActivity();
        if (subProcess.isRejectToBeforeTask() && WorkflowConstants.UserTaskStatus.REJECT.equals(actionType)) {
            String rejectToBeforeTaskActivityId = subProcess.getSubProcessEndrejectToBeforeTaskActivityId();
            subWorkflowInstance.workflow.getWorkflowEngine().sendByActivityId(new Message(subWorkflowInstance.workflow.getTenantId())
                            .workflowInstanceId(subActivityInstance.workflowInstance.getId()).activityInstanceId(subActivityInstance.getId())
                            .data(WorkflowConstants.TO_ACTIVITY_ID, rejectToBeforeTaskActivityId)
                            .transientData(WorkflowBindingEnum.moveToActivityIdWhenComplete.name(), subWorkflowInstance.getTransientProperty(WorkflowBindingEnum.moveToActivityIdWhenComplete.name()))
                            .transientData(WorkflowBindingEnum.parallelRejectedToCurrentNodeTaskId.name(), subWorkflowInstance.getTransientProperty(WorkflowBindingEnum.parallelRejectedToCurrentNodeTaskId.name()))
                    , subActivityInstance.workflowInstance);
        } else {
            //如果驳回到结束,则让流程继续流转,因为action=reject时,主流程会设置end,在fs-paas-workflow中会更新为reject
//                subWorkflowInstance.workflow.getWorkflowEngine().send(new Message(subWorkflowInstance.workflow.getTenantId())
//                        .workflowInstanceId(subActivityInstance.workflowInstance.getId()).activityInstanceId(subActivityInstance.getId()), subActivityInstance.workflowInstance);

            //修改下生成下一节点的方式
            nextCreateTask(subActivityInstance, subProcessTask.getId().toString());
        }
    }

    public void nextCreateTask(ActivityInstanceImpl subActivityInstance, String subProcessTaskId) {
        try {
            String tenantId=subActivityInstance.workflow.getTenantId();
            Class<?> subProcessTaskSupportClassBean = Class.forName(SUB_PROCESS_TASK_NEW_SUPPORT);
            Object subProcessTaskSupport = subProcessTaskSupportClassBean.newInstance();
            Method method = subProcessTaskSupportClassBean.getMethod("nextTask",ActivityInstanceImpl.class, String.class, String.class);
            method.invoke(subProcessTaskSupport,subActivityInstance, tenantId, subProcessTaskId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void sendHandlerEventBus(SubProcessTask subProcessTask) {
        MQContext mqContext = MQContext.create(subProcessTask.getTenantId(), subProcessTask.getAppId(), subProcessTask.getApplicantId(), null, subProcessTask.getType());
        EngineEventBus.post(
                TaskHandledEvent.create(
                        mqContext,
                        FlowTag.type(subProcessTask.getType()),
                        "",
                        subProcessTask.getId().toString(),
                        TaskState.valueOfSkipNotFound(subProcessTask.getState()),
                        null,
                        null,
                        WorkflowConstants.SUB_PROCESS,
                        null,
                        null,
                        subProcessTask.getWorkflowInstanceId(),
                        "",
                        null,
                        subProcessTask.getEntityId(),
                        subProcessTask.getObjectId(),
                        null
                )
        );

        EngineEventBus.post(InstanceEndEvent.create(
                mqContext,
                FlowTag.type(subProcessTask.getType()),
                "",
                subProcessTask.getSubWorkflowInstanceId(),
                null,
                null,
                subProcessTask.getEntityId(),
                subProcessTask.getObjectId()));
    }

    protected void mapOutputVariables(ActivityInstanceImpl callerActivityInstance, WorkflowInstanceImpl calledProcessInstance) {
        if (outputBindings != null) {
            for (String subWorkflowVariableId : outputBindings.keySet()) {
                String variableId = outputBindings.get(subWorkflowVariableId);
                TypedValueImpl typedValue = calledProcessInstance.getTypedValue(subWorkflowVariableId);
                callerActivityInstance.setVariableValue(variableId, typedValue);
            }
        }
    }

    public SubProcessTaskStore getTaskStore() {
        return taskStore;
    }

    protected void reportError(ActivityInstanceImpl activityInstance, String message) {
        log.warn(message);
    }

    public Map<String, BindingImpl> getInputBindings() {
        return inputBindings;
    }

}
