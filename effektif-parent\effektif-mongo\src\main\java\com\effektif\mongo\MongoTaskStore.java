package com.effektif.mongo;

import com.effektif.workflow.api.Configuration;
import com.effektif.workflow.api.model.StageTaskStateChangeRecord;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.api.query.OrderBy;
import com.effektif.workflow.api.query.OrderDirection;
import com.effektif.workflow.api.query.UserTaskQuery;
import com.effektif.workflow.impl.WorkflowEngineImpl;
import com.effektif.workflow.impl.activity.ActivityTypeService;
import com.effektif.workflow.impl.configuration.Brewable;
import com.effektif.workflow.impl.configuration.Brewery;
import com.effektif.workflow.impl.ext.*;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static com.effektif.workflow.api.ext.WorkflowConstants.UserTaskStatus.PASS;

/**
 * zhenghaibo
 * 16/6/12 15:55
 */
public class MongoTaskStore implements TaskStore, Brewable {
    public static final Logger log = MongoDb.log;

    protected WorkflowEngineImpl workflowEngine;
    protected ActivityTypeService activityTypeService;
    protected Configuration configuration;
    protected MongoObjectMapper mongoMapper;
    private MongoDb mongoDb;
    private MongoConfiguration mongoConfiguration;

    @Override public void brew(Brewery brewery) {
        this.mongoDb = brewery.get(MongoDb.class);
        this.mongoConfiguration = brewery.get(MongoConfiguration.class);
        this.configuration = brewery.get(Configuration.class);
        this.workflowEngine = brewery.get(WorkflowEngineImpl.class);
        this.activityTypeService = brewery.get(ActivityTypeService.class);
        this.mongoMapper = brewery.get(MongoObjectMapper.class);
    }

    public BasicDBObject taskToMongo(Task task) {
        return (BasicDBObject) mongoMapper.write(task);
    }

    public <T extends Task> T mongoToTask(BasicDBObject dbTask) {
        return mongoMapper.read(dbTask, Task.class);
    }

    @Override public TaskId generateTaskId() {
        return new TaskId(new ObjectId().toString());
    }

    @Override public void insertTask(Task task) {
        task.setModifyTime(System.currentTimeMillis());
        BasicDBObject dbTask = taskToMongo(task);
        this.getTasksCollection(task.getTenantId()).insert("insert-task", dbTask);
        try {
            this.getTodoTasksCollection(task.getTenantId()).insert("insert-todo-task", dbTask);
        } catch (Exception e) {
            log.error("insert todo_tasks error. ", e);
        }
    }

    private MongoCollection getTodoTasksCollection(String tenantId) {
        return  mongoDb.createCollection(tenantId,mongoConfiguration.todoTasksCollectionName);
    }

    private MongoCollection getTasksCollection(String tenantId) {
        return mongoDb.createCollection(tenantId,mongoConfiguration.getTasksCollectionName());
    }

    @Override public List<Task> findTasks(String tenantId, UserTaskQuery taskQuery) {
        List<Task> tasks = new ArrayList<>();
        BasicDBObject dbQuery = createDbQuery(taskQuery).get();
        DBCursor dbCursor = this.getTasksCollection(tenantId).find("find-tasks", dbQuery);
        if (taskQuery.getLimit() != null) {
            dbCursor.limit(taskQuery.getLimit());
        }
        if (taskQuery.getOrderBy() != null) {
            dbCursor.sort(writeOrderBy(taskQuery.getOrderBy()));
        }
        while (dbCursor.hasNext()) {
            BasicDBObject dbTask = (BasicDBObject) dbCursor.next();
            Task task = mongoToTask(dbTask);
            tasks.add(task);
        }
        return tasks;
    }

    @Override public Task assignTask(String tenantId, String taskId, List<String> assigneeIds) {
        BasicDBObject query = new Query()
            ._id(taskId)
            .get();
        BasicDBObject update = new Update()
            .set(TaskFields.ASSIGNEE_IDS, assigneeIds)
            .set(TaskFields.MODIFY_TIME, System.currentTimeMillis())
            .get();
        BasicDBObject dbTask = this.getTasksCollection(tenantId).findAndModify("assign-task", query, update);
        updateTodoTask(tenantId,query, update);
        return mongoToTask(dbTask);
    }

    @Override public Task completeTask(String tenantId, String taskId, String actionType) {
        BasicDBObject query = new Query()
            ._id(taskId)
            .get();
        BasicDBObject update = new Update()
            .set(TaskFields.COMPLETED, true)
            .set(TaskFields.MODIFY_TIME, System.currentTimeMillis())
            .set(TaskFields.ACTION_TYPE, actionType)
            .get();
        // this findAndModify returns the old version
        BasicDBObject dbTask = this.getTasksCollection(tenantId).findAndModify("complete-task", query, update, null, null, false, false, false);
        updateTodoTask(tenantId, query, update);
        return mongoToTask(dbTask);
    }

    @Override public Task completeTask(String tenantId, String taskId, String actionType, List<ApprovalOpinion> opinionList, List<String> assigneeIds, Map<String, List<String>> nextTaskAssignee) {
        //TODO 考虑并发，查询的时候应该加上modifyTime
        BasicDBObject query = new Query()
            ._id(taskId).notEqual("opinions.userId",opinionList.get(opinionList.size()-1).getUserId())
            .get();
        BasicDBList opinionListObj = getOpinionList(opinionList);
        BasicDBObject update = new Update()
            .set(TaskFields.COMPLETED, true)
            .set(TaskFields.STATE, PASS)
            .set(TaskFields.ASSIGNEE_IDS, assigneeIds)
            .set(TaskFields.MODIFY_TIME, System.currentTimeMillis())
            .set(TaskFields.ACTION_TYPE, actionType)
            .set(TaskFields.OPINIONS, opinionListObj)
            .set(TaskFields.NEXT_TASK_ASSIGNEE,nextTaskAssignee)
            .get();
        // this findAndModify returns the old version
        BasicDBObject dbTask = this.getTasksCollection(tenantId).findAndModify("complete-task", query, update, null, null, false, false, false);
        updateTodoTask(tenantId, query, update);
        return mongoToTask(dbTask);
    }

    @Override
    public Task completeTaskItem(String tenantId, String taskId, String actionType, List<ApprovalOpinion> opinionList, List<String> assigneeIds, String state) {
        return completeStageTask(tenantId,taskId, actionType, opinionList, assigneeIds, state, state);
    }

    @Override
    public Task completeStageTask(String tenantId, String taskId, String actionType, List<ApprovalOpinion> opinionList, List<String> assigneeIds, String state) {
        return completeStageTask(tenantId, taskId, actionType, opinionList, assigneeIds, state, null);
    }

    private Task completeStageTask(String tenantId, String taskId, String actionType, List<ApprovalOpinion> opinionList, List<String> assigneeIds, String state, String stateRecord) {
        //TODO 考虑并发，查询的时候应该加上modifyTime
        BasicDBObject query = new Query()
                ._id(taskId)
                .get();
        BasicDBList opinionListObj = getOpinionList(opinionList);

        Update updateOperate = new Update()
                .set(TaskFields.COMPLETED, true)
                .set(TaskFields.ASSIGNEE_IDS, assigneeIds)
                .set(TaskFields.MODIFY_TIME, System.currentTimeMillis())
                .set(TaskFields.ACTION_TYPE, actionType)
                .set(TaskFields.OPINIONS, opinionListObj)
                .set(TaskFields.STATE, state)
                .set(TaskFields.END_TIME, opinionList.get(opinionList.size() - 1).getReplyTime());
        if(StringUtils.isNotEmpty(stateRecord)) {
            updateOperate = updateOperate.push(TaskFields.STATE_CHANGE_RECORD, StageTaskStateChangeRecord.getRecord(state));
        }
        BasicDBObject update = updateOperate.get();

        BasicDBObject dbTask = this.getTasksCollection(tenantId).findAndModify("complete-task", query, update, null, null, false, false, false);
        updateTodoTask(tenantId, query, update);

        return dbTask == null ? null: mongoToTask(dbTask);
    }

    @Override
    public Task completeTask(String tenantId, String taskId, String actionType, String state, List<ApprovalOpinion> opinionList) {
        BasicDBObject query = new Query()
            ._id(taskId)
            .get();
        BasicDBList opinionListObj = getOpinionList(opinionList);
        long nowTime = System.currentTimeMillis();
        BasicDBObject update = new Update()
            .set(TaskFields.COMPLETED, true)
            .set(TaskFields.MODIFY_TIME, nowTime)
            .set(TaskFields.END_TIME, nowTime)
            .set(TaskFields.ACTION_TYPE, actionType)
            .set(TaskFields.STATE, state)
            .set(TaskFields.OPINIONS, opinionListObj)
            .get();
        BasicDBObject dbTask = this.getTasksCollection(tenantId).findAndModify("complete-task", query, update, null, null, false, true, false);
        updateTodoTask(tenantId, query, update);
        return mongoToTask(dbTask);
    }

    @Override public Task updateApprovalOpinion(String tenantId, String taskId, List<ApprovalOpinion> opinionList, List<String> assigneeIds) {
        BasicDBObject query = new Query()
            ._id(taskId)
            .get();
        BasicDBList opinionListObj = getOpinionList(opinionList);
        BasicDBObject update = new Update()
            .set(TaskFields.ASSIGNEE_IDS, assigneeIds)
            .set(TaskFields.MODIFY_TIME, System.currentTimeMillis())
            .set(TaskFields.OPINIONS, opinionListObj)
            .get();
        BasicDBObject dbTask = this.getTasksCollection(tenantId).findAndModify("update-task-opinion", query, update, null, null, false, false, false);
        updateTodoTask(tenantId, query, update);
        return mongoToTask(dbTask);
    }

    /**
     * 处理任务操作记录列表
     * @param opinionList
     * @return
     */
    private BasicDBList getOpinionList(List<ApprovalOpinion> opinionList) {
        BasicDBList opinionListObj = new BasicDBList();
        if (CollectionUtils.isEmpty(opinionList)) {
            return opinionListObj;
        }
        for (ApprovalOpinion opinion : opinionList) {
            opinionListObj.add(new BasicDBObject()
                    .append("id",StringUtils.isEmpty(opinion.getId())?new ObjectId().toString():opinion.getId())
                    .append("tenantId", opinion.getTenantId())
                    .append("feedId",opinion.getFeedId())
                    .append("replyId",opinion.getReplyId())
                    .append("replyTime", opinion.getReplyTime())
                    .append("userId", opinion.getUserId())
                    .append("actionType", opinion.getActionType())
                    .append("opinion", opinion.getOpinion())
                    .append("toTaskName",opinion.getToTaskName())
                    .append("sequence", opinion.getSequence())
                    .append("history", opinion.getHistory())
                    .append("needAfterTag",opinion.getNeedAfterTag())
                    .append("extraNodeAssignee", opinion.getExtraNodeAssignee())
                    .append("autoAgreeType", opinion.getAutoAgreeType())
                    .append("batch", opinion.getBatch())
                    .append("taskId", opinion.getTaskId())
                    .append("endTime", opinion.getEndTime())
                    .append("parallelRejectedToCurrentNodeTaskId", opinion.getParallelRejectedToCurrentNodeTaskId())
                    .append("moveToCurrentActivityWhenReject", opinion.getMoveToCurrentActivityWhenReject())
            );
        }
        return opinionListObj;
    }

    @Override public void deleteTasks(String tenantId, UserTaskQuery taskQuery) {
        //高风险，不再支持全量删除任务
    }

    private Query createDbQuery(UserTaskQuery taskQuery) {
        if (taskQuery == null) {
            taskQuery = new UserTaskQuery();
        }
        Query mongoQuery = new Query();

        if (taskQuery.getTaskId() != null) {
            mongoQuery.equal(TaskFields._ID, new ObjectId(taskQuery.getTaskId()));
        }
        if (taskQuery.getTaskName() != null) {
            mongoQuery.equal(TaskFields.NAME, Pattern.compile(taskQuery.getTaskName()));
        }
        if (taskQuery.getCompleted() != null) {
            if (taskQuery.getCompleted()) {
                mongoQuery.equal(TaskFields.COMPLETED, true);
            } else {
                mongoQuery.doesNotExist(TaskFields.COMPLETED);
            }
        }
        if (taskQuery.getWorkflowInstanceId() != null) {
            mongoQuery.equal(TaskFields.WORKFLOW_INSTANCE_ID,
                taskQuery.getWorkflowInstanceId());
        }
        if (taskQuery.getApplicantId() != null) {
            mongoQuery.equal(TaskFields.APPLICANT_ID, taskQuery.getApplicantId());
        }
        if (taskQuery.getAssigneeId() != null) {
            mongoQuery.in(TaskFields.ASSIGNEE_IDS, taskQuery.getAssigneeIds());
        }
        if (taskQuery.getTenantId() != null) {
            mongoQuery.equal(TaskFields.TENANT_ID, taskQuery.getTenantId());
        }
        if (taskQuery.getAppId() != null) {
            mongoQuery.equal(TaskFields.APP_ID, taskQuery.getAppId());
        }
        if (taskQuery.getEntityId() != null) {
            mongoQuery.equal(TaskFields.ENTITY_ID, taskQuery.getEntityId());
        }
        if (taskQuery.getObjectId() != null) {
            mongoQuery.equal(TaskFields.OBJECT_ID, taskQuery.getObjectId());
        }
        if (taskQuery.getParentTaskId() != null) {
            mongoQuery.equal(TaskFields.PARENT_TASK_ID, taskQuery.getParentTaskId());
        }
        return mongoQuery;
    }

    private DBObject writeOrderBy(List<OrderBy> orderBy) {
        BasicDBObject dbOrderBy = new BasicDBObject();
        for (OrderBy element : orderBy) {
            String dbField = element.getField();
            int dbDirection = (element.getDirection() == OrderDirection.asc ? 1 : -1);
            dbOrderBy.append(dbField, dbDirection);
        }
        return dbOrderBy;
    }

    @Override
    public void removeTodoTask(String tenantId, String taskId) {
        if (StringUtils.isNotEmpty(taskId)) {
            BasicDBObject query = new Query()._id(taskId).get();
            try {
                this.getTodoTasksCollection(tenantId).remove("remove-todoTask", query);
            } catch (Exception e) {
                log.error("remove todo_tasks error. ", e);
            }
        }
    }

    @Override
    public void updateTaskState(String tenantId, String taskId, String state, List<ApprovalOpinion> opinionList) {
        BasicDBObject query = new Query()
                ._id(taskId)
                .equal(TaskFields.TENANT_ID, tenantId)
                .get();
        Update update = new Update().set(TaskFields.MODIFY_TIME, System.currentTimeMillis());

        if (StringUtils.isNotEmpty(state)) {
            update.set(TaskFields.STATE, state);
        }

        if (CollectionUtils.isNotEmpty(opinionList)) {
            BasicDBList opinionListObj = getOpinionList(opinionList);
            update.set(TaskFields.OPINIONS, opinionListObj);
        }
        BasicDBObject updateObject = update.get();
        this.getTasksCollection(tenantId).findAndModify("update-task-state-and-opinions", query, updateObject,
                null, null, false, false, false);
        updateTodoTask(tenantId, query, updateObject);
    }

    /**
     * findAndModify task的时候update todoTask
     * @param tenantId
     * @param query
     * @param update
     */
    private void updateTodoTask(String tenantId, BasicDBObject query, BasicDBObject update) {
        try {
            this.getTodoTasksCollection(tenantId).update("update-todoTask", query, update);
        } catch (Exception e) {
            log.error("remove todo_tasks error. ", e);
        }
    }

    @Override
    public Task findTaskById(String tenantId, String taskId) {
        Query mongoQuery = new Query();
        mongoQuery.equal(TaskFields.TENANT_ID, tenantId);
        mongoQuery.equal(TaskFields._ID, new ObjectId(taskId));
        BasicDBObject dbTask = this.getTasksCollection(tenantId).findOne("task-findOne", mongoQuery.get());
        return mongoToTask(dbTask);
    }
}
