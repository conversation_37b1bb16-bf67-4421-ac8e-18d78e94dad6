package com.effektif.workflow.impl.conditions;

import com.effektif.workflow.api.condition.Condition;
import com.effektif.workflow.api.condition.NotIn;
import com.effektif.workflow.impl.data.TypedValueImpl;

/**
 * zhenghaibo
 * 2017/3/21 13:30
 */
public class NotInImpl extends InImpl {

    @Override
    public Class<? extends Condition> getApiType() {
        return NotIn.class;
    }

    @Override
    public boolean compare(TypedValueImpl leftValue, TypedValueImpl rightValue) {
        return !super.compare(leftValue, rightValue);
    }
}
