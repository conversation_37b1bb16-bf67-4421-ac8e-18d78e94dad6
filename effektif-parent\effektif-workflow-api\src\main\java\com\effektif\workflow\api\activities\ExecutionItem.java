package com.effektif.workflow.api.activities;

import com.effektif.workflow.api.ext.ActionParam;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * zhenghaibo
 * 2017/6/29 16:52
 */
public class ExecutionItem implements Serializable {
  private static final long serialVersionUID = -9201307438128145017L;

  /**
   * @see com.effektif.workflow.api.ext.WorkflowConstants.ExecuteType
   */
  private String taskType;
  private Boolean async;

  private String sender;
  /**
   * map<type,Set<id>>
   * type:PERSON,DEPT,ROLE,DEPT_LEADER
   */
  private LinkedHashMap<String, Set<String>> recipients;
  private LinkedHashMap<String, Set<String>> ccRecipients;
  private LinkedHashMap<String, Set<String>> bccRecipients;
  private Set<String> emailAddress;
  private String title;
  private String content;
  private String template;


  /**
   * map<entityId,<field,value>>
   * value:constant
   * $$__value:variable
   * %%__value:expression
   */
  private LinkedHashMap<String, LinkedHashMap<String, String>> fieldMapping;

  private String updateFieldJson;
  private Object updateFieldObject;
  private Map<String, Object> triggerParam;


  //zz 2017.11.13 start
  private String afterActionDefinitionId;
  private String afterActionMappingId;
  // zz  2017.11.13  end

  // wangbf 2018.01.19 start
  private Map<String, Object> actionMapping;
  private List<ActionParam> actionParams;
  // wangbf 2018.01.19 end
  private List customVariables;

  //只有该值为true时才会使用 下面两个属性
  private Boolean useRelated;

  //级联的 entity 如： AccountObj
  private String relatedEntityId;
  //级联的 objectId 如： AccountObj.account_id.account_id
  private String relatedObjectId;

  private String sourceWorkflowId;
  private List<Map<String, Object>> inputs;

  public String getSourceWorkflowId() {
    return sourceWorkflowId;
  }

  public void setSourceWorkflowId(String sourceWorkflowId) {
    this.sourceWorkflowId = sourceWorkflowId;
  }

  public List<Map<String, Object>> getInputs() {
    return inputs;
  }

  public void setInputs(List<Map<String, Object>>  inputs) {
    this.inputs = inputs;
  }

  public Boolean getUseRelated() {
    return useRelated;
  }

  public void setUseRelated(Boolean useRelated) {
    this.useRelated = useRelated;
  }

  public String getRelatedEntityId() {
    return relatedEntityId;
  }

  public void setRelatedEntityId(String relatedEntityId) {
    this.relatedEntityId = relatedEntityId;
  }

  public String getRelatedObjectId() {
    return relatedObjectId;
  }

  public void setRelatedObjectId(String relatedObjectId) {
    this.relatedObjectId = relatedObjectId;
  }

  public String getAfterActionDefinitionId() {
    return afterActionDefinitionId;
  }

  public void setAfterActionDefinitionId(String afterActionDefinitionId) {
    this.afterActionDefinitionId = afterActionDefinitionId;
  }

  public String getAfterActionMappingId() {
    return afterActionMappingId;
  }

  public void setAfterActionMappingId(String afterActionMappingId) {
    this.afterActionMappingId = afterActionMappingId;
  }

  public Map<String, Object> getActionMapping() {
    return actionMapping;
  }

  public void setActionMapping(Map<String, Object> actionMapping) {
    this.actionMapping = actionMapping;
  }

  public List<ActionParam> getActionParams() {
    return actionParams;
  }

  public void setActionParams(List<ActionParam> actionParams) {
    this.actionParams = actionParams;
  }

  public String getTaskType() {
    return taskType;
  }

  public void setTaskType(String taskType) {
    this.taskType = taskType;
  }

  public String getSender() {
    return sender;
  }

  public void setSender(String sender) {
    this.sender = sender;
  }

  public LinkedHashMap<String, Set<String>> getRecipients() {
    return recipients;
  }

  public void setRecipients(LinkedHashMap<String, Set<String>> recipients) {
    this.recipients = recipients;
  }

  public LinkedHashMap<String, Set<String>> getCcRecipients() {
    return ccRecipients;
  }

  public void setCcRecipients(LinkedHashMap<String, Set<String>> ccRecipients) {
    this.ccRecipients = ccRecipients;
  }

  public LinkedHashMap<String, Set<String>> getBccRecipients() {
    return bccRecipients;
  }

  public void setBccRecipients(LinkedHashMap<String, Set<String>> bccRecipients) {
    this.bccRecipients = bccRecipients;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public String getTemplate() {
    return template;
  }

  public void setTemplate(String template) {
    this.template = template;
  }

  public LinkedHashMap<String, LinkedHashMap<String, String>> getFieldMapping() {
    return fieldMapping;
  }

  public void setFieldMapping(LinkedHashMap<String, LinkedHashMap<String, String>> fieldMapping) {
    this.fieldMapping = fieldMapping;
  }

  public String getUpdateFieldJson() {
    return updateFieldJson;
  }

  public void setUpdateFieldJson(String updateFieldJson) {
    this.updateFieldJson = updateFieldJson;
  }

  public Set<String> getEmailAddress() {
    return emailAddress;
  }

  public void setEmailAddress(Set<String> emailAddress) {
    this.emailAddress = emailAddress;
  }

  public Map<String, Object> getTriggerParam() {
    return triggerParam;
  }

  public void setTriggerParam(Map<String, Object> triggerParam) {
    this.triggerParam = triggerParam;
  }

  public Object getUpdateFieldObject() {
    return updateFieldObject;
  }

  public void setUpdateFieldObject(Object updateFieldObject) {
    this.updateFieldObject = updateFieldObject;
  }

  public Boolean getAsync() {
    return async;
  }

  public void setAsync(Boolean async) {
    this.async = async;
  }

  public List getCustomVariables() {
    return customVariables;
  }

  public void setCustomVariables(List customVariables) {
    this.customVariables = customVariables;
  }
}
