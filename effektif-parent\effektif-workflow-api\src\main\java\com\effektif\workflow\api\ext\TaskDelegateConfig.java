package com.effektif.workflow.api.ext;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 委托记录参数
 */
public interface TaskDelegateConfig {


    /**
     * 批量查询委托设置入参
     */
    class BatchTaskDelegateQuery {
        private String tenantId;
        private String type;
        private String appId;
        private String entityId;
        private String sourceWorkflowId;
        private Map<String, List<String>> batchOfParamsAssignees;


        public static BatchTaskDelegateQuery create(String tenantId, String type,String appId, String entityId, String sourceWorkflowId, Map<String, List<String>> batchOfParamsAssignees) {
            BatchTaskDelegateQuery batchTaskDelegateQuery = new BatchTaskDelegateQuery();
            batchTaskDelegateQuery.setTenantId(tenantId);
            batchTaskDelegateQuery.setType(type);
            batchTaskDelegateQuery.setAppId(appId);
            batchTaskDelegateQuery.setBatchOfParamsAssignees(batchOfParamsAssignees);
            batchTaskDelegateQuery.setEntityId(entityId);
            batchTaskDelegateQuery.setSourceWorkflowId(sourceWorkflowId);
            return batchTaskDelegateQuery;

        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public Map<String, List<String>> getBatchOfParamsAssignees() {
            return batchOfParamsAssignees;
        }

        public void setBatchOfParamsAssignees(Map<String, List<String>> batchOfParamsAssignees) {
            this.batchOfParamsAssignees = batchOfParamsAssignees;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getEntityId() {
            return entityId;
        }

        public void setEntityId(String entityId) {
            this.entityId = entityId;
        }

        public String getSourceWorkflowId() {
            return sourceWorkflowId;
        }

        public void setSourceWorkflowId(String sourceWorkflowId) {
            this.sourceWorkflowId = sourceWorkflowId;
        }
    }


    /**
     * 批量存储任务的 对象数据
     */
    class ObjectMapping {
        private String entityId;
        private String objectId;

        public static ObjectMapping create(String entityId, String objectId) {
            ObjectMapping objectMapping = new ObjectMapping();
            objectMapping.setObjectId(objectId);
            objectMapping.setEntityId(entityId);
            return objectMapping;
        }

        public String getEntityId() {
            return entityId;
        }

        public void setEntityId(String entityId) {
            this.entityId = entityId;
        }

        public String getObjectId() {
            return objectId;
        }

        public void setObjectId(String objectId) {
            this.objectId = objectId;
        }
    }



    // =========================分割

    /**
     * 单个查询委托设置入参
     */
    class TaskDelegateQuery implements Serializable {
        private static final long serialVersionUID = -7469490149879315616L;


        private String tenantId;
        private String appId;
        private String type;
        private String taskId;


        private String entityId;
        private String objectId;
        private String linkAppId;
        private String sourceWorkflowId;

        private List<String> candidateIds;

        public static TaskDelegateQuery create(String tenantId, String appId, String type, String taskId, String entityId, String objectId, List<String> candidateIds,String linkAppId, String sourceWorkflowId) {
            TaskDelegateQuery taskDelegatePojo = new TaskDelegateQuery();
            taskDelegatePojo.setTenantId(tenantId);
            taskDelegatePojo.setAppId(appId);
            taskDelegatePojo.setType(type);
            taskDelegatePojo.setTaskId(taskId);
            taskDelegatePojo.setEntityId(entityId);
            taskDelegatePojo.setObjectId(objectId);
            taskDelegatePojo.setCandidateIds(candidateIds);
            taskDelegatePojo.setLinkAppId(linkAppId);
            taskDelegatePojo.setSourceWorkflowId(sourceWorkflowId);
            return taskDelegatePojo;

        }


        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }


        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public List<String> getCandidateIds() {
            return candidateIds;
        }

        public void setCandidateIds(List<String> candidateIds) {
            this.candidateIds = candidateIds;
        }

        public String getEntityId() {
            return entityId;
        }

        public void setEntityId(String entityId) {
            this.entityId = entityId;
        }

        public String getObjectId() {
            return objectId;
        }

        public void setObjectId(String objectId) {
            this.objectId = objectId;
        }

        public String getLinkAppId() {
            return linkAppId;
        }

        public void setLinkAppId(String linkAppId) {
            this.linkAppId = linkAppId;
        }

        public String getSourceWorkflowId() {
            return sourceWorkflowId;
        }

        public void setSourceWorkflowId(String sourceWorkflowId) {
            this.sourceWorkflowId = sourceWorkflowId;
        }
    }
}
