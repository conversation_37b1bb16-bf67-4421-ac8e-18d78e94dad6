package com.effektif.workflow.test.support;

import com.effektif.workflow.impl.ext.blockTask.BlockTask;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 10/8/24
 * @apiNote
 **/
public class BlockExecutionTaskSupportEffektif {
    public boolean execute(ActivityInstanceImpl activityInstance, BlockTask task) {
        return StringUtils.equals(task.getExecuteType(), "task");
    }
}
