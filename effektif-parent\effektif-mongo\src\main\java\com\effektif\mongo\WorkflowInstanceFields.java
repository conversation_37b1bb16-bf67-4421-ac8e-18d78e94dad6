/* Copyright (c) 2014, Effektif GmbH.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License. */
package com.effektif.mongo;


public interface WorkflowInstanceFields extends ScopeInstanceFields {

  String _ID = "_id";
  String WORKFLOW_ID = "workflowId";
  String SOURCE_WORKFLOW_ID = "sourceWorkflowId";
  String ACTIVITY_INSTANCES = "activityInstances";
  String ARCHIVED_ACTIVITY_INSTANCES = "archivedActivities";
  String VARIABLE_INSTANCES = "variableInstances";
  String LOCK = "lock";
  String UPDATES = "updates";
  String WORK = "work";
  String WORK_ASYNC = "workAsync";
  String CALLING_WORKFLOW_INSTANCE_ID = "callingWorkflowInstanceId";
  String CALLING_ACTIVITY_INSTANCE_ID = "callingActivityInstanceId";
  String NEXT_ACTIVITY_INSTANCE_ID = "nextActivityInstanceId";
  String NEXT_VARIABLE_INSTANCE_ID = "nextVariableInstanceId";
  String JOBS = "jobs";
  String PROPERTIES = "properties";
  String BUSINESS_KEY = "businessKey";

  String TENANT_ID = "tenantId";
  String INSTANCE_ID = "workflowInstanceId";
  String TRACE_ID = "traceId";
  String APP_ID = "appId";
  String ENTITY_ID = "entityId";
  String OBJECT_ID = "objectId";
  String STATE = "state";
  String TYPE = "type";//类型 审批流/工作流
  String SUB_TYPE = "subType";//子类型， 屏幕流使用
  String APPLICANT_ID = "applicantId";
  String CREATE_TIME = "start";
  String MODIFY_TIME = "modifyTime";
  String MODIFIER = "modifier";
  String WORKFLOW_NAME = "workflowName";
  String WORKFLOW_DESCRIPTION = "workflowDescription";
  String TRIGGER_TYPE = "triggerType";
  String TRIGGER_SOURCE = "triggerSource";
  String DELETED = "deleted";
  String HISTORY = "history";
  String VERSION = "version";

  String DATA_LOCK_TYPE = "dataLockType";
  String TRIGGER_DATA = "triggerData";
  String EXECUTION = "execution";
  String EXTERNAL_FLOW ="externalFlow";

  String  SUBMITTER = "submitter";
  String  OUTER_SUBMITTER = "outerSubmitter";
  String  SUBSTATE = "subState";
  String  END_STATE = "endState";
  String CANCEL_REASON = "cancelReason";

  String terminalId = "terminalId";

  String REJECT_REASON = "rejectReason";
  String REJECTER = "rejecter";
  String CANCEL_PERSON = "cancelPerson";
  String END = "end";
  String DURATION = "duration";
  String RESUBMIT_INFO = "resubmitInfo";
  String RESUBMIT_RECORDS = "resubmitRecords";
  String ERR_MSG = "errMsg";
  String OPERATE_RECORDS = "operateRecords";
  String TASKS = "tasks";
  String ALLOW_AUTO_COMPLETED_RETRIEVE = "allowAutoCompletedRetrieve";
  /**
   * 发起节点支持回复
   */
  String REPLIES = "replies";
  String EXCEPTION_INFO = "exceptionInfo";

  String EXCEPTION_INFO_I18N = "exceptionInfoI18n";
  String WORKFLOW_EXECUTE_STATE = "workflowExecuteState";
  String FEED_ID = "feedId";
  String EVENT_ID = "eventId";
  String COMPLETED_CALLBACK = "completedCallback";
  String OUT_PUTS = "outputs";

  interface Lock {
    String TIME = "time";
    String OWNER = "owner";
  }


  interface VariableInstance {
    String VARIABLE_ID = "variableId";
    String VALUE = "value";
    String TYPE = "type";
  }
}
