package com.effektif.workflow.test.timer;

import com.effektif.workflow.api.activities.BlockExecutionTask;
import com.effektif.workflow.api.activities.ExecutionItem;
import com.effektif.workflow.api.ext.ExecutionPojo;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.WorkflowInstanceStore;
import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.activity.AbstractActivityType;
import com.effektif.workflow.impl.activity.types.BlockExecutionTaskImpl;
import com.effektif.workflow.impl.ext.blockTask.BlockTask;
import com.effektif.workflow.impl.ext.blockTask.BlockTaskStore;
import com.effektif.workflow.impl.ext.blockTask.BlockTask.ExecutionType;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflow.WorkflowImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import com.facishare.paas.workflow.bus.EngineEventBus;
import com.effektif.workflow.impl.activity.AbstractActivityType;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;

public class JCActivityTaskImpl  extends AbstractActivityType<JCActivityParam> {        
 
    public JCActivityTaskImpl() {
       super(JCActivityParam.class);      
 
    }
 
    public void parse(ActivityImpl activityImpl, JCActivityParam executionTask, WorkflowParser parser) {
       super.parse(activityImpl, executionTask, parser);
       
    }
 
    public void execute(ActivityInstanceImpl activityInstance) {              
               activityInstance.onwards();         
    } 
    
}