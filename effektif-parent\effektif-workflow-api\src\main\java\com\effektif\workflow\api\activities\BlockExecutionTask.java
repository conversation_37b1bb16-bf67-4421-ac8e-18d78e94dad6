package com.effektif.workflow.api.activities;

import com.effektif.workflow.api.bpmn.BpmnElement;
import com.effektif.workflow.api.json.TypeName;
import com.effektif.workflow.api.model.Output;
import com.effektif.workflow.api.workflow.Activity;
import lombok.Data;

import java.util.*;

/**
 * 纷享 One Flow 阻塞的自动节点任务
 * <AUTHOR>
 */
@TypeName("blockExecutionTask")
@BpmnElement("blockExecutionTask")
@Data
public class BlockExecutionTask extends Activity {
    /**
     * auto,task
     */
    protected String executeType;
    /**
     * 对已经支持的执行项进行兼容
     */
    protected ExecutionItem item;
    /**
     * 屏幕操作节点相关配置信息
     */
    public Map<String,Object> extension;
    public List<Output> outputList;

    public BlockExecutionTask item(ExecutionItem item) {
        this.item=item;
        return this;
    }
    public BlockExecutionTask extension(Map<String,Object> extension) {
        this.extension=extension;
        return this;
    }

    public BlockExecutionTask extension(String key, Object value) {
        if (Objects.isNull(extension)) {
            this.extension = new HashMap<>();
        }
        this.extension.put(key, value);
        return this;
    }

    public BlockExecutionTask outputList(String from, Object to) {
        if (Objects.isNull(outputList)) {
            this.outputList = new ArrayList<>();
        }
        this.outputList.add(new Output(from, to));
        return this;
    }

    public List<Output> getOutputList() {
        if(Objects.isNull(outputList)){
            outputList= Collections.emptyList();
        }
        return outputList;
    }
    public BlockExecutionTask executeType(String executeType) {
        this.executeType=executeType;
        return this;
    }
}
