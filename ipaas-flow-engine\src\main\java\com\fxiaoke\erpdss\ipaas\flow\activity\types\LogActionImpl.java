package com.fxiaoke.erpdss.ipaas.flow.activity.types;

import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.activity.AbstractActivityType;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflow.BindingImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.fxiaoke.erpdss.ipaas.flow.activity.LogAction;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
@Slf4j
public class LogActionImpl extends AbstractActivityType<LogAction> {

    List<BindingImpl<String>> logBindings;

    public LogActionImpl() {
        super(LogAction.class);
    }

    @Override
    public void parse(ActivityImpl activityImpl, LogAction activity, WorkflowParser parser) {
        super.parse(activityImpl, activity, parser);
        this.logBindings = parser.parseBindings(activity.logBindings(), "argBindings");
    }

    @Override
    public void execute(ActivityInstanceImpl activityInstance) {
        if (logBindings != null) {
            for (BindingImpl<String> logBinding : logBindings) {
                //todo 记录租户日志
                log.info("LogActionImpl.execute:{}", activityInstance.getValue(logBinding));
            }
        }
    }
}
