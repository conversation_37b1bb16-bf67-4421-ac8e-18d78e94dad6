package com.effektif.workflow.test.support;

import com.effektif.workflow.api.ext.AssigneeParserPojo;
import com.google.common.collect.Sets;

import java.util.Set;


/**
 * <AUTHOR>
 * @creat_date: 2019-08-14
 * @creat_time: 09:30
 * @since 6.6
 */
public class UserTaskSupportEffektif {
  public Set<String> parseAssignee(AssigneeParserPojo assigneeParserPojo){
    if(assigneeParserPojo.getAssigneeMap().isEmpty()){
      return Sets.newHashSet();
    }
    return Sets.newHashSet("1000");
  }
}
