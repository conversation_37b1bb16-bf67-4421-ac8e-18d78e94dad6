package com.effektif.workflow.api.ext;

/**
 * 启动工作流时要绑定的类型
 * <p>
 * Created by liuyl on 2016/7/5.
 */
public enum WorkflowBindingEnum {
  tenantId,//租户
  appId,//产品线
  entityId,//实体
  objectId,//对象id
  applicantId,//提交人id
  applicantAccount,//提交人账号
  state,//实例状态
  type,//流程类型
  triggerType,//触发动作
  triggerSource,//触发来源
  triggerSourceId,//触发来源明细
  remind,//提醒
  remindLatency,//提醒时间
  latencyUnit,//提醒时间单位：1-天；2-小时；3-分钟；
  modifyTime, execution, reminders,//task提醒设置
  remindersV2,  //860新版超时提醒
  bpmExtension,//bpm扩展属性
  nextTaskAssigneeScope,//指定下一节点处理人范围

  /**
   * dataLockType == 0或字段不存在，表示锁定主对象和从对象
   * dataLockType == 1       表示只锁定主对象
   */
  dataLockType,
  /**
   * completedLockType=2或字段不存在，表示审批完成后解锁主对象和从对象
   * completedLockType=3，          表示审批完成后不执行解锁操作
   */
  completedLockType,

  /**
   *  防止工作流/审批流的死循环
   */
  eventId,
  /**
   * 920 流程实例的结束状态
   */
  endState,

  /**
   *  记录流程实例的版本,如6.2版本
   */
  version,

  triggerData,
  conditionMap,

  /**
   * 区分批量队列
   */
  triggerWay,
  /**
   * applicant 流程发起人(目前在审批流场景下 submitter和applicant 可能不同)
   * submitter 流程提交人或上游对接人
   * outerSubmitter 下流程流程提交人
   */

  /**
   * 6.3.2 数据提交人
   * 当是下游发起时,这里是下游的上游对接人
   */
  submitter,
  /**
   * 770 如果是下游发起,这里是下游提交人
   */
  outerSubmitter,
  terminalId, maxActivityInstance,
  /**
   * 目前审批流在用:实例完成时提醒
   */
  remindWhenInstanceComplete,
  /**
   * 目前审批流在用:任务完成时提醒
   */
  remindWhenTaskComplete,
  /**
   * 目前审批流在用:启用企业互联
   */
  linkAppEnable,
  /**
   * 应用Id
   */
  linkApp,
  /**
   * 应用名称
   */
  linkAppName,
  /**
   * 应用类型
   */
  linkAppType,
  /**
   * 是否允许跳过标识（阶段推进器使用）
   */
  skip,
  /**
   * 是否允许被CRM管理员或者外部主负责人之外的其他人撤回审批
   */
  cancelable,
  /**
   * 是否按照处理人列表顺序执行
   */
  sequence,
  /**
   * 连续两个节点是同一人处理的时候是否自动通过，1 自动通过 0 非自动通过
   */
  isAutoAgree,
  /**
   * 阶段推进器是否重新触发
   */
  stageChange,
  /**
   * 是否允许取回重审
   */
  allowRetrieve,
  /**
   * 是否允许跨自动通过的节点执行取回
   */
  allowAutoCompletedRetrieve,

  /**
   * 下游字段
   */
  outerTenantField,
  /**
   * 外部企业id
   */
  outerTenantId,
  /**
   * 驳回之后再次提交是否从当前节点开始，开关
   */
  enableMoveToCurrentActivityWhenReject,

  /**
   * 任务通过之后 要跳转到的activityId
   */
  moveToActivityIdWhenComplete,
  /**
   * 是否允许提交人终止审批流程
   */
  submitterCancelable,

  /**
   * 发起子流程时,子流程记录主流程的实例id
   */
  callingWorkflowInstanceId,


  /**
   * 发起子流程时,子流程记录在主流程上的activitInstanceId
   */
  callingActivityInstanceId,
  /**
   * 审批流驳回时候  需要告知驳回,eff判断如果是驳回  激活父状态流转
   */
  actionType,
  toActivityId,
  /**
   * 并行内驳回前置节点,标记是从哪个任务驳回的
   */
  parallelRejectedToCurrentNodeTaskId,
  /**
   * 工作流和one 实例及执行项 过期天数
   */
  expireDay
  ;

}
