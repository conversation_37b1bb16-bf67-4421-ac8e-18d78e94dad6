package com.effektif.workflow.impl.ext.latencyTask;

import com.effektif.workflow.api.model.TaskId;

/**
 * liugh
 * 19/2/28 22:50
 */
public interface AutoTaskStore {

  TaskId generateTaskId();

  /**
   * 1、插入定时等待节点
   * 2、插入BPM的自动节点
   *
   * @param task
   */
  void insertTask(AutoTask task);

  /**
   * 插入工作流的节点
   *
   * @param task
   */
  void insertWorkflowTask(AutoTask task);

  AutoTask completeTask(String tenantId,String taskId, String actionType);

}
