package com.effektif.mongo;

import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.configuration.Brewable;
import com.effektif.workflow.impl.configuration.Brewery;
import com.effektif.workflow.impl.ext.latencyTask.AutoTask;
import com.effektif.workflow.impl.ext.latencyTask.AutoTaskStore;
import com.mongodb.BasicDBObject;
import org.bson.types.ObjectId;
import org.slf4j.Logger;

import java.util.Date;

/**
 * liugh
 * 19/2/28 22:50
 */
public class MongoAutoTaskStore implements AutoTaskStore, Brewable {
  public static final Logger log = MongoDb.log;

  protected MongoObjectMapper mongoMapper;
  private MongoDb mongoDb;
  private MongoConfiguration mongoConfiguration;

  @Override
  public void brew(Brewery brewery) {
    mongoDb = brewery.get(MongoDb.class);
    mongoConfiguration = brewery.get(MongoConfiguration.class);
    this.mongoMapper = brewery.get(MongoObjectMapper.class);
  }

  public BasicDBObject taskToMongo(AutoTask task) {
    return (BasicDBObject) mongoMapper.write(task);
  }

  public <T extends AutoTask> T mongoToTask(BasicDBObject dbTask) {
    return mongoMapper.read(dbTask, AutoTask.class);
  }

  @Override
  public TaskId generateTaskId() {
    return new TaskId(new ObjectId().toString());
  }

  @Override
  public void insertTask(AutoTask task) {
    task.setModifyTime(System.currentTimeMillis());
    BasicDBObject dbTask = taskToMongo(task);
    if("CRM".equals(task.getAppId())&&"workflow".equals(task.getType())){
      //增加工作流过期索引时间
      dbTask.append("expireDate", new Date());
    }
    this.getTasksCollection(task.getTenantId()).insert("insert-autoTask", dbTask);
  }

  @Override
  public void insertWorkflowTask(AutoTask task) {
    task.setModifyTime(System.currentTimeMillis());
    BasicDBObject dbTask = taskToMongo(task);
    if("CRM".equals(task.getAppId())&&"workflow".equals(task.getType())){
      //增加工作流过期索引时间
      dbTask.append("expireDate", new Date());
    }
    this.getWorkflow_tasksCollection(task.getTenantId()).insert("insert-workflowTask", dbTask);
  }

  @Override
  public AutoTask completeTask(String tenantId,String taskId, String actionType) {
    BasicDBObject query = new Query()._id(taskId).get();
    long nowTime = System.currentTimeMillis();
    BasicDBObject update = new Update().set(TaskFields.COMPLETED, true)
      .set(TaskFields.MODIFY_TIME, nowTime)
      .set(TaskFields.END_TIME, nowTime)
      .set(TaskFields.ACTION_TYPE, actionType)
      .get();
    BasicDBObject dbTask = this.getTasksCollection(tenantId).findAndModify("complete-latencyTask", query, update, null, null, false, false, false);
    return mongoToTask(dbTask);
  }

  private MongoCollection getWorkflow_tasksCollection(String tenantId){
    return mongoDb.createCollection(tenantId,"workflow_tasks");
  }
  private MongoCollection getTasksCollection(String tenantId) {
    return mongoDb.createCollection(tenantId,mongoConfiguration.getAutoTasksCollectionName());
  }
}
