/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.api;

import com.effektif.workflow.api.model.*;
import com.effektif.workflow.api.query.UserTaskQuery;
import com.effektif.workflow.api.query.WorkflowInstanceQuery;
import com.effektif.workflow.api.query.WorkflowQuery;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;

import java.util.List;
import java.util.Map;


/**
 * Main interface to the workflow engine.
 * 
 * See <a href="https://github.com/effektif/effektif-oss/wiki/Workflow-engine-types">Workflow engine types</a>
 * for how to obtain a <code>WorkflowEngine</code> object.
 * 
 * <AUTHOR> Baeyens
 */
public interface WorkflowEngine {

  void refreshCache(String tenantId, List<WorkflowId> workflowIds);

  /** Validates and deploys if there are no errors. */
  Deployment deployWorkflow(ExecutableWorkflow workflow);

  Deployment updateWorkflow(ExecutableWorkflow workflow);

  List<ExecutableWorkflow> findWorkflows(String tenantId, WorkflowQuery workflowQuery);

  void deleteWorkflows(String tenantId, WorkflowQuery workflowQuery);

  /** starts a new workflow instance with the data specified in the trigger instance. */
  WorkflowInstance start(TriggerInstance triggerInstance);

  /** Sends a {@link Message message} to an activity instance, most likely this is invoked 
   * to end the specified activity instance and move workflow execution forward from there. */
  WorkflowInstance send(Message message);

  WorkflowInstance move(String tenantId, WorkflowInstanceId workflowInstanceId, String activityInstanceId, String newActivityId);

  WorkflowInstance move(String tenantId, WorkflowInstanceId workflowInstanceId, String newActivityId);

  WorkflowInstance move(String tenantId, WorkflowInstanceId workflowInstanceId,String newActivityId, Map<String, Object> transitionMap);

  WorkflowInstance cancel(String tenantId, WorkflowInstanceId workflowInstanceId);

  VariableValues getVariableValues(String tenantId, WorkflowInstanceId workflowInstanceId);

  VariableValues getVariableValues(String tenantId, WorkflowInstanceId workflowInstanceId, String activityInstanceId);

  void setVariableValues(String tenantId,WorkflowInstanceId workflowInstanceId, VariableValues variableValues);

  void setVariableValues(String tenantId,WorkflowInstanceId workflowInstanceId, String activityInstanceId, VariableValues variableValues);

  List<WorkflowInstance> findWorkflowInstances(WorkflowInstanceQuery query);
  
  void deleteWorkflowInstances(String tenantId, WorkflowInstanceQuery query);

  void deleteTasks(String tenantId, UserTaskQuery workflowQuery);

  void unlockWorkflowInstance(String tenantId, String workflowInstanceId);
  public void refreshCache();


}
