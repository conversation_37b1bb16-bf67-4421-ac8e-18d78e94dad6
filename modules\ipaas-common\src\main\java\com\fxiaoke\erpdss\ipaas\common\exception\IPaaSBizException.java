package com.fxiaoke.erpdss.ipaas.common.exception;

import com.fxiaoke.erpdss.ipaas.common.module.ResultCode;
import lombok.experimental.StandardException;

import java.io.Serial;

/**
 * 业务异常
 * <br/>内部不处理多语，msg应该是多语转换后放进来。
 *
 * <AUTHOR> (^_−)☆
 */
@StandardException
public class IPaaSBizException extends IPaaSException {
    @Serial
    private static final long serialVersionUID = 5269780425818536951L;

    public IPaaSBizException(String msg) {
        super(ResultCode.BIZ_ERROR.getCode(), msg);
    }


    public IPaaSBizException(String msg, Throwable cause) {
        super(ResultCode.BIZ_ERROR.getCode(), msg, cause);
    }

    public IPaaSBizException(String code, String msg) {
        super(code, msg);
    }

    public IPaaSBizException(String code, String msg, Throwable cause) {
        super(code, msg, cause);
    }
}
