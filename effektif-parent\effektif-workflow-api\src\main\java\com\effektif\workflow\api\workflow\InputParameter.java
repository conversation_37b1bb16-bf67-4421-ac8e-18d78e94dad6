/* Copyright (c) 2014, Effektif GmbH.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License. */
package com.effektif.workflow.api.workflow;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
public class InputParameter extends Extensible {

  protected Binding<?> binding;
  protected List<Binding<?>> bindings;

  public Binding<?> getBinding() {
    return this.binding;
  }
  public void setBinding(Binding<?> binding) {
    this.binding = binding;
  }
  public InputParameter binding(Binding<?> binding) {
    this.binding = binding;
    return this;
  }
  
  public List<Binding<?>> getBindings() {
    return this.bindings;
  }
  public void setBindings(List<Binding<?>> bindings) {
    this.bindings = bindings;
  }
  public InputParameter addBinding(Binding<?> binding) {
    if (bindings==null) {
      bindings = new ArrayList();
    }
    bindings.add(binding);
    return this;
  }
  
  @Override
  public InputParameter property(String key, Object value) {
    super.property(key, value);
    return this;
  }
  @Override
  public InputParameter propertyOpt(String key, Object value) {
    super.propertyOpt(key, value);
    return this;
  }
}
