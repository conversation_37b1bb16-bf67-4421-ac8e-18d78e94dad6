package com.effektif.workflow.impl.util;

import com.effektif.workflow.api.exception.EngineBranchFetchSubDeptException;
import com.effektif.workflow.impl.conditions.*;
import com.effektif.workflow.impl.workflow.ExpressionImpl;
import com.effektif.workflow.impl.workflow.TransitionImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @creat_date: 2020-02-12
 * @creat_time: 00:28
 * @since 7.0
 */
public class InstanceVariableUtil {
  public static final Logger log = LoggerFactory.getLogger(InstanceVariableUtil.class);

  @Data
  public static class BranchBean {
    public BranchBean(String id, String from, String to, String condition, Set<String> variableIds, Map<String, Object> variables, int serialNumber) {
      this.id = id;
      this.from = from;
      this.to = to;
      this.condition = condition;
      this.variableIds = variableIds;
      this.variables = variables;
      this.serialNumber = serialNumber;
    }

    String id;
    String from;
    String to;
    String condition;
    Set<String> variableIds;
    Map<String, Object> variables;
    int serialNumber;
    boolean defaultBranch;

    public String getId() {
      return id;
    }

    public Set<String> getVariableIds() {
      if (variableIds == null) {
        variableIds = new HashSet<>();
      }
      return variableIds;
    }

    public Map<String, Object> getVariables() {
      if (variables == null) {
        variables = new HashMap<>();
      }
      return variables;
    }

    public void setDefaultBranch(boolean defaultBranch) {
      this.defaultBranch = defaultBranch;
    }

    @Override
    public String toString() {
      final StringBuffer sb = new StringBuffer();
      sb.append("id='").append(id).append('\'');
      sb.append(", from='").append(from).append('\'');
      sb.append(", to='").append(to).append('\'');
      sb.append(", condition='").append(condition).append('\'');
      sb.append(", serialNumber=").append(serialNumber);
      sb.append(", defaultBranch=").append(defaultBranch);
      sb.append(", variableIds=").append(variableIds);
      sb.append(", variables=").append(variables);
      return sb.toString();
    }
  }
  /**
   * 从后动作获取最新的值 ，只适用于 业务流程，工作流，审批流 ，阶段推进器（如果有必要的话）
   *
   * @param activityInstance
   * @param transitions
   */
  public static void updateVariable(ActivityInstanceImpl activityInstance, List<TransitionImpl> transitions) {
    try {
      List<BranchBean>  transitionVariables = collectTransitionVariable(transitions);
      Class<?> bean = Class.forName("com.facishare.paas.workflow.kernel.support.ExclusiveGatewaySupport");
      IBranchVariablesAdapter branchVariablesAdapter = (IBranchVariablesAdapter) bean.newInstance();
      branchVariablesAdapter.setBranchNewVariables(activityInstance, transitionVariables);
    }catch (RuntimeException e){
      throw e;
    }catch (Throwable e) {
      log.info("branch variable refresh error: {} ", activityInstance.getWorkflowInstance().getId().toString(),e);
    }
  }

  /**
   * 分支上包含子部门出现异常时 进行尝试重试
   *
   * @param activityInstance
   * @param e
   */
  public static void setSubDeptErrorRetry(ActivityInstanceImpl activityInstance, EngineBranchFetchSubDeptException e) {
    try{
      Class<?> bean = Class.forName("com.facishare.paas.workflow.kernel.support.ExclusiveGatewaySupport");
      IBranchVariablesAdapter branchVariablesAdapter = (IBranchVariablesAdapter) bean.newInstance();
      branchVariablesAdapter.setSubDeptErrorRetry(activityInstance, e);
    }catch (Exception err){
      log.info("branch variable set sub dept retry error: {} ", activityInstance.getWorkflowInstance().getId().toString());
      throw new RuntimeException(err);
    }
  }

  /**
   * 忽略
   * action
   * activity_1488769056730##result
   * [action|activity_\d##result]
   *
   * @param transitions
   * @return
   */
  private static List<BranchBean> collectTransitionVariable(List<TransitionImpl> transitions) {
    List<BranchBean> branchBeans =new ArrayList<>();

    transitions.stream().filter(transition -> transition != null).forEach(transition -> {
      Set<String> variableIds = new HashSet<>();
      Map<String, Object> variables = Maps.newHashMap();
      collectTransitionVariable(variableIds, variables, transition.condition);
      BranchBean branch = new BranchBean(transition.id,
        transition.from != null ? transition.from.toString() : "",
        transition.to != null ? transition.to.toString() : "",
        transition.condition != null ? transition.condition.toString() : "",
        variableIds,
        variables,
        transition.serialNumber);
      branchBeans.add(branch);
    });
    return branchBeans;
  }


  private static void collectTransitionVariable(Set<String> variableIds, Map<String, Object> variables, ConditionImpl condition) {
    if (SingleBindingConditionImpl.class.isInstance(condition)) {
      String variableId = ((SingleBindingConditionImpl) condition).getLeft().expression.variableId;
      if (variableId != null && !pattern.matcher(variableId).matches()) {
        variableIds.add(variableId);
        variables.put(variableId, null);
      }
    } else if (ComparatorImpl.class.isInstance(condition)) {
      ExpressionImpl left = ((ComparatorImpl) condition).getLeft().expression;
      if (left != null) {
        if (!pattern.matcher(left.variableId).matches()) {
          if(CollectionUtils.isNotEmpty(left.fieldKeys)){
            left.fieldKeys.stream().map(key -> left.variableId + "." + key).forEach(variableIds::add);
          }else{
            variableIds.add(left.variableId);
          }
        }
        variables.put(left.variableId, null);
      }
      ExpressionImpl right = ((ComparatorImpl) condition).getRight().expression;
      if (right != null) {
        if (!pattern.matcher(right.variableId).matches()) {
          variableIds.add(right.variableId);
        }
        variables.put(left.variableId, null);
      }
    } else if (AndImpl.class.isInstance(condition)) {
      collectTransitionVariable(variableIds, variables, ((AndImpl) condition).getConditions());
    } else if (OrImpl.class.isInstance(condition)) {
      collectTransitionVariable(variableIds, variables, ((OrImpl) condition).getConditions());
    }
  }

  private static void collectTransitionVariable(Set<String> variableIds, Map<String, Object> variables, List<ConditionImpl> conditions) {
    conditions.forEach(condition -> collectTransitionVariable(variableIds, variables, condition));
  }

  public static final Pattern pattern = Pattern.compile("action|activity_\\d+##result|activity_\\d+##executionType");


  public interface IBranchVariablesAdapter {

    void setBranchNewVariables(ActivityInstanceImpl activityInstance, List<BranchBean> transitionVariables);

    void setSubDeptErrorRetry(ActivityInstanceImpl activityInstance,Exception e);

  }
}
