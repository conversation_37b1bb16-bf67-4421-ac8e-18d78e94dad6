package com.effektif.workflow.impl.util;

import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;

public class WorkflowPropertiesUtil {

    public static String getObjectHelper(Object obj) {
        String result = null;
        if (obj != null) {
            result = obj.toString();
        }
        return result;
    }

    public static String getWorkflowInstanceProperty(WorkflowInstanceImpl workflowInstance, String key) {
        if (workflowInstance == null || key == null) {
            return null;
        }
        if (workflowInstance.getProperty(key) != null) {
            return getObjectHelper(workflowInstance.getProperty(key));
        }
        return getObjectHelper(workflowInstance.getTransientProperty(key));
    }

}
