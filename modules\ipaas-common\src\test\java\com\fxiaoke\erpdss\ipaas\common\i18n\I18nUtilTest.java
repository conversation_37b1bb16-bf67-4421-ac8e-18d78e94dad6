package com.fxiaoke.erpdss.ipaas.common.i18n;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.function.BiFunction;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * I18nUtil 单元测试
 * 
 * <AUTHOR> (^_−)☆
 */
class I18nUtilTest {

    private BiFunction<String, String, String> originalFunction;

    @BeforeEach
    void setUp() {
        // 保存原始的i18n函数
        originalFunction = getI18nFunction();
    }

    @AfterEach
    void tearDown() {
        // 恢复原始的i18n函数
        I18nUtil._SetI18nFunction(originalFunction);
    }

    @Test
    void testGetWithDefaultFunction() {
        // Given
        String i18nKey = "test.key";
        String defaultValue = "默认值";

        // When
        String result = I18nUtil.get(i18nKey, defaultValue);

        // Then
        assertThat(result).isEqualTo(defaultValue);
    }

    @Test
    void testGetWithCustomFunction() {
        // Given
        String i18nKey = "test.key";
        String defaultValue = "默认值";
        String expectedValue = "国际化值";
        
        BiFunction<String, String, String> customFunction = (key, def) -> {
            if ("test.key".equals(key)) {
                return expectedValue;
            }
            return def;
        };
        
        I18nUtil._SetI18nFunction(customFunction);

        // When
        String result = I18nUtil.get(i18nKey, defaultValue);

        // Then
        assertThat(result).isEqualTo(expectedValue);
    }

    @Test
    void testGetWithNullKey() {
        // Given
        String i18nKey = null;
        String defaultValue = "默认值";

        // When
        String result = I18nUtil.get(i18nKey, defaultValue);

        // Then
        assertThat(result).isEqualTo(defaultValue);
    }

    @Test
    void testGetWithEmptyKey() {
        // Given
        String i18nKey = "";
        String defaultValue = "默认值";

        // When
        String result = I18nUtil.get(i18nKey, defaultValue);

        // Then
        assertThat(result).isEqualTo(defaultValue);
    }

    @Test
    void testGetWithBlankKey() {
        // Given
        String i18nKey = "   ";
        String defaultValue = "默认值";

        // When
        String result = I18nUtil.get(i18nKey, defaultValue);

        // Then
        assertThat(result).isEqualTo(defaultValue);
    }

    @Test
    void testGetWithArgs() {
        // Given
        String i18nKey = "test.key";
        String defaultValue = "Hello {0}, welcome to {1}!";
        Object[] args = {"张三", "iPaaS系统"};

        // When
        String result = I18nUtil.get(i18nKey, defaultValue, args);

        // Then
        assertThat(result).isEqualTo("Hello 张三, welcome to iPaaS系统!");
    }

    @Test
    void testGetWithNullArgs() {
        // Given
        String i18nKey = "test.key";
        String defaultValue = "默认值";
        Object[] args = null;

        // When
        String result = I18nUtil.get(i18nKey, defaultValue, args);

        // Then
        assertThat(result).isEqualTo(defaultValue);
    }

    @Test
    void testGetWithEmptyArgs() {
        // Given
        String i18nKey = "test.key";
        String defaultValue = "默认值";
        Object[] args = {};

        // When
        String result = I18nUtil.get(i18nKey, defaultValue, args);

        // Then
        assertThat(result).isEqualTo(defaultValue);
    }

    @Test
    void testGetWithCustomFunctionAndArgs() {
        // Given
        String i18nKey = "test.key";
        String defaultValue = "Hello {0}!";
        String i18nValue = "你好 {0}!";
        Object[] args = {"世界"};
        
        BiFunction<String, String, String> customFunction = (key, def) -> {
            if ("test.key".equals(key)) {
                return i18nValue;
            }
            return def;
        };
        
        I18nUtil._SetI18nFunction(customFunction);

        // When
        String result = I18nUtil.get(i18nKey, defaultValue, args);

        // Then
        assertThat(result).isEqualTo("你好 世界!");
    }

    @Test
    void testGetWithExceptionInFunction() {
        // Given
        String i18nKey = "test.key";
        String defaultValue = "默认值";
        
        BiFunction<String, String, String> faultyFunction = (key, def) -> {
            throw new RuntimeException("模拟异常");
        };
        
        I18nUtil._SetI18nFunction(faultyFunction);

        // When
        String result = I18nUtil.get(i18nKey, defaultValue);

        // Then
        assertThat(result).isEqualTo(defaultValue);
    }

    @Test
    void testGetWithInvalidFormatPattern() {
        // Given
        String i18nKey = "test.key";
        String defaultValue = "Invalid pattern {0} {1} {";
        Object[] args = {"arg1"};

        // When
        String result = I18nUtil.get(i18nKey, defaultValue, args);

        // Then
        // 当格式化失败时，应该返回原始模式
        assertThat(result).isEqualTo(defaultValue);
    }

    @Test
    void testGetWithCustomFunctionReturningInvalidPattern() {
        // Given
        String i18nKey = "test.key";
        String defaultValue = "Default {0}";
        String invalidPattern = "Invalid {0} {1} {";
        Object[] args = {"test"};
        
        BiFunction<String, String, String> customFunction = (key, def) -> invalidPattern;
        I18nUtil._SetI18nFunction(customFunction);

        // When
        String result = I18nUtil.get(i18nKey, defaultValue, args);

        // Then
        // 当i18n值格式化失败时，应该尝试使用默认值格式化
        assertThat(result).isEqualTo("Default test");
    }

    @Test
    void testSetI18nFunctionThreadSafety() {
        // Given
        BiFunction<String, String, String> function1 = (key, def) -> "function1";
        BiFunction<String, String, String> function2 = (key, def) -> "function2";

        // When
        I18nUtil._SetI18nFunction(function1);
        String result1 = I18nUtil.get("key", "default");
        
        I18nUtil._SetI18nFunction(function2);
        String result2 = I18nUtil.get("key", "default");

        // Then
        assertThat(result1).isEqualTo("function1");
        assertThat(result2).isEqualTo("function2");
    }

    @Test
    void testGetWithMultipleFormatArguments() {
        // Given
        String i18nKey = "test.key";
        String defaultValue = "用户 {0} 在 {1} 执行了 {2} 操作，结果：{3}";
        Object[] args = {"张三", "2023-12-01 10:30:00", "登录", "成功"};

        // When
        String result = I18nUtil.get(i18nKey, defaultValue, args);

        // Then
        assertThat(result).isEqualTo("用户 张三 在 2023-12-01 10:30:00 执行了 登录 操作，结果：成功");
    }

    @Test
    void testGetWithMismatchedArguments() {
        // Given
        String i18nKey = "test.key";
        String defaultValue = "需要 {0} 和 {1} 参数";
        Object[] args = {"参数1"}; // 只提供一个参数，但模板需要两个

        // When
        String result = I18nUtil.get(i18nKey, defaultValue, args);

        // Then
        // 应该正常处理，未匹配的占位符保持原样
        assertThat(result).contains("参数1");
    }

    @Test
    void testGetWithSpecialCharacters() {
        // Given
        String i18nKey = "test.special";
        String defaultValue = "特殊字符：{0}，符号：{1}";
        Object[] args = {"@#$%^&*()", "中文测试"};

        // When
        String result = I18nUtil.get(i18nKey, defaultValue, args);

        // Then
        assertThat(result).isEqualTo("特殊字符：@#$%^&*()，符号：中文测试");
    }

    @Test
    void testGetWithNumberArguments() {
        // Given
        String i18nKey = "test.numbers";
        String defaultValue = "数字：{0}，小数：{1}，长整型：{2}";
        Object[] args = {123, 45.67, 9876543210L};

        // When
        String result = I18nUtil.get(i18nKey, defaultValue, args);

        // Then
        // 注意：长整型数字可能会被格式化为带逗号的形式，这是正常的
        assertThat(result).contains("数字：123");
        assertThat(result).contains("小数：45.67");
        assertThat(result).contains("长整型：");
        // 检查数字是否存在（可能带逗号分隔符）
        assertThat(result).satisfiesAnyOf(
            r -> assertThat(r).contains("9876543210"),
            r -> assertThat(r).contains("9,876,543,210")
        );
    }

    @Test
    void testGetWithNullArgument() {
        // Given
        String i18nKey = "test.null";
        String defaultValue = "值：{0}，状态：{1}";
        Object[] args = {null, "正常"};

        // When
        String result = I18nUtil.get(i18nKey, defaultValue, args);

        // Then
        assertThat(result).isEqualTo("值：null，状态：正常");
    }

    @Test
    void testConcurrentAccess() throws InterruptedException {
        // Given
        int threadCount = 5; // 减少线程数量以提高稳定性
        java.util.concurrent.CountDownLatch startLatch = new java.util.concurrent.CountDownLatch(1);
        java.util.concurrent.CountDownLatch endLatch = new java.util.concurrent.CountDownLatch(threadCount);
        java.util.concurrent.atomic.AtomicInteger successCount = new java.util.concurrent.atomic.AtomicInteger(0);

        // When
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                try {
                    startLatch.await(); // 等待所有线程准备就绪

                    BiFunction<String, String, String> customFunction = (key, def) -> "Thread-" + threadId + "-" + def;
                    I18nUtil._SetI18nFunction(customFunction);

                    // 添加小延迟确保函数设置生效
                    Thread.sleep(10);

                    String result = I18nUtil.get("test.key", "default");
                    if (result.contains("Thread-" + threadId)) {
                        successCount.incrementAndGet();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    endLatch.countDown();
                }
            }).start();
        }

        // 启动所有线程
        startLatch.countDown();

        // Then
        endLatch.await(10, java.util.concurrent.TimeUnit.SECONDS);
        // 由于线程竞争，至少应该有一些成功的设置
        assertThat(successCount.get()).isGreaterThan(0);
        assertThat(successCount.get()).isLessThanOrEqualTo(threadCount);
    }

    /**
     * 通过反射获取当前的i18n函数（用于测试）
     */
    private BiFunction<String, String, String> getI18nFunction() {
        try {
            var field = I18nUtil.class.getDeclaredField("i18nFunction");
            field.setAccessible(true);
            return (BiFunction<String, String, String>) field.get(null);
        } catch (Exception e) {
            // 如果获取失败，返回默认函数
            return (i18nKey, defaultValue) -> defaultValue;
        }
    }
}
