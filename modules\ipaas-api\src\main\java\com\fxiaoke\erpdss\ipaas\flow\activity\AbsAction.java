package com.fxiaoke.erpdss.ipaas.flow.activity;

import com.effektif.workflow.api.condition.Condition;
import com.effektif.workflow.api.workflow.Activity;
import com.effektif.workflow.api.workflow.Transition;

import java.io.Serial;

/**
 * 
 * <AUTHOR> (^_−)☆
 */
 @SuppressWarnings("unchecked")
 public class AbsAction <T extends AbsAction<T>> extends Activity{
 @Serial
 private static final long serialVersionUID = 5062463077623040065L;

 @Override
    public T id(String id) {
        super.id(id);
        return (T) this;
    }
    @Override
    public T name(String name) {
        super.name(name);
        return (T) this;
    }
    @Override
    public T description(String description) {
        super.description(description);
        return (T) this;
    }
    @Override
    public T transitionTo(String toActivityId) {
        super.transitionTo(toActivityId);
        return (T) this;
    }
    @Override
    public T transitionWithConditionTo(Condition condition, String toActivityId) {
        super.transitionWithConditionTo(condition, toActivityId);
        return (T) this;
    }
    @Override
    public T transitionToNext() {
        super.transitionToNext();
        return (T) this;
    }
    @Override
    public T transitionTo(Transition transition) {
        super.transitionTo(transition);
        return (T) this;
    }
    @Override
    public T transition(Transition transition) {
        super.transition(transition);
        return (T) this;
    }
    @Override
    public T transition(String id, Transition transition) {
        super.transition(id, transition);
        return (T) this;
    }
    @Override
    public T property(String key, Object value) {
        super.property(key, value);
        return (T) this;
    }
    @Override
    public T propertyOpt(String key, Object value) {
        super.propertyOpt(key, value);
        return (T) this;
    }

}
