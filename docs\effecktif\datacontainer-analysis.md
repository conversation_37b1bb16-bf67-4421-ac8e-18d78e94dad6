# DataContainer 数据字段分析文档

## 概述

本文档详细分析了 Effektif 工作流引擎中 `DataContainer` 类的 `data` 和 `transientData` 两个核心字段，包括其设计理念、使用场景和在 iPaaS 集成平台中的最佳实践。

## 1. 字段定义和特征

### 1.1 字段声明

```java
public class DataContainer {
    // 持久化数据字段 - 支持类型化值
    protected Map<String,TypedValue> data;
    
    // 临时数据字段 - 不会被序列化和持久化
    protected Map<String,Object> transientData;
}
```

### 1.2 特征对比

| 特征 | data | transientData |
|------|------|---------------|
| **数据类型** | `Map<String,TypedValue>` | `Map<String,Object>` |
| **类型支持** | 强类型化（TypedValue包装） | 弱类型化（Object） |
| **序列化** | ✅ 会被序列化 | ❌ 不会被序列化 |
| **持久化** | ✅ 会被持久化到数据库 | ❌ 不会被持久化 |
| **生命周期** | 工作流全生命周期 | 仅运行时有效 |

## 2. 设计意图

### 2.1 数据分离的核心理念

- **持久化数据（data）**：存储需要在工作流生命周期中保持的业务数据
- **临时数据（transientData）**：存储仅在当前执行上下文中有效的运行时数据

### 2.2 设计模式优势

1. **性能优化**：避免不必要的数据持久化，减少存储开销
2. **内存管理**：临时数据可以及时释放，避免内存泄漏
3. **安全性**：敏感的运行时信息不会被持久化
4. **灵活性**：支持不同生命周期的数据管理策略

## 3. 生命周期差异

### 3.1 data 字段生命周期
```
工作流启动 → 数据设置 → 序列化存储 → 跨节点传递 → 工作流结束后保留
```

### 3.2 transientData 字段生命周期
```
运行时创建 → 内存中使用 → 执行完成后销毁（不持久化）
```

### 3.3 序列化配置
```java
// 在 MongoObjectMappingsBuilder 中明确配置忽略 transientData 字段
ignore(DataContainer.class, "transientData");
```

## 4. 使用场景分析

### 4.1 data 字段适用场景

#### 业务变量存储
```java
// 动态存储工作流业务数据 - 适应未知数据结构
public void storeBusinessData(TriggerInstance triggerInstance, Map<String, Object> incomingData) {
    // 遍历所有传入的数据字段，动态设置到data中
    for (Map.Entry<String, Object> entry : incomingData.entrySet()) {
        String fieldName = entry.getKey();
        Object fieldValue = entry.getValue();

        // 根据值类型动态确定数据类型
        if (fieldValue instanceof String) {
            triggerInstance.data(fieldName, fieldValue);
        } else if (fieldValue instanceof Number) {
            triggerInstance.data(fieldName, fieldValue, NumberType.INSTANCE);
        } else if (fieldValue instanceof Boolean) {
            triggerInstance.data(fieldName, fieldValue, BooleanType.INSTANCE);
        } else {
            // 复杂对象使用ObjectType
            triggerInstance.data(fieldName, fieldValue, ObjectType.INSTANCE);
        }
    }
}
```

#### 跨节点数据传递
```java
// 动态传递节点间数据 - 支持任意字段结构
public void transferNodeData(WorkflowInstance workflowInstance,
                           Map<String, Object> nodeOutputData,
                           FieldMappingConfig mappingConfig) {
    // 根据映射配置动态传递数据
    for (Map.Entry<String, Object> entry : nodeOutputData.entrySet()) {
        String sourceField = entry.getKey();
        Object value = entry.getValue();

        // 应用字段映射规则
        String targetField = mappingConfig.getTargetField(sourceField);
        if (targetField != null) {
            workflowInstance.setVariableValue(targetField, value);
        }
    }
}
```

#### 持久化状态信息
```java
// 动态持久化状态信息 - 支持灵活的状态字段
public void persistWorkflowState(DataContainer dataContainer,
                               Map<String, Object> stateData,
                               Set<String> persistentFields) {
    // 只持久化指定的状态字段
    for (String fieldName : persistentFields) {
        if (stateData.containsKey(fieldName)) {
            Object value = stateData.get(fieldName);
            dataContainer.data(fieldName, value);
        }
    }
}
```

### 4.2 transientData 字段适用场景

#### 运行时上下文信息
```java
// 动态传递临时运行时数据 - 支持任意上下文结构
public void setRuntimeContext(WorkflowInstance workflowInstance,
                            Map<String, Object> contextData,
                            ContextFilterConfig filterConfig) {
    if (contextData != null) {
        for (Map.Entry<String, Object> entry : contextData.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 应用上下文过滤规则
            if (filterConfig.shouldInclude(key, value)) {
                workflowInstance.setTransientProperty(key, value);
            }
        }
    }
}
```

#### 系统内部状态
```java
// 动态存储系统内部状态 - 支持可配置的状态字段
public void setSystemState(DataContainer dataContainer,
                         Map<String, Object> systemState,
                         SystemStateConfig stateConfig) {
    // 根据配置动态设置系统状态
    for (Map.Entry<String, Object> entry : systemState.entrySet()) {
        String stateName = entry.getKey();
        Object stateValue = entry.getValue();

        // 检查是否为允许的系统状态字段
        if (stateConfig.isAllowedStateField(stateName)) {
            dataContainer.transientData(stateName, stateValue);
        }
    }
}
```

#### 敏感信息传递
```java
// 动态传递敏感信息 - 支持可配置的敏感字段识别
public void setSensitiveData(DataContainer dataContainer,
                           Map<String, Object> sensitiveData,
                           SecurityConfig securityConfig) {
    for (Map.Entry<String, Object> entry : sensitiveData.entrySet()) {
        String fieldName = entry.getKey();
        Object fieldValue = entry.getValue();

        // 检查是否为敏感字段
        if (securityConfig.isSensitiveField(fieldName)) {
            // 敏感信息只存储在transientData中，避免持久化
            dataContainer.transientData(fieldName, fieldValue);
        }
    }
}
```

## 5. iPaaS 集成平台应用

### 5.1 Webhook 触发器场景

#### 数据接收阶段
```java
public TriggerInstance handleWebhook(HttpServletRequest request,
                                   WebhookConfig webhookConfig) {
    // 解析未知结构的Webhook数据
    Map<String, Object> webhookPayload = parseWebhookPayload(request);
    TriggerInstance trigger = new TriggerInstance(tenantId);

    // === 动态处理业务数据 ===
    processBusinessData(trigger, webhookPayload, webhookConfig);

    // === 动态处理运行时数据 ===
    processRuntimeData(trigger, request, webhookConfig);

    return trigger;
}

private void processBusinessData(TriggerInstance trigger,
                               Map<String, Object> payload,
                               WebhookConfig config) {
    // 根据配置的字段映射规则处理业务数据
    FieldMappingConfig mappingConfig = config.getFieldMappingConfig();

    for (Map.Entry<String, Object> entry : payload.entrySet()) {
        String sourceField = entry.getKey();
        Object value = entry.getValue();

        // 检查是否为需要持久化的业务字段
        if (mappingConfig.isBusinessField(sourceField)) {
            String targetField = mappingConfig.getTargetField(sourceField);
            DataType dataType = mappingConfig.getDataType(sourceField);

            // 动态设置业务数据
            if (dataType != null) {
                trigger.data(targetField, value, dataType);
            } else {
                trigger.data(targetField, value);
            }
        }
    }

    // 添加系统标识字段
    trigger.data("sourceSystem", config.getSourceSystemId())
           .data("targetSystem", config.getTargetSystemId());
}

private void processRuntimeData(TriggerInstance trigger,
                              HttpServletRequest request,
                              WebhookConfig config) {
    // 动态添加运行时上下文数据
    Map<String, Object> runtimeData = new HashMap<>();
    runtimeData.put("webhookId", generateWebhookId());
    runtimeData.put("receivedAt", System.currentTimeMillis());
    runtimeData.put("sourceIp", request.getRemoteAddr());
    runtimeData.put("retryCount", 0);

    // 根据安全配置添加认证信息
    if (config.getSecurityConfig().requiresTempAuth()) {
        runtimeData.put("tempAuthToken", generateTempToken());
    }

    // 批量设置临时数据
    for (Map.Entry<String, Object> entry : runtimeData.entrySet()) {
        trigger.transientData(entry.getKey(), entry.getValue());
    }
}
```

#### 集成流处理
```java
public void transformIntegrationData(ActivityInstanceImpl activityInstance,
                                   IntegrationConfig integrationConfig) {
    WorkflowInstanceImpl workflowInstance = activityInstance.getWorkflowInstance();

    // 动态获取业务数据 - 支持任意字段结构
    Map<String, Object> businessData = extractBusinessData(workflowInstance, integrationConfig);

    // 动态获取运行时数据
    Map<String, Object> runtimeData = extractRuntimeData(workflowInstance, integrationConfig);

    // 执行数据转换
    Map<String, Object> transformedData = performDataTransformation(businessData, integrationConfig);

    // 动态保存转换结果
    saveTransformationResults(workflowInstance, transformedData, integrationConfig);

    // 更新处理状态
    updateProcessingState(workflowInstance, runtimeData, "DATA_TRANSFORMED");
}

private Map<String, Object> extractBusinessData(WorkflowInstanceImpl workflowInstance,
                                               IntegrationConfig config) {
    Map<String, Object> businessData = new HashMap<>();
    Set<String> businessFields = config.getBusinessFields();

    // 动态提取所有业务字段
    for (String fieldName : businessFields) {
        Object value = workflowInstance.getVariableValue(fieldName);
        if (value != null) {
            businessData.put(fieldName, value);
        }
    }
    return businessData;
}

private Map<String, Object> extractRuntimeData(WorkflowInstanceImpl workflowInstance,
                                              IntegrationConfig config) {
    Map<String, Object> runtimeData = new HashMap<>();
    Set<String> runtimeFields = config.getRuntimeFields();

    // 动态提取所有运行时字段
    for (String fieldName : runtimeFields) {
        Object value = workflowInstance.getTransientProperty(fieldName);
        if (value != null) {
            runtimeData.put(fieldName, value);
        }
    }
    return runtimeData;
}

private Map<String, Object> performDataTransformation(Map<String, Object> sourceData,
                                                     IntegrationConfig config) {
    Map<String, Object> transformedData = new HashMap<>();
    DataTransformationRules rules = config.getTransformationRules();

    // 应用转换规则
    for (Map.Entry<String, Object> entry : sourceData.entrySet()) {
        String sourceField = entry.getKey();
        Object sourceValue = entry.getValue();

        // 获取转换规则
        FieldTransformationRule rule = rules.getRule(sourceField);
        if (rule != null) {
            String targetField = rule.getTargetField();
            Object transformedValue = rule.transform(sourceValue);
            transformedData.put(targetField, transformedValue);
        }
    }
    return transformedData;
}

private void saveTransformationResults(WorkflowInstanceImpl workflowInstance,
                                     Map<String, Object> transformedData,
                                     IntegrationConfig config) {
    // 动态保存转换结果
    for (Map.Entry<String, Object> entry : transformedData.entrySet()) {
        String fieldName = entry.getKey();
        Object value = entry.getValue();
        workflowInstance.setVariableValue(fieldName, value);
    }
}

private void updateProcessingState(WorkflowInstanceImpl workflowInstance,
                                 Map<String, Object> runtimeData,
                                 String newState) {
    // 更新处理状态
    workflowInstance.setTransientProperty("processingStep", newState);

    // 更新其他运行时状态
    Integer currentRetryCount = (Integer) runtimeData.get("retryCount");
    if (currentRetryCount != null) {
        workflowInstance.setTransientProperty("retryCount", currentRetryCount);
    }
}
```

### 5.2 多租户环境数据隔离
```java
// 动态多租户数据隔离 - 支持可配置的租户字段
public void setupTenantIsolation(WorkflowInstance workflowInstance,
                               TenantContext tenantContext,
                               TenantConfig tenantConfig) {
    // 动态设置租户业务配置（持久化）
    Map<String, Object> tenantBusinessData = tenantContext.getBusinessData();
    for (Map.Entry<String, Object> entry : tenantBusinessData.entrySet()) {
        String configKey = entry.getKey();
        Object configValue = entry.getValue();

        // 检查是否为需要持久化的租户配置
        if (tenantConfig.isPersistentConfig(configKey)) {
            workflowInstance.setVariableValue(configKey, configValue);
        }
    }

    // 动态设置临时租户上下文（不持久化）
    Map<String, Object> tenantRuntimeData = tenantContext.getRuntimeData();
    for (Map.Entry<String, Object> entry : tenantRuntimeData.entrySet()) {
        String contextKey = entry.getKey();
        Object contextValue = entry.getValue();

        // 临时上下文信息不持久化
        workflowInstance.setTransientProperty(contextKey, contextValue);
    }
}
```

## 6. 最佳实践

### 6.1 数据分类原则

#### 使用 data 字段的情况：
- 业务关键数据
- 需要审计追踪的信息
- 跨工作流节点传递的数据
- 工作流重启后需要恢复的状态

#### 使用 transientData 字段的情况：
- 运行时临时计算结果
- 系统内部状态信息
- 敏感认证信息
- 大对象的临时引用

### 6.2 内存优化建议

#### 及时清理临时数据
```java
// 在适当的时机清理不再需要的临时数据
dataContainer.removeTransientData("largeTemporaryObject");
```

#### 避免在 transientData 中存储大对象
```java
// 错误做法
dataContainer.transientData("hugeDataSet", millionRecordsList);

// 正确做法
dataContainer.transientData("dataSetReference", dataSetId);
```

#### 使用 transientDataOpt 方法
```java
// 只在值不为null时设置临时数据
dataContainer.transientDataOpt("optionalContext", contextValue);
```

### 6.3 完整的数据管理示例

```java
public class iPaaSWorkflowDataManager {

    /**
     * 动态设置集成任务数据 - 支持任意数据结构
     */
    public void setupIntegrationTask(DataContainer container,
                                   Map<String, Object> requestData,
                                   IntegrationTaskConfig taskConfig) {
        // 动态处理业务数据
        setupBusinessData(container, requestData, taskConfig);

        // 动态处理运行时数据
        setupRuntimeData(container, requestData, taskConfig);
    }

    private void setupBusinessData(DataContainer container,
                                 Map<String, Object> requestData,
                                 IntegrationTaskConfig taskConfig) {
        // 根据配置动态设置持久化业务数据
        Set<String> businessFields = taskConfig.getBusinessFields();

        for (String fieldName : businessFields) {
            if (requestData.containsKey(fieldName)) {
                Object value = requestData.get(fieldName);
                DataType dataType = taskConfig.getFieldDataType(fieldName);

                if (dataType != null) {
                    container.data(fieldName, value, dataType);
                } else {
                    container.data(fieldName, value);
                }
            }
        }

        // 添加系统配置字段
        Map<String, Object> systemConfig = taskConfig.getSystemConfig();
        for (Map.Entry<String, Object> entry : systemConfig.entrySet()) {
            container.data(entry.getKey(), entry.getValue());
        }
    }

    private void setupRuntimeData(DataContainer container,
                                Map<String, Object> requestData,
                                IntegrationTaskConfig taskConfig) {
        // 生成运行时标识
        Map<String, Object> runtimeData = new HashMap<>();
        runtimeData.put("requestId", UUID.randomUUID().toString());
        runtimeData.put("startTime", System.currentTimeMillis());

        // 添加配置的运行时字段
        Set<String> runtimeFields = taskConfig.getRuntimeFields();
        for (String fieldName : runtimeFields) {
            if (requestData.containsKey(fieldName)) {
                runtimeData.put(fieldName, requestData.get(fieldName));
            }
        }

        // 创建执行上下文
        ExecutionContext executionContext = createExecutionContext(taskConfig);
        runtimeData.put("executionContext", executionContext);

        // 创建API客户端
        if (taskConfig.requiresApiClient()) {
            ApiClient apiClient = createApiClient(taskConfig);
            runtimeData.put("apiClient", apiClient);
        }

        // 批量设置临时数据
        for (Map.Entry<String, Object> entry : runtimeData.entrySet()) {
            container.transientData(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 动态处理集成结果 - 支持灵活的结果字段
     */
    public void handleIntegrationResult(DataContainer container,
                                      Map<String, Object> resultData,
                                      IntegrationResultConfig resultConfig) {
        // 动态持久化结果数据
        persistResultData(container, resultData, resultConfig);

        // 动态清理临时数据
        cleanupRuntimeData(container, resultConfig);

        // 动态设置后续处理数据
        setupPostProcessingData(container, resultData, resultConfig);
    }

    private void persistResultData(DataContainer container,
                                 Map<String, Object> resultData,
                                 IntegrationResultConfig resultConfig) {
        // 根据配置持久化结果字段
        Set<String> persistentResultFields = resultConfig.getPersistentResultFields();

        for (String fieldName : persistentResultFields) {
            if (resultData.containsKey(fieldName)) {
                Object value = resultData.get(fieldName);
                container.data(fieldName, value);
            }
        }
    }

    private void cleanupRuntimeData(DataContainer container,
                                  IntegrationResultConfig resultConfig) {
        // 根据配置清理临时数据
        Set<String> cleanupFields = resultConfig.getCleanupFields();

        for (String fieldName : cleanupFields) {
            container.removeTransientData(fieldName);
        }
    }

    private void setupPostProcessingData(DataContainer container,
                                       Map<String, Object> resultData,
                                       IntegrationResultConfig resultConfig) {
        // 检查是否有错误需要重试
        if (hasErrors(resultData, resultConfig)) {
            Map<String, Object> retryData = new HashMap<>();
            retryData.put("retryRequired", true);
            retryData.put("nextRetryTime", System.currentTimeMillis() + resultConfig.getRetryDelay());
            retryData.put("errorDetails", extractErrorDetails(resultData, resultConfig));

            // 设置重试相关的临时数据
            for (Map.Entry<String, Object> entry : retryData.entrySet()) {
                container.transientData(entry.getKey(), entry.getValue());
            }
        }
    }

    private boolean hasErrors(Map<String, Object> resultData,
                            IntegrationResultConfig resultConfig) {
        // 根据配置检查错误条件
        Set<String> errorIndicatorFields = resultConfig.getErrorIndicatorFields();

        for (String fieldName : errorIndicatorFields) {
            Object value = resultData.get(fieldName);
            if (resultConfig.isErrorValue(fieldName, value)) {
                return true;
            }
        }
        return false;
    }

    private Map<String, Object> extractErrorDetails(Map<String, Object> resultData,
                                                   IntegrationResultConfig resultConfig) {
        Map<String, Object> errorDetails = new HashMap<>();
        Set<String> errorDetailFields = resultConfig.getErrorDetailFields();

        for (String fieldName : errorDetailFields) {
            if (resultData.containsKey(fieldName)) {
                errorDetails.put(fieldName, resultData.get(fieldName));
            }
        }
        return errorDetails;
    }
}
```

## 7. 监控和调试

### 7.1 基于数据类型的监控实现
```java
public WorkflowExecutionStatus getExecutionStatus(WorkflowInstanceImpl workflowInstance,
                                                 MonitoringConfig monitoringConfig) {

    // 动态获取持久化的业务状态
    Map<String, Object> businessStatus = extractBusinessStatus(workflowInstance, monitoringConfig);

    // 动态获取实时运行状态
    Map<String, Object> runtimeStatus = extractRuntimeStatus(workflowInstance, monitoringConfig);

    // 构建状态对象
    return buildExecutionStatus(businessStatus, runtimeStatus, monitoringConfig);
}

private Map<String, Object> extractBusinessStatus(WorkflowInstanceImpl workflowInstance,
                                                 MonitoringConfig monitoringConfig) {
    Map<String, Object> businessStatus = new HashMap<>();
    Set<String> businessStatusFields = monitoringConfig.getBusinessStatusFields();

    // 动态提取业务状态字段
    for (String fieldName : businessStatusFields) {
        Object value = workflowInstance.getVariableValue(fieldName);
        if (value != null) {
            businessStatus.put(fieldName, value);
        }
    }
    return businessStatus;
}

private Map<String, Object> extractRuntimeStatus(WorkflowInstanceImpl workflowInstance,
                                                MonitoringConfig monitoringConfig) {
    Map<String, Object> runtimeStatus = new HashMap<>();
    Set<String> runtimeStatusFields = monitoringConfig.getRuntimeStatusFields();

    // 动态提取运行时状态字段
    for (String fieldName : runtimeStatusFields) {
        Object value = workflowInstance.getTransientProperty(fieldName);
        if (value != null) {
            runtimeStatus.put(fieldName, value);
        }
    }
    return runtimeStatus;
}

private WorkflowExecutionStatus buildExecutionStatus(Map<String, Object> businessStatus,
                                                    Map<String, Object> runtimeStatus,
                                                    MonitoringConfig monitoringConfig) {
    WorkflowExecutionStatus.Builder builder = WorkflowExecutionStatus.builder();

    // 动态设置业务状态字段
    for (Map.Entry<String, Object> entry : businessStatus.entrySet()) {
        String fieldName = entry.getKey();
        Object value = entry.getValue();
        builder.businessField(fieldName, value);
    }

    // 动态设置运行时状态字段
    for (Map.Entry<String, Object> entry : runtimeStatus.entrySet()) {
        String fieldName = entry.getKey();
        Object value = entry.getValue();
        builder.runtimeField(fieldName, value);
    }

    return builder.build();
}
```

## 8. 动态字段处理配置类

为了支持动态字段处理，需要定义相应的配置类：

### 8.1 字段映射配置
```java
public class FieldMappingConfig {
    private Map<String, String> fieldMappings;           // 源字段 -> 目标字段映射
    private Map<String, DataType> fieldDataTypes;        // 字段数据类型定义
    private Set<String> businessFields;                  // 业务字段集合
    private Set<String> runtimeFields;                   // 运行时字段集合

    public boolean isBusinessField(String fieldName) {
        return businessFields.contains(fieldName);
    }

    public String getTargetField(String sourceField) {
        return fieldMappings.getOrDefault(sourceField, sourceField);
    }

    public DataType getDataType(String fieldName) {
        return fieldDataTypes.get(fieldName);
    }
}
```

### 8.2 上下文过滤配置
```java
public class ContextFilterConfig {
    private Set<String> allowedFields;                   // 允许的字段集合
    private Set<String> excludedFields;                  // 排除的字段集合
    private Map<String, Predicate<Object>> valueFilters; // 值过滤器

    public boolean shouldInclude(String fieldName, Object value) {
        // 检查字段是否被排除
        if (excludedFields.contains(fieldName)) {
            return false;
        }

        // 检查字段是否在允许列表中
        if (!allowedFields.isEmpty() && !allowedFields.contains(fieldName)) {
            return false;
        }

        // 应用值过滤器
        Predicate<Object> valueFilter = valueFilters.get(fieldName);
        if (valueFilter != null) {
            return valueFilter.test(value);
        }

        return true;
    }
}
```

### 8.3 安全配置
```java
public class SecurityConfig {
    private Set<String> sensitiveFields;                 // 敏感字段集合
    private Pattern sensitiveFieldPattern;               // 敏感字段模式

    public boolean isSensitiveField(String fieldName) {
        // 检查是否在敏感字段列表中
        if (sensitiveFields.contains(fieldName)) {
            return true;
        }

        // 检查是否匹配敏感字段模式
        if (sensitiveFieldPattern != null) {
            return sensitiveFieldPattern.matcher(fieldName).matches();
        }

        return false;
    }
}
```

### 8.4 数据转换规则
```java
public class DataTransformationRules {
    private Map<String, FieldTransformationRule> rules;

    public FieldTransformationRule getRule(String sourceField) {
        return rules.get(sourceField);
    }
}

public class FieldTransformationRule {
    private String targetField;
    private Function<Object, Object> transformer;

    public String getTargetField() {
        return targetField;
    }

    public Object transform(Object sourceValue) {
        return transformer != null ? transformer.apply(sourceValue) : sourceValue;
    }
}
```

### 8.5 通用数据处理工具
```java
public class DynamicDataProcessor {

    /**
     * 通用字段复制方法
     */
    public static void copyFields(Map<String, Object> source,
                                DataContainer target,
                                FieldMappingConfig mappingConfig,
                                boolean isPersistent) {
        for (Map.Entry<String, Object> entry : source.entrySet()) {
            String sourceField = entry.getKey();
            Object value = entry.getValue();

            if (mappingConfig.isBusinessField(sourceField)) {
                String targetField = mappingConfig.getTargetField(sourceField);
                DataType dataType = mappingConfig.getDataType(sourceField);

                if (isPersistent) {
                    if (dataType != null) {
                        target.data(targetField, value, dataType);
                    } else {
                        target.data(targetField, value);
                    }
                } else {
                    target.transientData(targetField, value);
                }
            }
        }
    }

    /**
     * 动态类型推断
     */
    public static DataType inferDataType(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            return TextType.INSTANCE;
        } else if (value instanceof Number) {
            return NumberType.INSTANCE;
        } else if (value instanceof Boolean) {
            return BooleanType.INSTANCE;
        } else if (value instanceof Date) {
            return DateType.INSTANCE;
        } else if (value instanceof List) {
            return new ListType();
        } else if (value instanceof Map) {
            return ObjectType.INSTANCE;
        } else {
            return ObjectType.INSTANCE;
        }
    }

    /**
     * 批量字段验证
     */
    public static Map<String, String> validateFields(Map<String, Object> data,
                                                    FieldValidationConfig validationConfig) {
        Map<String, String> errors = new HashMap<>();

        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();

            FieldValidator validator = validationConfig.getValidator(fieldName);
            if (validator != null) {
                String error = validator.validate(value);
                if (error != null) {
                    errors.put(fieldName, error);
                }
            }
        }

        return errors;
    }
}
```

## 9. 总结

通过采用动态字段处理方式和合理使用 `data` 和 `transientData` 字段，可以在 iPaaS 集成平台中实现：

1. **动态数据适配**：无需硬编码字段名称，支持任意数据源结构
2. **灵活的字段映射**：通过配置实现源字段到目标字段的动态映射
3. **智能类型推断**：自动识别数据类型，减少手动配置工作
4. **可配置的数据处理**：通过配置类控制数据的处理逻辑和生命周期
5. **高效的数据管理**：区分持久化和临时数据，优化存储和性能
6. **安全的信息处理**：通过配置识别敏感字段，避免意外持久化
7. **优化的内存使用**：及时释放临时数据，避免内存泄漏
8. **完善的监控支持**：基于配置实现动态监控字段选择
9. **强大的扩展性**：支持新数据源的快速接入，无需修改核心代码

这种动态字段处理设计模式是现代 iPaaS 平台中数据管理的最佳实践，为构建高性能、可扩展、易维护的集成平台提供了坚实的基础。通过配置驱动的方式，平台能够快速适应不同数据源的字段结构变化，大大提高了系统的灵活性和可维护性。
