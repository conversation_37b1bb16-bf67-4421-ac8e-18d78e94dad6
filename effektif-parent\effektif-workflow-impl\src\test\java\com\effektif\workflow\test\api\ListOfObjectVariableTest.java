package com.effektif.workflow.test.api;

import com.effektif.workflow.api.activities.*;
import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.types.TextType;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflow.MultiInstance;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.effektif.workflow.impl.data.types.ObjectType;
import com.effektif.workflow.impl.exceptions.ActivityInstanceBeyondMaxException;
import com.effektif.workflow.test.WorkflowTest;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.effektif.workflow.api.ext.WorkflowBindingEnum.maxActivityInstance;
import static org.junit.Assert.assertTrue;


/**
 * <AUTHOR>
 */
public class ListOfObjectVariableTest extends WorkflowTest {


    /**
     * 对象类型的List变量支持
     * 1. 多实例
     * 2. 分支
     * 3. 单独存储
     * 4. List变量的操作
     */
    @Test
    public void testListOfObjectVariable() {
        // @formatter:off
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("start", new StartEvent()
                        .transitionTo("t1"))
                .activity("t1", new LoopTask().activity("subProcess",
                        new EmbeddedSubprocess()
                                .multiInstance(new MultiInstance().valuesExpression("loopVariables").variable("loopVariable", new ObjectType()))
                                .activity("sn1", new BlockExecutionTask())
                ).transitionTo("t2"))
                .activity("t2", new NoneTask()
                        .transitionTo("end"))
                .activity("end", new EndEvent());
        // @formatter:on

        deploy(workflow);
        List<Map<String, Object>> loopVariables = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            final int index = i;
            loopVariables.add(new HashMap<String, Object>() {
                {
                    put("name", "test" + index);
                    put("age", index);
                }
            });
        }
        TriggerInstance triggerInstance = new TriggerInstance("71557")
                .workflowId(workflow.getId()).data("loopVariables", loopVariables);

        WorkflowInstance instance = workflowEngine.start(triggerInstance);
        assertTrue(instance.isEnded());
    }

}
