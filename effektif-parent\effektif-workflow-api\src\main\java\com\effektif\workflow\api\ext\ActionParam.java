package com.effektif.workflow.api.ext;

import java.io.Serializable;

/**
 * *
 * Created by wangbaifeng on 18/1/30.
 */
public class ActionParam implements Serializable {
  private String fromField;
  private String toField;
  private String fieldFlag;
  private Object fieldValue;

  public ActionParam() {
  }

  public ActionParam(String fromField, String toField, Object fieldValue) {
    this.fromField = fromField;
    this.toField = toField;
    this.fieldValue = fieldValue;
  }

  public String getFromField() {
    return fromField;
  }

  public void setFromField(String fromField) {
    this.fromField = fromField;
  }

  public String getToField() {
    return toField;
  }

  public void setToField(String toField) {
    this.toField = toField;
  }

  public Object getFieldValue() {
    return fieldValue;
  }

  public void setFieldValue(Object fieldValue) {
    this.fieldValue = fieldValue;
  }

  public String getFieldFlag() {
    return fieldFlag;
  }

  public void setFieldFlag(String fieldFlag) {
    this.fieldFlag = fieldFlag;
  }
}
