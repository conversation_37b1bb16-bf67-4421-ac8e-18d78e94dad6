package com.fxiaoke.erpdss.ipaas.base.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * CheckController集成测试
 * 
 * <AUTHOR> (^_−)☆
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
class BaseCheckControllerIT {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Test
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    /**
     * 测试check接口 - 正常情况
     */
    @Test
    void testCheckEndpoint_Success() throws Exception {
        if (mockMvc == null) {
            setUp();
        }
        
        mockMvc.perform(get("/base/check"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType("text/plain;charset=UTF-8"))
                .andExpect(content().string("hello,erpdss base!"));
    }

    /**
     * 测试check接口 - 验证响应内容
     */
    @Test
    void testCheckEndpoint_ResponseContent() throws Exception {
        if (mockMvc == null) {
            setUp();
        }
        
        mockMvc.perform(get("/base/check"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(result -> {
                    String content = result.getResponse().getContentAsString();
                    assert content.contains("hello");
                    assert content.contains("erpdss");
                    assert content.contains("base");
                });
    }

    /**
     * 测试不存在的接口路径
     */
    @Test
    void testNonExistentEndpoint_NotFound() throws Exception {
        if (mockMvc == null) {
            setUp();
        }
        
        mockMvc.perform(get("/base/nonexistent"))
                .andDo(print())
                .andExpect(status().isNotFound());
    }

    /**
     * 测试错误的HTTP方法
     */
    @Test
    void testCheckEndpoint_WrongMethod() throws Exception {
        if (mockMvc == null) {
            setUp();
        }
        
        mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post("/base/check"))
                .andDo(print())
                .andExpect(status().isMethodNotAllowed());
    }
}
