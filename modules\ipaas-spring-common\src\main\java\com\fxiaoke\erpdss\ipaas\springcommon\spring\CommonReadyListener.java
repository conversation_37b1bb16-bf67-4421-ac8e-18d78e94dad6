package com.fxiaoke.erpdss.ipaas.springcommon.spring;

import cn.hutool.core.util.ArrayUtil;
import com.fxiaoke.erpdss.ipaas.common.i18n.I18nUtil;
import com.fxiaoke.erpdss.ipaas.springcommon.util.TraceUtil;
import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.bean.LocalLogPolicy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.stereotype.Component;

/**
 * 服务启动后动作
 *
 * <AUTHOR> (^_−)☆
 */
@Component
@Slf4j
public class CommonReadyListener {
    I18nClient i18nClient;

    @EventListener
    public void handleApplicationReady(ApplicationReadyEvent event) {
        log.info("Application is ready!");
        // 在这里执行启动后的操作

        //替换I18nUtil的转换方法，但是，在集成测试和本地调试时，不启动。
        ConfigurableEnvironment env = event.getApplicationContext().getEnvironment();
        String[] activeProfiles = env.getActiveProfiles();
        if (!ArrayUtil.contains(activeProfiles, "test")) {
            if (i18nClient == null) {
                i18nClient = new I18nClient();
                i18nClient.initWithTags("erpdss");
                I18nUtil._SetI18nFunction((i18nKey, defaultValue) -> i18nClient.getOrDefault(i18nKey, 0, getLocaleFromContext(), defaultValue));
            }
        }
        //设置localLogPolicy
        System.setProperty("localLogPolicy", LocalLogPolicy.WARN.name());
    }


    /**
     * 从traceContext读取当前语言环境
     */
    private static String getLocaleFromContext() {
        String locale = TraceUtil.getLocale();
        if (locale != null && !locale.isEmpty()) {
            return locale;
        }
        return "zh-CN";
    }
}
