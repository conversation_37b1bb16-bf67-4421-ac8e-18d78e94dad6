package com.effektif.workflow.api.activities;

import com.effektif.workflow.api.bpmn.BpmnElement;
import com.effektif.workflow.api.json.TypeName;
import com.effektif.workflow.api.workflow.Activity;

import java.util.Map;

/**
 * liugh
 * 19/2/26 16:00
 */
@TypeName("latencyTask")
@BpmnElement("latencyTask")
public class UserLatencyTask extends Activity {
  private String taskId;
  /**
   * 节点停留时间
   */
  protected Object latencyTime;
  /**
   * 节点停留时间的单位
   */
  protected String latencyUnit;

  /**
   * 定时等待节点的等待方式：1-设置等待时间，2-设置等待到未来某个时间点
   */
  protected String timeType;
  /**
   * 定时等待节点的时间扩展
   */
  protected Map<String, Object> eventExtension;

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public String getTaskId() {
    return taskId;
  }

  public Object getLatencyTime() {
    return latencyTime;
  }

  public String getLatencyUnit() {
    return latencyUnit;
  }

  public String getTimeType() {
    return timeType;
  }

  public Map<String, Object> getEventExtension() {
    return eventExtension;
  }

  public void setLatencyTime(Object latencyTime) {
    this.latencyTime = latencyTime;
  }

  public void setLatencyUnit(String latencyUnit) {
    this.latencyUnit = latencyUnit;
  }

  public void setTimeType(String timeType) {
    this.timeType = timeType;
  }

  public void setEventExtension(Map<String, Object> eventExtension) {
    this.eventExtension = eventExtension;
  }
}
