# iPaaS 项目测试规范

## 概述

本文档定义了 iPaaS 项目的测试命名规范、技术栈和 CI 自动化配置。

## 测试命名规范

### 单元测试
- **命名规范**: `*Test.java`
- **示例**: `UserServiceTest.java`, `OrderControllerTest.java`
- **用途**: 测试单个类或方法的功能，使用模拟对象隔离依赖

### 集成测试
- **命名规范**: `*IT.java`
- **示例**: `CheckControllerIT.java`, `UserServiceIT.java`
- **用途**: 测试多个组件之间的交互，通常需要启动 Spring 上下文

## 技术栈

### 单元测试技术栈
- **JUnit 5**: 测试框架
- **Mockito**: 用于创建模拟对象
- **AssertJ**: 提供流畅的断言API

### 集成测试技术栈
- **JUnit 5**: 测试框架
- **Spring Boot Test**: 提供 Spring 应用程序测试支持
- **常用注解**:
  - `@SpringBootTest`: 加载完整的Spring应用程序上下文
  - `@DataJpaTest`: 用于测试JPA组件
  - `@MockBean`: 在Spring上下文中添加Mockito模拟
  - `@AutoConfigureMockMvc`: 模拟 HTTP 请求和响应

## CI 自动化配置

### Maven Surefire 插件配置
- **执行范围**: 仅执行 `*Test.java` 文件
- **排除**: 不执行 `*IT.java` 文件
- **目的**: 在常规构建中只运行快速的单元测试

### Maven Failsafe 插件配置
- **执行范围**: 执行 `*IT.java` 文件
- **用途**: 在集成测试阶段运行需要更多资源的集成测试

## 运行测试

### 运行单元测试
```bash
# 运行所有单元测试
mvn test

# 运行特定的单元测试类
mvn test -Dtest=UserServiceTest
```

### 运行集成测试
```bash
# 运行所有集成测试
mvn verify

# 运行特定的集成测试类
mvn test -Dtest=CheckControllerIT -Dspring.profiles.active=test
```

## 最佳实践

### 测试组织
1. 测试类应按照被测试类的包结构组织
2. 集成测试和单元测试应分开放置
3. 测试资源文件应统一管理

### 测试编写
1. 遵循 AAA (Arrange-Act-Assert) 模式
2. 每个测试方法只测试一个功能点
3. 使用描述性的测试方法命名
4. 包含正常流程、边界条件和异常流程测试

### 测试环境
1. 使用专门的测试配置文件
2. 使用 `@ActiveProfiles("test")` 激活测试配置
3. 确保测试数据的隔离性

## 更新历史

- 2025-01-14: 将集成测试文件从 `*IntegrationTest.java` 重命名为 `*IT.java` 以符合 Maven 插件约定
- 2025-01-14: 更新相关脚本和文档中的引用
