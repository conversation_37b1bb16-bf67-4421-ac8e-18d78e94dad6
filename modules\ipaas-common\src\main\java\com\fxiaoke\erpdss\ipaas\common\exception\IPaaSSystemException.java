package com.fxiaoke.erpdss.ipaas.common.exception;

import com.fxiaoke.erpdss.ipaas.common.module.ResultCode;
import lombok.experimental.StandardException;

import java.io.Serial;

/**
 * 系统异常
 * <br/>内部不处理多语，msg应该是多语转换后放进来。
 * <AUTHOR> (^_−)☆
 */
@StandardException
public class IPaaSSystemException extends IPaaSException {
    @Serial
    private static final long serialVersionUID = 8417993302125159977L;

    public IPaaSSystemException(String msg) {
        super(ResultCode.SYSTEM_ERROR.getCode(), msg);
    }


    public IPaaSSystemException(String msg, Throwable cause) {
        super(ResultCode.SYSTEM_ERROR.getCode(), msg, cause);
    }

    public IPaaSSystemException(String code, String msg) {
        super(code, msg);
    }

    public IPaaSSystemException(String code, String msg, Throwable cause) {
        super(code, msg, cause);
    }
}
