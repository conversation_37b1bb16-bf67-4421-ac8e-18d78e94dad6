package com.effektif.workflow.test.api;

import com.effektif.workflow.api.activities.BlockExecutionTask;
import com.effektif.workflow.api.activities.EndEvent;
import com.effektif.workflow.api.activities.ExecutionItem;
import com.effektif.workflow.api.activities.StartEvent;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.effektif.workflow.test.WorkflowTest;
import org.junit.Test;

import static org.junit.Assert.assertTrue;


/**
 * <AUTHOR>
 */
public class BlockExecutionServiceTest extends WorkflowTest {

    /**
     * <pre>
     *  [start]──[blockExecution]─→[end]
     * </pre>
     */
    @Test
    public void testBlock() {
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("start", new StartEvent()
                        .transitionTo("auto_block"))
                .activity("auto_block", new BlockExecutionTask().item(new ExecutionItem()).executeType("task").transitionTo("end"))
                .activity("end", new EndEvent());

        deploy(workflow);

        WorkflowInstance workflowInstance = start(workflow);
        assertOpen(workflowInstance, "auto_block");

        workflowInstance = endTask(workflowInstance, "auto_block");
        assertTrue(workflowInstance.isEnded());
    }

    /**
     * <pre>
     *  [start]──[blockExecution]─→[end]
     * </pre>
     */
    @Test
    public void testAuto() {
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("start", new StartEvent()
                        .transitionTo("auto_block"))
                .activity("auto_block", new BlockExecutionTask().item(new ExecutionItem())
                        .executeType("auto").transitionTo("end"))
                .activity("end", new EndEvent());

        deploy(workflow);

        WorkflowInstance workflowInstance = start(workflow);
        assertTrue(workflowInstance.isEnded());
    }

}
