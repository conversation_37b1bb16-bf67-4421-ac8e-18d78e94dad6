package com.fxiaoke.erpdss.ipaas.common.module;

import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSBizException;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSException;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSSystemException;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Result 单元测试
 * 
 * <AUTHOR> (^_−)☆
 */
class ResultTest {

    @Test
    void testSuccessWithoutData() {
        // When
        Result<Void> result = Result.success();

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.SUCCESS.getCode());
        assertThat(result.getMessage()).isEqualTo(ResultCode.SUCCESS.getI18nMsg());
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.isFailed()).isFalse();
    }

    @Test
    void testSuccessWithData() {
        // Given
        String testData = "测试数据";

        // When
        Result<String> result = Result.success(testData);

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.SUCCESS.getCode());
        assertThat(result.getMessage()).isEqualTo(ResultCode.SUCCESS.getI18nMsg());
        assertThat(result.getData()).isEqualTo(testData);
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.isFailed()).isFalse();
    }

    @Test
    void testSuccessWithCustomMessage() {
        // Given
        String data = "测试数据";
        String message = "操作成功";

        // When
        Result<String> result = Result.success(data, message);

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.SUCCESS.getCode());
        assertThat(result.getMessage()).isEqualTo(message);
        assertThat(result.getData()).isEqualTo(data);
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.isFailed()).isFalse();
    }

    @Test
    void testSuccessWithDataAndMessage() {
        // Given
        String testData = "测试数据";
        String message = "操作成功";

        // When
        Result<String> result = Result.success(testData, message);

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.SUCCESS.getCode());
        assertThat(result.getMessage()).isEqualTo(message);
        assertThat(result.getData()).isEqualTo(testData);
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.isFailed()).isFalse();
    }

    @Test
    void testErrorWithoutParameters() {
        // When
        Result<Void> result = Result.error();

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.SYSTEM_ERROR.getCode());
        assertThat(result.getMessage()).isEqualTo(ResultCode.SYSTEM_ERROR.getI18nMsg());
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testErrorWithMessage() {
        // Given
        String message = "操作失败";

        // When
        Result<Void> result = Result.error(message);

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.SYSTEM_ERROR.getCode());
        assertThat(result.getMessage()).isEqualTo(message);
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testErrorWithCodeAndMessage() {
        // Given
        String code = "CUSTOM_ERROR";
        String message = "自定义错误";

        // When
        Result<Void> result = Result.error(code, message);

        // Then
        assertThat(result.getCode()).isEqualTo(code);
        assertThat(result.getMessage()).isEqualTo(message);
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testErrorWithResultCode() {
        // When
        Result<Void> result = Result.error(ResultCode.BIZ_ERROR);

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(result.getMessage()).isEqualTo(ResultCode.BIZ_ERROR.getI18nMsg());
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testErrorWithResultCodeAndCustomMessage() {
        // Given
        String customMessage = "自定义错误消息";

        // When
        Result<Void> result = Result.error(ResultCode.BIZ_ERROR, customMessage);

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(result.getMessage()).isEqualTo(customMessage);
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testErrorWithIPaaSBizException() {
        // Given
        IPaaSBizException exception = new IPaaSBizException("业务异常消息");

        // When
        Result<Void> result = Result.error(exception);

        // Then
        assertThat(result.getCode()).isEqualTo(exception.getCode());
        assertThat(result.getMessage()).isEqualTo(exception.getMessage());
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testErrorWithIPaaSSystemException() {
        // Given
        IPaaSSystemException exception = new IPaaSSystemException("系统异常消息");

        // When
        Result<Void> result = Result.error(exception);

        // Then
        assertThat(result.getCode()).isEqualTo(exception.getCode());
        assertThat(result.getMessage()).isEqualTo(exception.getMessage());
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testErrorWithCustomIPaaSException() {
        // Given
        IPaaSException exception = new IPaaSBizException("CUSTOM_CODE", "自定义异常");

        // When
        Result<Void> result = Result.error(exception);

        // Then
        assertThat(result.getCode()).isEqualTo("CUSTOM_CODE");
        assertThat(result.getMessage()).isEqualTo("自定义异常");
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testIsSuccessWithDifferentCodes() {
        // Given
        Result<Void> successResult = Result.<Void>builder()
                .code("s199999999")
                .message("成功")
                .build();
        
        Result<Void> warningResult = Result.<Void>builder()
                .code("s299999999")
                .message("警告")
                .build();
        
        Result<Void> errorResult = Result.<Void>builder()
                .code("s399999999")
                .message("错误")
                .build();

        // When & Then
        assertThat(successResult.isSuccess()).isTrue();
        assertThat(successResult.isFailed()).isFalse();
        
        assertThat(warningResult.isSuccess()).isFalse();
        assertThat(warningResult.isFailed()).isTrue();
        
        assertThat(errorResult.isSuccess()).isFalse();
        assertThat(errorResult.isFailed()).isTrue();
    }

    @Test
    void testBuilderPattern() {
        // Given
        String code = "TEST_CODE";
        String message = "测试消息";
        String data = "测试数据";

        // When
        Result<String> result = Result.<String>builder()
                .code(code)
                .message(message)
                .data(data)
                .build();

        // Then
        assertThat(result.getCode()).isEqualTo(code);
        assertThat(result.getMessage()).isEqualTo(message);
        assertThat(result.getData()).isEqualTo(data);
    }

    @Test
    void testWithNullValues() {
        // When
        Result<String> result = Result.<String>builder()
                .code(null)
                .message(null)
                .data(null)
                .build();

        // Then
        assertThat(result.getCode()).isNull();
        assertThat(result.getMessage()).isNull();
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse(); // null code is not success
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testGenericTypes() {
        // Given
        Integer intData = 123;
        Boolean boolData = true;

        // When
        Result<Integer> intResult = Result.success(intData);
        Result<Boolean> boolResult = Result.success(boolData);

        // Then
        assertThat(intResult.getData()).isEqualTo(intData);
        assertThat(boolResult.getData()).isEqualTo(boolData);
    }

    @Test
    void testTimestamp() {
        // Given
        long beforeTime = System.currentTimeMillis();

        // When
        Result<String> result = Result.success("test");
        long afterTime = System.currentTimeMillis();

        // Then
        assertThat(result.getTimestamp()).isBetween(beforeTime, afterTime);
    }

    @Test
    void testTimestampWithBuilder() {
        // Given
        long customTimestamp = 1234567890L;

        // When
        Result<String> result = Result.<String>builder()
                .code("TEST")
                .message("测试")
                .data("数据")
                .timestamp(customTimestamp)
                .build();

        // Then
        assertThat(result.getTimestamp()).isEqualTo(customTimestamp);
    }

    @Test
    void testErrorWithResultCodeOnly() {
        // When
        Result<Void> result = Result.error(ResultCode.PARAM_ILLEGAL);

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(result.getMessage()).isEqualTo(ResultCode.PARAM_ILLEGAL.getI18nMsg());
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testSuccessWithNullData() {
        // When
        Result<String> result = Result.success((String) null);

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.SUCCESS.getCode());
        assertThat(result.getMessage()).isEqualTo(ResultCode.SUCCESS.getI18nMsg());
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.isFailed()).isFalse();
    }

    @Test
    void testSuccessWithNullDataAndMessage() {
        // When
        Result<String> result = Result.success(null, null);

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.SUCCESS.getCode());
        assertThat(result.getMessage()).isNull();
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.isFailed()).isFalse();
    }

    @Test
    void testErrorWithNullCodeAndMessage() {
        // When
        Result<Void> result = Result.error((String) null, (String) null);

        // Then
        assertThat(result.getCode()).isNull();
        assertThat(result.getMessage()).isNull();
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse(); // null code is not success
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testErrorWithEmptyCodeAndMessage() {
        // When
        Result<Void> result = Result.error("", "");

        // Then
        assertThat(result.getCode()).isEqualTo("");
        assertThat(result.getMessage()).isEqualTo("");
        assertThat(result.getData()).isNull();
        assertThat(result.isSuccess()).isFalse(); // empty code is not success
        assertThat(result.isFailed()).isTrue();
    }

    @Test
    void testComplexGenericTypes() {
        // Given
        java.util.List<String> listData = java.util.Arrays.asList("item1", "item2", "item3");
        java.util.Map<String, Integer> mapData = java.util.Map.of("key1", 1, "key2", 2);

        // When
        Result<java.util.List<String>> listResult = Result.success(listData);
        Result<java.util.Map<String, Integer>> mapResult = Result.success(mapData);

        // Then
        assertThat(listResult.getData()).isEqualTo(listData);
        assertThat(listResult.getData()).hasSize(3);

        assertThat(mapResult.getData()).isEqualTo(mapData);
        assertThat(mapResult.getData()).hasSize(2);
    }

    @Test
    void testResultEquality() {
        // Given
        Result<String> result1 = Result.<String>builder()
                .code("TEST_CODE")
                .message("测试消息")
                .data("测试数据")
                .timestamp(1000L)
                .build();

        Result<String> result2 = Result.<String>builder()
                .code("TEST_CODE")
                .message("测试消息")
                .data("测试数据")
                .timestamp(1000L)
                .build();

        Result<String> result3 = Result.<String>builder()
                .code("DIFFERENT_CODE")
                .message("测试消息")
                .data("测试数据")
                .timestamp(1000L)
                .build();

        // When & Then
        assertThat(result1).isEqualTo(result2);
        assertThat(result1).isNotEqualTo(result3);
        assertThat(result1.hashCode()).isEqualTo(result2.hashCode());
    }

    @Test
    void testResultToString() {
        // Given
        Result<String> result = Result.<String>builder()
                .code("TEST_CODE")
                .message("测试消息")
                .data("测试数据")
                .timestamp(1000L)
                .build();

        // When
        String resultString = result.toString();

        // Then
        assertThat(resultString).contains("TEST_CODE");
        assertThat(resultString).contains("测试消息");
        assertThat(resultString).contains("测试数据");
        assertThat(resultString).contains("1000");
    }

    @Test
    void testDefaultTimestampBehavior() {
        // Given
        long beforeTime = System.currentTimeMillis();

        // When
        Result<String> result1 = Result.<String>builder()
                .code("TEST")
                .message("测试")
                .data("数据")
                .build(); // 不设置timestamp，应该使用默认值

        Result<String> result2 = Result.success("数据");

        long afterTime = System.currentTimeMillis();

        // Then
        assertThat(result1.getTimestamp()).isBetween(beforeTime, afterTime);
        assertThat(result2.getTimestamp()).isBetween(beforeTime, afterTime);
    }

    @Test
    void testBuilderWithPartialData() {
        // When
        Result<String> result = Result.<String>builder()
                .code("PARTIAL_CODE")
                .build(); // 只设置code，其他使用默认值

        // Then
        assertThat(result.getCode()).isEqualTo("PARTIAL_CODE");
        assertThat(result.getMessage()).isNull();
        assertThat(result.getData()).isNull();
        assertThat(result.getTimestamp()).isGreaterThan(0);
        assertThat(result.isSuccess()).isFalse(); // PARTIAL_CODE不是成功代码
        assertThat(result.isFailed()).isTrue();
    }
}
