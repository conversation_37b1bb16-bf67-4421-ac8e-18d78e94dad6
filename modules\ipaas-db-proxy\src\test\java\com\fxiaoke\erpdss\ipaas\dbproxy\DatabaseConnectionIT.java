package com.fxiaoke.erpdss.ipaas.dbproxy;

import com.fxiaoke.erpdss.ipaas.dbproxy.dao.ErpIPaaSMongoDao;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Set;

/**
 * 数据库连接集成测试
 * 使用 Testcontainers 启动 MongoDB 4.0 容器进行测试
 *
 * <AUTHOR> (^_−)☆
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
class DatabaseConnectionIT {
    @Autowired
    private ErpIPaaSMongoDao erpIPaaSMongoDao;

    @Test
    void test() {
        Set<String> strings = erpIPaaSMongoDao.listCollNames(ErpIPaaSMongoDao.COMMON_TENANT_ID);
        log.info("collections: {}", strings);
        Assertions.assertNotNull(strings);
    }
}
