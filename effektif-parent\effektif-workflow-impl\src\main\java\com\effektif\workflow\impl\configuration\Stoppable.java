/* Copyright (c) 2014, Effektif GmbH.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License. */
package com.effektif.workflow.impl.configuration;


/** Callback invoked by the {@link Brewery} when it is {@link Brewery#stop()} stopped}.
 * 
 * <p>Stoppables are stopped in the reverse order as they are added or created.</p>
 */
public interface Stoppable {

  /** Callback invoked by the {@link Brewery} when it is {@link Brewery#stop()} stopped}.
   *
   * <p>Stoppables are stopped in the reverse order as they are added or created.</p> */
  void stop(Brewery brewery);
}
