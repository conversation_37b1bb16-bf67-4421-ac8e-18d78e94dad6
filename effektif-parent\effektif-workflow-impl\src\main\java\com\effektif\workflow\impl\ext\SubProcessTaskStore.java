package com.effektif.workflow.impl.ext;

import com.effektif.workflow.api.model.TaskId;

public interface SubProcessTaskStore {
    TaskId generateTaskId();

    void insertTask(SubProcessTask task);


    SubProcessTask updateState(String tenantId, String superWorkflowInstanceId , String subProcessInstanceId, String actionType, Long end);

    SubProcessTask findById(String tenantId, String taskId);
}
