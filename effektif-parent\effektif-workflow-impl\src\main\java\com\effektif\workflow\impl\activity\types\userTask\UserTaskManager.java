package com.effektif.workflow.impl.activity.types.userTask;

import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.impl.activity.types.UserTaskImpl;
import com.effektif.workflow.impl.activity.types.userTask.handler.create.UserTaskCreateApprovalHandler;
import com.effektif.workflow.impl.activity.types.userTask.handler.create.UserTaskCreateBpmHandler;
import com.effektif.workflow.impl.activity.types.userTask.handler.create.UserTaskCreateHandler;
import com.effektif.workflow.impl.activity.types.userTask.handler.create.UserTaskCreateStageHandler;
import com.effektif.workflow.impl.ext.Task;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.facishare.paas.workflow.bus.EngineEventBus;
import com.facishare.paas.workflow.bus.api.CreateTaskEvent;
import com.facishare.paas.workflow.bus.api.type.FlowTag;
import com.facishare.paas.workflow.bus.api.type.TaskState;
import com.facishare.paas.workflow.bus.model.BusConstant;
import com.facishare.paas.workflow.bus.model.MQContext;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.Objects;

import static com.effektif.workflow.impl.activity.types.userTask.handler.create.UserTaskCreateHandler.setTaskBasicProperties;

/**
 * <AUTHOR>
 * @date 2023/10/1
 * @apiNote 用来管理各种类型的流程的处理逻辑， 目前都在UserTaskImpl中， 导致比较臃肿
 **/
public class UserTaskManager {

    public static final Map<String, UserTaskCreateHandler> handlers;

    static {
        handlers = Maps.newHashMap();
        handlers.put(WorkflowConstants.WorkflowType.BPM, new UserTaskCreateBpmHandler());
        handlers.put(WorkflowConstants.WorkflowType.APPROVAL_FLOW, new UserTaskCreateApprovalHandler());
        handlers.put(WorkflowConstants.WorkflowType.STAGE, new UserTaskCreateStageHandler());
    }

    public static Task generateTask(UserTaskImpl userTaskImpl, ActivityInstanceImpl activityInstance) {
        Task task = setTaskBasicProperties(userTaskImpl, userTaskImpl.getActivity(), activityInstance);
        try {
            return handlers.get(task.getType()).generateTask(userTaskImpl, activityInstance, task);
        } catch (Exception e) {
            throw new RuntimeException("user task error", e);
        } finally {
            Map<String, Object> transientProperties = activityInstance.workflowInstance.transientProperties;
            boolean isBatch=(Objects.nonNull(transientProperties) && !transientProperties.isEmpty())? StringUtils.isNotEmpty((String) transientProperties.get(BusConstant.REQUEST_ID)):false;
            //审批流状态需要重新获取,可能解析人异常
            EngineEventBus.post(CreateTaskEvent.create(
                    MQContext.create(
                            task.getTenantId(),
                            task.getAppId(), ""),
                    Objects.nonNull(task.getType()) ? FlowTag.valueOf(task.getType()) : null,
                    task.getId().toString(),
                    TaskState.valueOfSkipNotFound(task.getState()),
                    task.getExternalApplyTask(),
                    task.getWorkflowInstanceId(),
                    //800 如果是阶段推进器的阶段的话， 如果不传taskType， 阶段推进器无法识别是阶段还是任务， 就会将阶段当任务同步到对象侧
                    Objects.isNull(task.getNodeType()) ? task.getTaskType() : task.getNodeType(),
                    task.getEntityId(),
                    task.getObjectId(),
                    isBatch,
                    task.getId().toString(),
                    task.getLinkApp()));

        }
    }
}
