/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.impl.conditions;

import com.effektif.workflow.api.condition.Condition;
import com.effektif.workflow.api.condition.Equals;
import com.effektif.workflow.impl.data.TypedValueImpl;
import com.effektif.workflow.impl.util.CollectionComparator;

import java.util.*;


/**
 * <AUTHOR>
 */
public class EqualsImpl extends ComparatorImpl {

    @Override
    public Class<? extends Condition> getApiType() {
        return Equals.class;
    }

    @Override
    public boolean compare(TypedValueImpl leftValue, TypedValueImpl rightValue) {
        if (isNull(leftValue) && isNull(rightValue))
            return true;
        if (isNotNull(leftValue) && isNull(rightValue)) {
            return false;
        }
        if (isNull(leftValue) && isNotNull(rightValue)) {
            return false;
        }
        if ("".equals(leftValue) && isNull(rightValue)) {
            return true;
        }
        if (isNull(leftValue) && "".equals(rightValue)) {
            return true;
        }

        if ((leftValue.value instanceof Number) && (rightValue.value instanceof Number)) {
            Number leftNumber = (Number) leftValue.value;
            Number rightNumber = (Number) rightValue.value;
            return leftNumber.doubleValue() == rightNumber.doubleValue();
        }
        if((leftValue.value instanceof Collection)&& (rightValue.value instanceof Collection)){
            return CollectionComparator.isEqualCollection((Collection) leftValue.value,(Collection)rightValue.value);
        }


        return leftValue.value.equals(rightValue.value);
    }

    @Override
    public String getComparatorSymbol() {
        return "==";
    }

}
