#!/bin/bash

# 临时 JAVA_HOME 变量，可以根据需要进行设置
JAVA_HOME_TEMP=/Library/Java/JavaVirtualMachines/jdk-11.0.8.jdk/Contents/Home
projectKey=com.effektif:effektif-parent
sonarLogin=squ_02db0ac7ddbf2257d36692b3ae54d2571a4bcacc


# 如果 JAVA_HOME_TEMP 未设置或为空，则使用系统环境变量中的 JAVA_HOME
if [ -z "$JAVA_HOME_TEMP" ]; then
    JAVA_HOME=$JAVA_HOME
else
    JAVA_HOME=$JAVA_HOME_TEMP
fi

JAVA_HOME=$JAVA_HOME mvn -v

JAVA_HOME=$JAVA_HOME mvn clean verify sonar:sonar -Dsonar.projectKey=$projectKey -Dsonar.host.url=https://oss.firstshare.cn/sonarqube -Dsonar.login=$sonarLogin
