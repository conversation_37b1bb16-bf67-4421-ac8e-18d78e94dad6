package com.fxiaoke.erpdss.ipaas.web.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;

/**
 * Web层配置
 * 
 * <AUTHOR> (^_−)☆
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {
}
