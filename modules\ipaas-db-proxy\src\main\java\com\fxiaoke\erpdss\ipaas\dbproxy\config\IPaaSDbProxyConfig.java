package com.fxiaoke.erpdss.ipaas.dbproxy.config;

import com.fxiaoke.erpdss.ipaas.IPaaS;
import org.springframework.boot.actuate.autoconfigure.metrics.mongo.MongoMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> (^_−)☆
 */
@Configuration
@ComponentScan(basePackageClasses = IPaaS.class)
@EnableAutoConfiguration(exclude = {
        MongoAutoConfiguration.class,
        MongoMetricsAutoConfiguration.class
})
public class IPaaSDbProxyConfig {
}
