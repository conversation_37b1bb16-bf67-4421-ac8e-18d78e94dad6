package com.effektif.workflow.test.impl;

import com.effektif.workflow.api.activities.*;
import com.effektif.workflow.api.condition.Equals;
import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.types.BooleanType;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflow.Transition;
import com.effektif.workflow.impl.ext.Task;
import com.effektif.workflow.test.WorkflowTest;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @creat_date: 2019-08-13
 * @creat_time: 23:05
 * @since 6.6
 */
public class GoBackGradeTest extends WorkflowTest {

  @Test
  public void testGoBackGradeApproval() {
    String tenantId="";
    ExecutableWorkflow workflow = new ExecutableWorkflow().tenantId(tenantId).variable("taskType", new BooleanType())
      .activity("start", new StartEvent().transitionTo("user0"))
      /**
       * 第一个userTask节点
       */
      .activity("user0", new UserTask().assignee(getBeyondAssigneePerson(Lists.newArrayList("1047"))).taskType("single").transitionTo("grade1"))

      /**
       * 第一个逐级节点
       */
      .activity("grade1",
        new UserTask().assignee(getGradeAssignee(2, 1))
          .taskType("single")
          .demandBeyondAssignee(2)
          .beyondAssignee(getBeyondAssigneePerson(Lists.newArrayList("1047")))
          .transitionTo("gateway1"))
      .activity("gateway1",
        new ExclusiveGateway().transitionTo(new Transition().id("go_back1")
          .toId("user0")
          .condition(new Equals().leftExpression("taskType").rightValue("go_back")))
          .transitionTo(new Transition().id("agree1").condition(new Equals().leftExpression("taskType").rightValue("agree")).toId("grade2")));
    /**
     * 生成 从2->9的8个中间逐级节点
     */
    workflow.type("approvalflow");
    for (int i = 2; i < 10; i++) {
      workflow.activity("grade" + i,
        new UserTask().assignee(getGradeAssignee(2, i))
          .taskType("single")
          .demandBeyondAssignee(2)
          .beyondAssignee(getBeyondAssigneePerson(Lists.newArrayList("1047")))
          .transitionTo("gateway" + i))
        .activity("gateway" + i,
          new ExclusiveGateway().transitionTo(new Transition().id("go_back" + i)
            .toId("grade" + (i - 1))
            .condition(new Equals().leftExpression("taskType").rightValue("go_back")))
            .transitionTo(new Transition().id("agree" + i).condition(new Equals().leftExpression("taskType").rightValue("agree")).toId("grade" + (i + 1))));
    }
    /**
     * 第10个逐级节点
     */
    workflow.activity("grade10",
      new UserTask().assignee(getGradeAssignee(2, 10))
        .taskType("single")
        .demandBeyondAssignee(2)
        .beyondAssignee(getBeyondAssigneePerson(Lists.newArrayList("1047")))
        .transitionTo("gateway10"))
      .activity("gateway10",
        new ExclusiveGateway().transitionTo(new Transition().id("go_back10")
          .toId("grade9")
          .condition(new Equals().leftExpression("taskType").rightValue("go_back")))
          .transitionTo(new Transition().id("agree10").condition(new Equals().leftExpression("taskType").rightValue("agree")).toId("lastUser")))
      /**
       * 最后一个userTask节点
       */
      .activity("lastUser", new UserTask().assignee(getBeyondAssigneePerson(Lists.newArrayList("1047"))).taskType("single").transitionTo("lastGateway"))
      /**
       * 最后一个网关
       */
      .activity("lastGateway",
        new ExclusiveGateway().transitionTo(new Transition().id("go_back11")
          .toId("grade10")
          .condition(new Equals().leftExpression("taskType").rightValue("go_back")))
          .transitionTo(new Transition().id("agree").condition(new Equals().leftExpression("taskType").rightValue("agree")).toId("end")))
      .activity("end", new EndEvent());


    deploy(workflow);

    TriggerInstance triggerInstance = createTriggerInstance(workflow).data("taskType", "agree");
    start(triggerInstance);
    List<Task> tasks = taskFactory.findTasks(tenantId, null);
    taskFactory.completeTask(tenantId, tasks.get(0).getId().getInternal());
    tasks = taskFactory.findTasks(tenantId, null);
    taskFactory.completeTask(tenantId, tasks.get(0).getId().toString());
  }

  private Map<String, List<String>> getBeyondAssigneePerson(List<String> persons) {
    Map<String, List<String>> assignee = Maps.newHashMap();
    assignee.put("person", persons);
    return assignee;
  }

  private Map<String, List<String>> getGradeAssignee(int to, int total) {
    Map<String, List<String>> assignee = Maps.newHashMap();
    List<String> grade = Lists.newArrayList("" + to, "" + total);
    assignee.put("grade", grade);
    return assignee;
  }
}
