package com.fxiaoke.erpdss.ipaas.test.service;

import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSBizException;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSSystemException;
import com.fxiaoke.erpdss.ipaas.common.module.Result;
import com.fxiaoke.erpdss.ipaas.springcommon.annotation.ServiceLog;
import org.springframework.stereotype.Service;

/**
 * 测试业务Service类
 * <p>
 * 位于 com.fxiaoke.erpdss.ipaas 包下，类名以Service结尾，应该被切面拦截
 * 
 * <AUTHOR> (^_−)☆
 */
@Service
public class TestBusinessService {

    /**
     * 正常执行的方法
     */
    public String normalMethod(String input) {
        return "业务处理结果: " + input;
    }

    /**
     * 抛出业务异常的方法
     */
    public String bizExceptionMethod(String input) {
        throw new IPaaSBizException("业务处理失败: " + input);
    }

    /**
     * 抛出系统异常的方法
     */
    public String systemExceptionMethod(String input) {
        throw new IPaaSSystemException("系统处理失败: " + input);
    }

    /**
     * 抛出其他异常的方法
     */
    public String otherExceptionMethod(String input) {
        throw new RuntimeException("运行时异常: " + input);
    }

    /**
     * 返回Result类型的正常方法
     */
    @ServiceLog("返回Result的业务方法")
    public Result<String> resultNormalMethod(String input) {
        return Result.success("业务处理结果: " + input);
    }

    /**
     * 返回Result类型但抛出业务异常的方法
     */
    public Result<String> resultBizExceptionMethod(String input) {
        throw new IPaaSBizException("业务处理失败: " + input);
    }

    /**
     * 返回Result类型但抛出系统异常的方法
     */
    public Result<String> resultSystemExceptionMethod(String input) {
        throw new IPaaSSystemException("系统处理失败: " + input);
    }

    /**
     * 返回Result类型但抛出其他异常的方法
     */
    public Result<String> resultOtherExceptionMethod(String input) {
        throw new RuntimeException("运行时异常: " + input);
    }
}
