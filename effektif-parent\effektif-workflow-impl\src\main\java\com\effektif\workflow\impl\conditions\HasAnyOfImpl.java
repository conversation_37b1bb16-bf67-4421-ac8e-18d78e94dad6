package com.effektif.workflow.impl.conditions;

import com.effektif.workflow.api.condition.Condition;
import com.effektif.workflow.api.condition.HasAnyOf;
import com.effektif.workflow.impl.data.TypedValueImpl;

import java.util.Collections;
import java.util.List;

/**
 * zhenghaibo
 * 2017/4/25 11:35
 */
public class HasAnyOfImpl extends ComparatorImpl {
    @Override
    public Class<? extends Condition> getApiType() {
        return HasAnyOf.class;
    }

    @Override
    public boolean compare(TypedValueImpl leftValue, TypedValueImpl rightValue) {
        if (isNull(leftValue) && isNull(rightValue))
            return true;
        if (isNotNull(leftValue) && isNull(rightValue))
            return false;
        if (isNull(leftValue) && isNotNull(rightValue))
            return false;
        Object left =  leftValue.value;
        List right = (List) rightValue.value;
        if (left instanceof String){
            return right.contains(left.toString());
        }
        if (left instanceof List){
            return !Collections.disjoint((List)left, right);
        }
        return false;
    }

    @Override
    public String getComparatorSymbol() {
        return "<hasAnyOf>";
    }
}
