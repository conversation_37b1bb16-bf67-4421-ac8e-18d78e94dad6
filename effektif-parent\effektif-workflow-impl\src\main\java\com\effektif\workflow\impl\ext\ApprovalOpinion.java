package com.effektif.workflow.impl.ext;


import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 * User: gaozl
 * Date: 16-8-2
 * Time: 下午7:55
 * To change this template use File | Settings | File Templates.
 */
public class ApprovalOpinion implements Serializable {
  private String id;
  private String tenantId;
  private String userId;
  private String actionType;
  private String opinion;
  //add by liuyl
  private long replyTime;
  // 驳回到节点名称
  private String toTaskName;

  /**
   * 加签 串行（true） 并行(false) 标示
   */
  private Boolean sequence;
  /**
   * 是否是历史意见
   */
  private Boolean history;
  /**
   * 是否需要创建后加签任务（后动作失败后用于创建后加签任务的标识） false-已经创建，true需要创建
   */
  private Boolean needAfterTag;
  /**
   * 后加签的人
   */
  private List<String> extraNodeAssignee;

  private String autoAgreeType;

  private String feedId;

  private String replyId;

  /**
   * 是否是批量完成
   */
  private Boolean batch;

  /**
   * 890新增 加签节点完成后,需要更新 被加签节点 审批意见中的加签属性的endTime
   */
  private String taskId;
  private Long endTime;

  /**
   * A 驳回到B 节点
   *
   * A上面需要记录下,用户驳回到任意节点后跳转到当前节点
   */
  private Boolean moveToCurrentActivityWhenReject;

  /**
   * 是从哪个任务驳回到当前节点 910新增  只有并行内的节点 驳回到并行外的时候记录一下
   */
  private String parallelRejectedToCurrentNodeTaskId;


  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getFeedId() {
    return feedId;
  }

  public void setFeedId(String feedId) {
    this.feedId = feedId;
  }

  public String getReplyId() {
    return replyId;
  }

  public void setReplyId(String replyId) {
    this.replyId = replyId;
  }

  public long getReplyTime() {
    return replyTime;
  }

  public void setReplyTime(long replyTime) {
    this.replyTime = replyTime;
  }

  public String getTenantId() {
    return tenantId;
  }

  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public String getActionType() {
    return actionType;
  }

  public void setActionType(String actionType) {
    this.actionType = actionType;
  }

  public String getOpinion() {
    return opinion;
  }

  public void setOpinion(String opinion) {
    this.opinion = opinion;
  }

  public String getToTaskName() {
    return toTaskName;
  }

  public void setToTaskName(String toTaskName) {
    this.toTaskName = toTaskName;
  }

  public Boolean getSequence() {
    return sequence;
  }

  public void setSequence(Boolean sequence) {
    this.sequence = sequence;
  }

  public Boolean getHistory() {
    return history;
  }

  public void setHistory(Boolean history) {
    this.history = history;
  }

  public Boolean getNeedAfterTag(){
    return this.needAfterTag;
  }

  public List<String> getExtraNodeAssignee(){
    return this.extraNodeAssignee;
  }

  public String getAutoAgreeType() {
    return autoAgreeType;
  }

  public void setAutoAgreeType(String autoAgreeType) {
    this.autoAgreeType = autoAgreeType;
  }

  public void setNeedAfterTag(Boolean needAfterTag) {
    this.needAfterTag = needAfterTag;
  }

  public void setExtraNodeAssignee(List<String> extraNodeAssignee) {
    this.extraNodeAssignee = extraNodeAssignee;
  }

  public Boolean getBatch() {
    return batch;
  }

  public void setBatch(Boolean batch) {
    this.batch = batch;
  }

  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public Long getEndTime() {
    return endTime;
  }

  public void setEndTime(Long endTime) {
    this.endTime = endTime;
  }

  public Boolean getMoveToCurrentActivityWhenReject() {
    return moveToCurrentActivityWhenReject;
  }

  public void setMoveToCurrentActivityWhenReject(Boolean moveToCurrentActivityWhenReject) {
    this.moveToCurrentActivityWhenReject = moveToCurrentActivityWhenReject;
  }

  public String getParallelRejectedToCurrentNodeTaskId() {
    return parallelRejectedToCurrentNodeTaskId;
  }

  public void setParallelRejectedToCurrentNodeTaskId(String parallelRejectedToCurrentNodeTaskId) {
    this.parallelRejectedToCurrentNodeTaskId = parallelRejectedToCurrentNodeTaskId;
  }

  public ApprovalOpinion (){

  }

  /**
   * 做转换
   */
  public static ApprovalOpinion makeActionApprovalOpinion(String tenantId,String userId, String actionType, String opinion, long replyTime,Boolean history,String autoAgreeType,String id,String feedId,String replyId,Boolean batch) {
    return createOpinion(id,tenantId,userId,actionType,opinion,replyTime,null,null,history,null,null,autoAgreeType,feedId,replyId,batch);
  }

  /**
   * 驳回到任意节点
   */
  public static ApprovalOpinion rejectToAnyTaskOpinion(String id,String tenantId,String userId, String actionType, String opinion,String toTaskName,String feedId,String replyId) {
    //驳回到某一个指定节点，来在业务层标示是否添加结束节点的记录
    return createOpinion(id,tenantId,userId,actionType,opinion,System.currentTimeMillis(),toTaskName,null,null,null,null,null,feedId,replyId,false);
  }


  public static ApprovalOpinion createApprovalOpinion(String id,String tenantId,String userId, String actionType, String opinion, long replyTime,String feedId,String replyId,String autoAgreeType,Boolean batch) {
    return createOpinion(id,tenantId,userId,actionType,opinion,replyTime,null,null,null,null,null,autoAgreeType,feedId,replyId,batch);
  }


  /**
   * 后加签
   */
  public static ApprovalOpinion createApprovalAfterTagOpinion(String id,String tenantId, String userId, String actionType, String opinion, long replyTime, String toTaskName, Boolean sequence, Boolean history, Boolean needAfterTag, List<String> extraNodeAssignee) {
    return createOpinion(id,tenantId,userId,actionType,opinion,replyTime,toTaskName,sequence,history,needAfterTag,extraNodeAssignee,null,null,null,false);
  }


  public static ApprovalOpinion createBpmApprovalOpinion(String tenantId, String userId, String actionType, String opinion, long replyTime) {
    return createOpinion(null,tenantId,userId,actionType,opinion,replyTime,null,null,null,null,null,null,null,null,false);
  }


  public static ApprovalOpinion createStageApprovalOpinion(String tenantId, String userId) {
    return createOpinion(null,tenantId,userId,"agree","",System.currentTimeMillis(),null,null,null,null,null,null,null,null,false);
  }

  private static ApprovalOpinion createOpinion(String id,
                                               String tenantId,
                                               String userId,
                                               String actionType,
                                               String opinion,
                                               Long replyTime,
                                               String toTaskName,
                                               Boolean sequence,
                                               Boolean history,
                                               Boolean needAfterTag,
                                               List<String> extraNodeAssignee,
                                               String autoAgreeType,
                                               String feedId,
                                               String replyId,
                                               Boolean batch) {
    ApprovalOpinion approvalOpinion = new ApprovalOpinion();
    approvalOpinion.setId(id);
    approvalOpinion.setTenantId(tenantId);
    approvalOpinion.setUserId(userId);
    approvalOpinion.setActionType(actionType);
    approvalOpinion.setOpinion(opinion);
    approvalOpinion.setReplyTime(Objects.isNull(replyTime) ? 0L : replyTime);
    approvalOpinion.setToTaskName(toTaskName);
    approvalOpinion.setSequence(sequence);
    approvalOpinion.setHistory(history);
    approvalOpinion.setNeedAfterTag(needAfterTag);
    approvalOpinion.setExtraNodeAssignee(extraNodeAssignee);
    approvalOpinion.setAutoAgreeType(autoAgreeType);
    approvalOpinion.setFeedId(feedId);
    approvalOpinion.setReplyId(replyId);
    approvalOpinion.setBatch(batch);
    return approvalOpinion;
  }
}
