package com.effektif.workflow.api.activities;

import com.effektif.workflow.api.bpmn.BpmnElement;
import com.effektif.workflow.api.ext.ExecutionPojo;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.json.TypeName;
import com.effektif.workflow.api.workflow.Activity;

import javax.swing.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * zhenghaibo
 * 16/5/13 15:09
 */
@TypeName("userTask")
@BpmnElement("userTask")
public class UserTask extends Activity {
    private static final long serialVersionUID = 8809813461884835903L;
    /**
     * 审批人,key 为 assigneeType
     */
    protected Map<String, List<String>> assignee;

    /**
     * 函数解析人
     */
    protected Map<String,Object> assigneeFunction;
    /**
     * assignee_function
     * assignee
     */
    protected String assigneeType;

    /**
     * 指派类型
     *
     * @see com.effektif.workflow.api.ext.WorkflowConstants.AssigneeType
     */
    protected String assignType;
    /**
     * 审批任务类型
     *
     * @see com.effektif.workflow.api.ext.WorkflowConstants.UserTaskType
     */
    protected String taskType;
    protected Map<String, List<ExecutionPojo>> execution;
    protected Object rule;
    protected Object rejectRule;

    protected Object functionRule;
    protected Object rejectFunctionRule;
    //设置是否为审批例外人
    protected Boolean candidateEditable;

    //任务的预期截止时间
    protected Long dueDate;
    //发送催办提醒的时间
    protected Long reminder;
    //提醒周期间隔
    protected Long reminderRepeat;
    //是否允许任务执行人将任务转给其他人处理
    protected Boolean allowEscalate;
    //是否需要上一级审批
    protected Boolean demandSuperior;

    /**
     * 是否允许加签
     */
    protected Boolean tagAllow;

    //0：上级审批；1：流程终止；2：指定审批人
    protected Integer demandBeyondAssignee;


    /**
     * 1:表示可以指定下一个审批人
     * 0或空：不可以
     */

    private Integer assignNextTask;

    /**
     * 1:是外部节点
     * 0或空：不是外部节点
     */

    private Integer externalApplyTask;

    /**
     * 1:表示该节点的审批人由上一节点指定
     * 0或空：不需要
     */

    private Integer candidateByPreTask;

    /**
     * 人员分组信息
     */
    private List<HandlerConfig> groupHandler;


    /**
     * 节点超时 执行动作
     */
    private List<TimeoutExecution> timeoutExecution;

    /**
     * 与发起人一致时,任务自动通过,审批流专用
     */
    private Boolean autoAgreeWhenEqualsWithApplicant;

    /**
     * 历史处理人与当前处理人一致 自动通过
     */
    private Boolean autoAgreeWhenHistorySame;

    protected Object createRule;

    protected Object createFunctionRule;

    public List<HandlerConfig> getGroupHandler() {
        return groupHandler;
    }

    public void setGroupHandler(List<HandlerConfig> groupHandler) {
        this.groupHandler = groupHandler;
    }

    public Integer getAllPassType() {
        return allPassType;
    }

    public void setAllPassType(Integer allPassType) {
        this.allPassType = allPassType;
    }

    /**
     *  会签时：
     *  allPassType = 1 表示所有人会签人员都要进行一次操作，即使第一个人执行了reject，后面的人也要执行
     *  allPassType = 0 如果第一个人执行了reject，则流程就终止，会签的其他人不需要执行。
     */

    private Integer allPassType;


    /**
     *  针对demandBeyondAssignee==2的情况     */

    protected Map<String, List<String>> beyondAssignee;

    public Map<String, List<String>> getBeyondAssignee() {
        return beyondAssignee;
    }

    public void setBeyondAssignee(Map<String, List<String>> beyondAssignee) {
        this.beyondAssignee = beyondAssignee;
    }



    public Integer getDemandBeyondAssignee() {
        return demandBeyondAssignee;
    }

    public void setDemandBeyondAssignee(Integer demandBeyondAssignee) {
        this.demandBeyondAssignee = demandBeyondAssignee;
    }

    public Long getDueDate() {
        return dueDate;
    }

    public void setDueDate(Long dueDate) {
        this.dueDate = dueDate;
    }

    public UserTask dueDate(Long dueDate) {
        this.dueDate = dueDate;
        return this;
    }

    public Long getReminder() {
        return reminder;
    }

    public void setReminder(Long reminder) {
        this.reminder = reminder;
    }

    public Long getReminderRepeat() {
        return reminderRepeat;
    }

    public void setReminderRepeat(Long reminderRepeat) {
        this.reminderRepeat = reminderRepeat;
    }

    public Boolean getAllowEscalate() {
        return allowEscalate;
    }

    public void setAllowEscalate(Boolean allowEscalate) {
        this.allowEscalate = allowEscalate;
    }

    public UserTask allowEscalate(Boolean allowEscalate) {
        this.allowEscalate = allowEscalate;
        return this;
    }

    public String getAssignType() {
        return assignType;
    }

    public void setAssignType(String assignType) {
        this.assignType = assignType;
    }

    public UserTask assignType(String assignType) {
        this.assignType = assignType;
        return this;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public UserTask taskType(String taskType) {
        this.taskType = taskType;
        return this;
    }

    public UserTask execution(Map<String, List<ExecutionPojo>> execution) {
        this.execution = execution;
        return this;
    }

    public UserTask demandBeyondAssignee(int demandBeyondAssignee){
        this.demandBeyondAssignee=demandBeyondAssignee;
        return this;
    }

    public UserTask beyondAssignee(Map<String,List<String>> beyondAssignee){
        this.beyondAssignee=beyondAssignee;
        return this;
    }

    public Map<String, List<String>> getAssignee() {
        return assignee;
    }

    public void setAssignee(Map<String, List<String>> assignee) {
        this.assignee = assignee;
    }

    public UserTask assignee(Map<String, List<String>> assignee) {
        this.assignee = assignee;
        return this;
    }


    public Map<String, Object> getAssigneeFunction() {
        return assigneeFunction;
    }

    public void setAssigneeFunction(Map<String, Object> assigneeFunction) {
        this.assigneeFunction = assigneeFunction;
    }

    public Map<String, List<ExecutionPojo>> getExecution() {
        return execution;
    }

    public void setExecution(Map<String, List<ExecutionPojo>> execution) {
        this.execution = execution;
    }

    public Object getRule() {
        return rule;
    }

    public void setRule(Object rule) {
        this.rule = rule;
    }

    public Object getRejectRule() {
        return rejectRule;
    }

    public void setRejectRule(Object rejectRule) {
        this.rejectRule = rejectRule;
    }

    public Boolean getCandidateEditable() {
        return candidateEditable;
    }

    public void setCandidateEditable(Boolean candidateEditable) {
        this.candidateEditable = candidateEditable;
    }

    public Boolean getDemandSuperior() {
        return demandSuperior;
    }

    public void setDemandSuperior(Boolean demandSuperior) {
        this.demandSuperior = demandSuperior;
    }

    public Boolean getTagAllow(){
        return tagAllow;
    }

    public void setTagAllow(Boolean tagAllow){
        this.tagAllow = tagAllow;
    }

    public Integer getAssignNextTask() {
        return assignNextTask;
    }

    public UserTask assignNextTask(Integer assignNextTask){
        this.assignNextTask = assignNextTask;
        return this;
    }

    public void setAssignNextTask(Integer assignNextTask) {
        this.assignNextTask = assignNextTask;
    }

    public Integer getExternalApplyTask() {
        return externalApplyTask;
    }

    public void setExternalApplyTask(Integer externalApplyTask) {
        this.externalApplyTask = externalApplyTask;
    }

    public Integer getCandidateByPreTask() {
        return candidateByPreTask;
    }

    public void setCandidateByPreTask(Integer candidateByPreTask) {
        this.candidateByPreTask = candidateByPreTask;
    }

    protected List<Activity> taskItemList;

    public List<Activity> getTaskItemList() {
        if(Objects.isNull(taskItemList)){
            taskItemList=new ArrayList<>();
        }
        return taskItemList;
    }

    public List<Activity> getNextTaskItemList(String upActivityId) {
        List<Activity> result = new ArrayList<>();
        if(Objects.isNull(taskItemList) || Objects.isNull(upActivityId)){
            return result;
        }
        boolean flag = false;
        for (Activity activity : taskItemList) {
            if(flag){
                result.add(activity);
            }else {
                flag = upActivityId.equals(activity.getId()) ? true : false;
            }
        }
        return result;
    }

    public List<Activity> getTaskItemListByActivityId(String activityId, boolean single) {
        List<Activity> result = new ArrayList<>();
        if(Objects.isNull(taskItemList) || Objects.isNull(activityId)){
            return result;
        }
        boolean flag = false;
        for (Activity activity : taskItemList) {
            if(activityId.equals(activity.getId()) || flag){
                result.add(activity);
                if(single){
                    break;
                }
                flag = true;
            }
        }
        return result;
    }

    public List<Activity> getTaskItemByActivityIdList(List<String> activityIdList) {
        List<Activity> result = new ArrayList<>();
        if (Objects.isNull(taskItemList) || Objects.isNull(activityIdList) || activityIdList.isEmpty()) {
            return result;
        }
        return taskItemList.stream().filter(activity -> activityIdList.contains(activity.getId())).collect(Collectors.toList());
    }

    public void setTaskItemList(List<Activity> taskItemList) {
        this.taskItemList = taskItemList;
    }

    protected String stageId;
    protected  List<String> mustExecuteTasks;
    protected Boolean isTerminal;
    protected List<TerminalConfig> terminalConfigs;

    public String getStageId() {
        return stageId;
    }

    public void setStageId(String stageId) {
        this.stageId = stageId;
    }

    public List<String> getMustExecuteTasks() {
        return mustExecuteTasks;
    }

    public void setMustExecuteTasks(List<String> mustExecuteTasks) {
        this.mustExecuteTasks = mustExecuteTasks;
    }

    public Boolean getTerminal() {
        return isTerminal;
    }

    /**
     * 阶段是否处于终结态
     * @return
     */
    public boolean isTerminal() {
        return this.getTerminal() != null && this.getTerminal();
    }

    public void setTerminal(Boolean terminal) {
        isTerminal = terminal;
    }

    public List<TerminalConfig> getTerminalConfigs() {
        return terminalConfigs;
    }

    public void setTerminalConfigs(List<TerminalConfig> terminalConfigs) {
        this.terminalConfigs = terminalConfigs;
    }

    public static class TerminalConfig implements Serializable {
        protected  List<String> mustCompleteStages;
        protected Boolean validateFromStage;
        private Object beforeSkipInRule;
        private Object beforeSkipInFunctionRule;
        protected Integer orderId;
        protected String  name;
        protected String stageId;
        public List<Map> taskItemList;
        private Boolean skipValidate;
        private Boolean stageReactivation;
        private Object reactiveRule;
        private Object reactiveFunctionRule;
        private Object reactiveTask;
        private String moveInTip;

        public Boolean getSkipValidate() {
            return skipValidate;
        }

        public void setSkipValidate(Boolean skipValidate) {
            this.skipValidate = skipValidate;
        }

        public List<Map> getTaskItemList() {
            return taskItemList;
        }

        public void setTaskItemList(List<Map> taskItemList) {
            this.taskItemList = taskItemList;
        }

        public String getMoveInTip() {
            return moveInTip;
        }

        public void setMoveInTip(String moveInTip) {
            this.moveInTip = moveInTip;
        }

        public Object getBeforeSkipInRule() {
            return beforeSkipInRule;
        }

        public Object getBeforeSkipInFunctionRule() {
            return beforeSkipInFunctionRule;
        }

        public void setBeforeSkipInFunctionRule(Object beforeSkipInFunctionRule) {
            this.beforeSkipInFunctionRule = beforeSkipInFunctionRule;
        }

        public void setBeforeSkipInRule(Object beforeSkipInRule) {
            this.beforeSkipInRule = beforeSkipInRule;
        }

        public List<String> getMustCompleteStages() {
            return mustCompleteStages;
        }

        public void setMustCompleteStages(List<String> mustCompleteStages) {
            this.mustCompleteStages = mustCompleteStages;
        }

        public Boolean getValidateFromStage() {
            return validateFromStage;
        }

        public void setValidateFromStage(Boolean validateFromStage) {
            this.validateFromStage = validateFromStage;
        }

        public Integer getOrderId() {
            return orderId;
        }

        public void setOrderId(Integer orderId) {
            this.orderId = orderId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getStageId() {
            return stageId;
        }

        public void setStageId(String stageId) {
            this.stageId = stageId;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String id;
        public String description;
        public String beforeSkipInApprovalId;
        public Map<String,Object> extraData;
        public Map<String,Object> extension;

        public String getBeforeSkipInApprovalId() {
            return beforeSkipInApprovalId;
        }

        public void setBeforeSkipInApprovalId(String beforeSkipInApprovalId) {
            this.beforeSkipInApprovalId = beforeSkipInApprovalId;
        }

        public Boolean needSelectApproval;

        public Boolean getNeedSelectApproval() {
            return needSelectApproval;
        }

        public void setNeedSelectApproval(Boolean needSelectApproval) {
            this.needSelectApproval = needSelectApproval;
        }

        public Map<String, Object> getExtraData() {
            return extraData;
        }

        public void setExtraData(Map<String, Object> extraData) {
            this.extraData = extraData;
        }

        public Map<String, Object> getExtension() {
            return extension;
        }

        public void setExtension(Map<String, Object> extension) {
            this.extension = extension;
        }

        public Boolean getStageReactivation() {
            return stageReactivation;
        }

        public void setStageReactivation(Boolean stageReactivation) {
            this.stageReactivation = stageReactivation;
        }

        public Object getReactiveRule() {
            return reactiveRule;
        }

        public void setReactiveRule(Object reactiveRule) {
            this.reactiveRule = reactiveRule;
        }

        public Object getReactiveFunctionRule() {
            return reactiveFunctionRule;
        }

        public void setReactiveFunctionRule(Object reactiveFunctionRule) {
            this.reactiveFunctionRule = reactiveFunctionRule;
        }

        public Object getReactiveTask() {
            return reactiveTask;
        }

        public void setReactiveTask(Object reactiveTask) {
            this.reactiveTask = reactiveTask;
        }
    }
    protected Integer orderId;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    protected String stageFieldApiName;

    public String getStageFieldApiName() {
        return stageFieldApiName;
    }

    public void setStageFieldApiName(String stageFieldApiName) {
        this.stageFieldApiName = stageFieldApiName;
    }

    protected String nodeType;

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    protected String beforeSkipOutApprovalId;

    public String getBeforeSkipOutApprovalId() {
        return beforeSkipOutApprovalId;
    }

    public void setBeforeSkipOutApprovalId(String beforeSkipOutApprovalId) {
        this.beforeSkipOutApprovalId = beforeSkipOutApprovalId;
    }

    public Boolean needSelectApproval;

    public Boolean getNeedSelectApproval() {
        return needSelectApproval;
    }

    public void setNeedSelectApproval(Boolean needSelectApproval) {
        this.needSelectApproval = needSelectApproval;
    }

    public Integer isAutoAgree;

    public Integer getAutoAgree() {
        return isAutoAgree;
    }

    public void setAutoAgree(Integer autoAgree) {
        isAutoAgree = autoAgree;
    }

    public Object getFunctionRule() {
        return functionRule;
    }

    public void setFunctionRule(Object functionRule) {
        this.functionRule = functionRule;
    }

    public Object getRejectFunctionRule() {
        return rejectFunctionRule;
    }

    public void setRejectFunctionRule(Object rejectFunctionRule) {
        this.rejectFunctionRule = rejectFunctionRule;
    }

    public List<TimeoutExecution> getTimeoutExecution() {
        return timeoutExecution;
    }

    public void setTimeoutExecution(List<TimeoutExecution> timeoutExecution) {
        this.timeoutExecution = timeoutExecution;
    }

    public Boolean getAutoAgreeWhenEqualsWithApplicant() {
        return autoAgreeWhenEqualsWithApplicant;
    }

    public void setAutoAgreeWhenEqualsWithApplicant(Boolean autoAgreeWhenEqualsWithApplicant) {
        this.autoAgreeWhenEqualsWithApplicant = autoAgreeWhenEqualsWithApplicant;
    }

    public Boolean getAutoAgreeWhenHistorySame() {
        return autoAgreeWhenHistorySame;
    }

    public void setAutoAgreeWhenHistorySame(Boolean autoAgreeWhenHistorySame) {
        this.autoAgreeWhenHistorySame = autoAgreeWhenHistorySame;
    }

    public String getAssigneeType() {
        return assigneeType;
    }

    public void setAssigneeType(String assigneeType) {
        this.assigneeType = assigneeType;
    }

    public boolean isAssigneeFunction(){
        return WorkflowConstants.AssigneeType.ASSIGNEE_FUNCTION.equals(assigneeType);
    }

    public Object getCreateRule() {
        return createRule;
    }

    public void setCreateRule(Object createRule) {
        this.createRule = createRule;
    }

    public Object getCreateFunctionRule() {
        return createFunctionRule;
    }

    public void setCreateFunctionRule(Object createFunctionRule) {
        this.createFunctionRule = createFunctionRule;
    }

    /**
     * 880 业务扩展人工元素
     */
    protected Boolean custom=false;
    protected Boolean customCandidateConfig=false;
    protected String elementApiName;
    protected Map<String,Object> customExtension;
    protected Boolean importObject=false;

    /**
     * 940审批流会签策略
     */
    protected Map<String,Object> countersignStrategy;

    public boolean isCustom() {
        return custom;
    }

    public UserTask custom(boolean custom) {
        this.custom = custom;
        return this;
    }

    public boolean isCustomCandidateConfig() {
        return customCandidateConfig;
    }

    public boolean isImportObject() {
        return importObject;
    }

    public UserTask customCandidateConfig(boolean customCandidateIds) {
        this.customCandidateConfig = customCandidateIds;
        return this;
    }

    public String getElementApiName() {
        return elementApiName;
    }

    public UserTask elementApiName(String elementApiName) {
        this.elementApiName = elementApiName;
        return this;
    }

    public Map<String, Object> getCustomExtension() {
        return customExtension;
    }

    public UserTask customExtension(Map<String, Object> customExtension) {
        this.customExtension = customExtension;
        return this;
    }

    public Map<String, Object> getCountersignStrategy() {
        return countersignStrategy;
    }

    public void setCountersignStrategy(Map<String, Object> countersignStrategy) {
        this.countersignStrategy = countersignStrategy;
    }
}
