package com.fxiaoke.erpdss.ipaas.common.module;

import com.fxiaoke.erpdss.ipaas.common.i18n.I18nBase;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * ResultCode 单元测试
 * 
 * <AUTHOR> (^_−)☆
 */
class ResultCodeTest {

    @Test
    void testSuccessCode() {
        // When & Then
        assertThat(ResultCode.SUCCESS.getCode()).isEqualTo("s106240000");
        assertThat(ResultCode.SUCCESS._GetDefaultMsg()).isEqualTo("成功");
    }

    @Test
    void testParamIllegalCode() {
        // When & Then
        assertThat(ResultCode.PARAM_ILLEGAL.getCode()).isEqualTo("s206240000");
        assertThat(ResultCode.PARAM_ILLEGAL._GetDefaultMsg()).isEqualTo("参数不合法");
    }

    @Test
    void testSystemErrorCode() {
        // When & Then
        assertThat(ResultCode.SYSTEM_ERROR.getCode()).isEqualTo("s306240000");
        assertThat(ResultCode.SYSTEM_ERROR._GetDefaultMsg()).isEqualTo("系统异常");
    }

    @Test
    void testBizErrorCode() {
        // When & Then
        assertThat(ResultCode.BIZ_ERROR.getCode()).isEqualTo("s306240001");
        assertThat(ResultCode.BIZ_ERROR._GetDefaultMsg()).isEqualTo("业务异常");
    }

    @Test
    void testIsSuccessWithSuccessCode() {
        // Given
        String successCode = "s106240000";

        // When
        boolean result = ResultCode.isSuccess(successCode);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void testIsSuccessWithOtherSuccessCode() {
        // Given
        String successCode = "s199999999";

        // When
        boolean result = ResultCode.isSuccess(successCode);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void testIsSuccessWithWarningCode() {
        // Given
        String warningCode = "s206240000";

        // When
        boolean result = ResultCode.isSuccess(warningCode);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testIsSuccessWithErrorCode() {
        // Given
        String errorCode = "s306240000";

        // When
        boolean result = ResultCode.isSuccess(errorCode);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testIsSuccessWithNullCode() {
        // Given
        String nullCode = null;

        // When
        boolean result = ResultCode.isSuccess(nullCode);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testIsSuccessWithEmptyCode() {
        // Given
        String emptyCode = "";

        // When
        boolean result = ResultCode.isSuccess(emptyCode);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testIsSuccessWithInvalidCode() {
        // Given
        String invalidCode = "invalid";

        // When
        boolean result = ResultCode.isSuccess(invalidCode);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testGetI18nKey() {
        // When & Then
        assertThat(ResultCode.SUCCESS._GetI18nKey())
                .isEqualTo("erpdss.ipaas.resultCode.SUCCESS");
        assertThat(ResultCode.PARAM_ILLEGAL._GetI18nKey())
                .isEqualTo("erpdss.ipaas.resultCode.PARAM_ILLEGAL");
        assertThat(ResultCode.SYSTEM_ERROR._GetI18nKey())
                .isEqualTo("erpdss.ipaas.resultCode.SYSTEM_ERROR");
        assertThat(ResultCode.BIZ_ERROR._GetI18nKey())
                .isEqualTo("erpdss.ipaas.resultCode.BIZ_ERROR");
    }

    @Test
    void testImplementsI18nBase() {
        // When & Then
        assertThat(ResultCode.SUCCESS).isInstanceOf(I18nBase.class);
        assertThat(ResultCode.PARAM_ILLEGAL).isInstanceOf(I18nBase.class);
        assertThat(ResultCode.SYSTEM_ERROR).isInstanceOf(I18nBase.class);
        assertThat(ResultCode.BIZ_ERROR).isInstanceOf(I18nBase.class);
    }

    @Test
    void testGetI18nMsg() {
        // When & Then
        // 由于没有设置自定义i18n函数，应该返回默认消息
        assertThat(ResultCode.SUCCESS.getI18nMsg()).isEqualTo("成功");
        assertThat(ResultCode.PARAM_ILLEGAL.getI18nMsg()).isEqualTo("参数不合法");
        assertThat(ResultCode.SYSTEM_ERROR.getI18nMsg()).isEqualTo("系统异常");
        assertThat(ResultCode.BIZ_ERROR.getI18nMsg()).isEqualTo("业务异常");
    }

    @Test
    void testGetI18nMsgWithArgs() {
        // Given
        Object[] args = {"测试参数"};

        // When & Then
        // 由于默认消息没有占位符，参数不会被使用
        assertThat(ResultCode.SUCCESS.getI18nMsg(args)).isEqualTo("成功");
        assertThat(ResultCode.PARAM_ILLEGAL.getI18nMsg(args)).isEqualTo("参数不合法");
    }

    @Test
    void testToString() {
        // When & Then
        String successString = ResultCode.SUCCESS.toString();
        assertThat(successString).contains("SUCCESS");
        assertThat(successString).contains("s106240000");
        assertThat(successString).contains("成功");
    }

    @Test
    void testEnumValues() {
        // When
        ResultCode[] values = ResultCode.values();

        // Then
        assertThat(values).hasSize(4);
        assertThat(values).contains(
                ResultCode.SUCCESS,
                ResultCode.PARAM_ILLEGAL,
                ResultCode.SYSTEM_ERROR,
                ResultCode.BIZ_ERROR
        );
    }

    @Test
    void testEnumValueOf() {
        // When & Then
        assertThat(ResultCode.valueOf("SUCCESS")).isEqualTo(ResultCode.SUCCESS);
        assertThat(ResultCode.valueOf("PARAM_ILLEGAL")).isEqualTo(ResultCode.PARAM_ILLEGAL);
        assertThat(ResultCode.valueOf("SYSTEM_ERROR")).isEqualTo(ResultCode.SYSTEM_ERROR);
        assertThat(ResultCode.valueOf("BIZ_ERROR")).isEqualTo(ResultCode.BIZ_ERROR);
    }

    @Test
    void testCodePattern() {
        // When & Then
        // 验证代码格式：s + 状态(1位) + 业务(2位) + 模块(2位) + 状态码(4位)
        assertThat(ResultCode.SUCCESS.getCode()).matches("s\\d{9}");
        assertThat(ResultCode.PARAM_ILLEGAL.getCode()).matches("s\\d{9}");
        assertThat(ResultCode.SYSTEM_ERROR.getCode()).matches("s\\d{9}");
        assertThat(ResultCode.BIZ_ERROR.getCode()).matches("s\\d{9}");
    }

    @Test
    void testCodeUniqueness() {
        // When
        ResultCode[] values = ResultCode.values();

        // Then
        // 验证所有错误码都是唯一的
        assertThat(values)
                .extracting(ResultCode::getCode)
                .doesNotHaveDuplicates();
    }

    @Test
    void testResultMethodWithoutArgs() {
        // When
        Result<Void> successResult = ResultCode.SUCCESS.result();
        Result<Void> paramIllegalResult = ResultCode.PARAM_ILLEGAL.result();
        Result<Void> systemErrorResult = ResultCode.SYSTEM_ERROR.result();
        Result<Void> bizErrorResult = ResultCode.BIZ_ERROR.result();

        // Then
        assertThat(successResult.getCode()).isEqualTo(ResultCode.SUCCESS.getCode());
        assertThat(successResult.getMessage()).isEqualTo(ResultCode.SUCCESS.getI18nMsg());
        assertThat(successResult.getData()).isNull();

        assertThat(paramIllegalResult.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(paramIllegalResult.getMessage()).isEqualTo(ResultCode.PARAM_ILLEGAL.getI18nMsg());
        assertThat(paramIllegalResult.getData()).isNull();

        assertThat(systemErrorResult.getCode()).isEqualTo(ResultCode.SYSTEM_ERROR.getCode());
        assertThat(systemErrorResult.getMessage()).isEqualTo(ResultCode.SYSTEM_ERROR.getI18nMsg());
        assertThat(systemErrorResult.getData()).isNull();

        assertThat(bizErrorResult.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(bizErrorResult.getMessage()).isEqualTo(ResultCode.BIZ_ERROR.getI18nMsg());
        assertThat(bizErrorResult.getData()).isNull();
    }

    @Test
    void testResultMethodWithArgs() {
        // Given
        Object[] args = {"用户ID", "操作类型"};

        // When
        Result<Void> paramIllegalResult = ResultCode.PARAM_ILLEGAL.result(args);
        Result<Void> systemErrorResult = ResultCode.SYSTEM_ERROR.result(args);

        // Then
        assertThat(paramIllegalResult.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(paramIllegalResult.getMessage()).isEqualTo(ResultCode.PARAM_ILLEGAL.getI18nMsg(args));
        assertThat(paramIllegalResult.getData()).isNull();

        assertThat(systemErrorResult.getCode()).isEqualTo(ResultCode.SYSTEM_ERROR.getCode());
        assertThat(systemErrorResult.getMessage()).isEqualTo(ResultCode.SYSTEM_ERROR.getI18nMsg(args));
        assertThat(systemErrorResult.getData()).isNull();
    }

    @Test
    void testResultMethodWithNullArgs() {
        // When
        Result<Void> result = ResultCode.BIZ_ERROR.result((Object[]) null);

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(result.getMessage()).isEqualTo(ResultCode.BIZ_ERROR.getI18nMsg((Object[]) null));
        assertThat(result.getData()).isNull();
    }

    @Test
    void testResultMethodWithEmptyArgs() {
        // When
        Result<Void> result = ResultCode.SYSTEM_ERROR.result(new Object[]{});

        // Then
        assertThat(result.getCode()).isEqualTo(ResultCode.SYSTEM_ERROR.getCode());
        assertThat(result.getMessage()).isEqualTo(ResultCode.SYSTEM_ERROR.getI18nMsg(new Object[]{}));
        assertThat(result.getData()).isNull();
    }

    @Test
    void testResultMethodReturnType() {
        // When
        Result<String> stringResult = ResultCode.PARAM_ILLEGAL.result();
        Result<Integer> intResult = ResultCode.SYSTEM_ERROR.result();
        Result<Object> objectResult = ResultCode.BIZ_ERROR.result();

        // Then
        // 验证泛型类型可以正确推断
        assertThat(stringResult).isInstanceOf(Result.class);
        assertThat(intResult).isInstanceOf(Result.class);
        assertThat(objectResult).isInstanceOf(Result.class);

        // 验证数据都为null（因为是错误结果）
        assertThat(stringResult.getData()).isNull();
        assertThat(intResult.getData()).isNull();
        assertThat(objectResult.getData()).isNull();
    }

    @Test
    void testIsSuccessWithVariousFormats() {
        // Given & When & Then
        // 测试各种成功代码格式
        assertThat(ResultCode.isSuccess("s1")).isTrue();
        assertThat(ResultCode.isSuccess("s10")).isTrue();
        assertThat(ResultCode.isSuccess("s100")).isTrue();
        assertThat(ResultCode.isSuccess("s1000")).isTrue();
        assertThat(ResultCode.isSuccess("s1abcd")).isTrue();

        // 测试非成功代码
        assertThat(ResultCode.isSuccess("s0")).isFalse();
        assertThat(ResultCode.isSuccess("s2")).isFalse();
        assertThat(ResultCode.isSuccess("s3")).isFalse();
        assertThat(ResultCode.isSuccess("s9")).isFalse();
        assertThat(ResultCode.isSuccess("1")).isFalse();
        assertThat(ResultCode.isSuccess("success")).isFalse();
        assertThat(ResultCode.isSuccess("S1")).isFalse(); // 大写S
    }

    @Test
    void testCodeStructureValidation() {
        // When & Then
        // 验证SUCCESS代码结构：s + 1(成功) + 06(业务) + 24(模块) + 0000(状态码)
        assertThat(ResultCode.SUCCESS.getCode()).isEqualTo("s106240000");
        assertThat(ResultCode.SUCCESS.getCode().charAt(1)).isEqualTo('1'); // 成功状态

        // 验证PARAM_ILLEGAL代码结构：s + 2(警告) + 06(业务) + 24(模块) + 0000(状态码)
        assertThat(ResultCode.PARAM_ILLEGAL.getCode()).isEqualTo("s206240000");
        assertThat(ResultCode.PARAM_ILLEGAL.getCode().charAt(1)).isEqualTo('2'); // 警告状态

        // 验证SYSTEM_ERROR代码结构：s + 3(错误) + 06(业务) + 24(模块) + 0000(状态码)
        assertThat(ResultCode.SYSTEM_ERROR.getCode()).isEqualTo("s306240000");
        assertThat(ResultCode.SYSTEM_ERROR.getCode().charAt(1)).isEqualTo('3'); // 错误状态

        // 验证BIZ_ERROR代码结构：s + 3(错误) + 06(业务) + 24(模块) + 0001(状态码)
        assertThat(ResultCode.BIZ_ERROR.getCode()).isEqualTo("s306240001");
        assertThat(ResultCode.BIZ_ERROR.getCode().charAt(1)).isEqualTo('3'); // 错误状态
    }

    @Test
    void testBusinessAndModuleCode() {
        // When & Then
        // 验证所有枚举都使用相同的业务代码(06)和模块代码(24)
        for (ResultCode code : ResultCode.values()) {
            String codeStr = code.getCode();
            assertThat(codeStr.substring(2, 4)).isEqualTo("06"); // 业务代码
            assertThat(codeStr.substring(4, 6)).isEqualTo("24"); // 模块代码
        }
    }

    @Test
    void testDefaultMessageNotEmpty() {
        // When & Then
        // 验证所有枚举都有非空的默认消息
        for (ResultCode code : ResultCode.values()) {
            assertThat(code._GetDefaultMsg()).isNotNull();
            assertThat(code._GetDefaultMsg()).isNotEmpty();
            assertThat(code._GetDefaultMsg().trim()).isNotEmpty();
        }
    }

    @Test
    void testI18nKeyFormat() {
        // When & Then
        // 验证国际化键的格式
        assertThat(ResultCode.SUCCESS._GetI18nKey()).isEqualTo("erpdss.ipaas.resultCode.SUCCESS");
        assertThat(ResultCode.PARAM_ILLEGAL._GetI18nKey()).isEqualTo("erpdss.ipaas.resultCode.PARAM_ILLEGAL");
        assertThat(ResultCode.SYSTEM_ERROR._GetI18nKey()).isEqualTo("erpdss.ipaas.resultCode.SYSTEM_ERROR");
        assertThat(ResultCode.BIZ_ERROR._GetI18nKey()).isEqualTo("erpdss.ipaas.resultCode.BIZ_ERROR");

        // 验证所有国际化键都以相同前缀开始
        for (ResultCode code : ResultCode.values()) {
            assertThat(code._GetI18nKey()).startsWith("erpdss.ipaas.resultCode.");
            assertThat(code._GetI18nKey()).endsWith(code.name());
        }
    }
}
