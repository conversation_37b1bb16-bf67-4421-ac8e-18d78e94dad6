package com.fxiaoke.erpdss.ipaas.springcommon.aspect;

import com.example.service.ExternalService;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSBizException;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSSystemException;
import com.fxiaoke.erpdss.ipaas.common.module.Result;
import com.fxiaoke.erpdss.ipaas.common.module.ResultCode;
import com.fxiaoke.erpdss.ipaas.springcommon.constans.CommonLabel;
import com.fxiaoke.erpdss.ipaas.test.service.TestBusinessService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * ServiceLogAspect 单元测试
 * 
 * <AUTHOR> (^_−)☆
 */
@SpringBootTest(classes = {
    ServiceLogAspect.class,
    TestService.class,
    TestBusinessService.class,
    ExternalService.class,
    com.fxiaoke.erpdss.ipaas.springcommon.config.AopConfiguration.class
})
@TestPropertySource(properties = {
    "logging.level.com.fxiaoke.erpdss.ipaas.springcommon.aspect=DEBUG"
})
class ServiceLogAspectTest {

    @Autowired
    private TestService testService;

    @Autowired
    private TestBusinessService testBusinessService;

    @Autowired
    private ExternalService externalService;

    @Test
    void testNormalMethod() {
        // When
        Object result = testService.normalMethod("测试输入");

        // Then
        assertThat(result).isEqualTo("处理结果: 测试输入");
    }

    @Test
    void testBizExceptionMethod() {
        // When & Then - 由于方法返回String而不是Result，异常应该被重新抛出
        assertThatThrownBy(() -> testService.bizExceptionMethod("测试输入"))
                .isInstanceOf(IPaaSBizException.class)
                .hasMessageContaining("业务处理失败");
    }

    @Test
    void testSystemExceptionMethod() {
        // When & Then - 由于方法返回String而不是Result，异常应该被重新抛出
        assertThatThrownBy(() -> testService.systemExceptionMethod("测试输入"))
                .isInstanceOf(IPaaSSystemException.class)
                .hasMessageContaining("系统处理失败");
    }

    @Test
    void testOtherExceptionMethod() {
        // When & Then - 由于方法返回String而不是Result，异常应该被包装为IPaaSSystemException并重新抛出
        assertThatThrownBy(() -> testService.otherExceptionMethod("测试输入"))
                .isInstanceOf(IPaaSSystemException.class)
                .hasMessageContaining(CommonLabel.systemInternalError.getI18nMsg());
    }

    @Test
    void testNoParamsLogMethod() {
        // When
        Object result = testService.noParamsLogMethod("敏感数据");

        // Then
        assertThat(result).isEqualTo("处理完成");
    }

    @Test
    void testNoResultLogMethod() {
        // When
        Object result = testService.noResultLogMethod("测试输入");

        // Then
        assertThat(result).isEqualTo("敏感返回数据: 测试输入");
    }

    @Test
    void testNoTimeLogMethod() {
        // When
        Object result = testService.noTimeLogMethod("测试输入");

        // Then
        assertThat(result).isEqualTo("快速处理: 测试输入");
    }

    @Test
    void testNoAnnotationMethod() {
        // When
        Object result = testService.noAnnotationMethod("测试输入");

        // Then
        assertThat(result).isEqualTo("无注解方法: 测试输入");
    }

    // ========== 测试返回Result类型的方法（异常转换功能） ==========

    @Test
    void testResultNormalMethod() {
        // When
        Result<String> result = testService.resultNormalMethod("测试输入");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo("处理结果: 测试输入");
    }

    @Test
    void testResultBizExceptionMethod() {
        // When
        Result<String> result = testService.resultBizExceptionMethod("测试输入");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(result.getMessage()).contains("业务处理失败");
    }

    @Test
    void testResultSystemExceptionMethod() {
        // When
        Result<String> result = testService.resultSystemExceptionMethod("测试输入");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.SYSTEM_ERROR.getCode());
        assertThat(result.getMessage()).contains("系统处理失败");
    }

    @Test
    void testResultOtherExceptionMethod() {
        // When
        Result<String> result = testService.resultOtherExceptionMethod("测试输入");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.SYSTEM_ERROR.getCode());
        assertThat(result.getMessage()).contains(CommonLabel.systemInternalError.getI18nMsg());
    }

    // ========== 测试新的切点规则 ==========

    @Test
    void testBusinessServiceNormalMethod() {
        // When - 测试 com.fxiaoke.erpdss.ipaas 包下的Service类应该被拦截
        String result = testBusinessService.normalMethod("测试输入");

        // Then
        assertThat(result).isEqualTo("业务处理结果: 测试输入");
    }

    @Test
    void testBusinessServiceBizException() {
        // When & Then - 业务Service的异常应该被拦截并重新抛出
        assertThatThrownBy(() -> testBusinessService.bizExceptionMethod("测试输入"))
                .isInstanceOf(IPaaSBizException.class)
                .hasMessageContaining("业务处理失败");
    }

    @Test
    void testBusinessServiceResultMethod() {
        // When - 测试返回Result类型的方法异常转换
        Result<String> result = testBusinessService.resultBizExceptionMethod("测试输入");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(result.getMessage()).contains("业务处理失败");
    }

    @Test
    void testExternalServiceNotIntercepted() {
        // When - 测试不在指定包下的Service类不应该被拦截
        String result = externalService.normalMethod("测试输入");

        // Then - 正常执行，没有日志输出
        assertThat(result).isEqualTo("外部服务处理结果: 测试输入");
    }

    @Test
    void testExternalServiceExceptionNotIntercepted() {
        // When & Then - 外部Service的异常不应该被拦截，直接抛出原始异常
        assertThatThrownBy(() -> externalService.exceptionMethod("测试输入"))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("外部服务异常")
                .hasMessageNotContaining(CommonLabel.systemInternalError.getI18nMsg()); // 不应该被包装
    }
}
