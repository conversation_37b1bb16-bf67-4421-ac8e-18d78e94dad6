package com.effektif.workflow.test.api;

import static org.junit.Assert.assertTrue;

import com.effektif.workflow.api.Configuration;
import com.effektif.workflow.api.WorkflowEngine;
import com.effektif.workflow.api.activities.*;
import com.effektif.workflow.api.condition.And;
import com.effektif.workflow.api.condition.Equals;
import com.effektif.workflow.api.condition.Or;
import com.effektif.workflow.api.model.Deployment;
import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.types.ListType;
import com.effektif.workflow.api.types.TextType;
import com.effektif.workflow.api.workflow.Binding;
import com.effektif.workflow.api.workflow.MultiInstance;
import com.effektif.workflow.impl.data.types.ObjectType;
import com.effektif.workflow.impl.util.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.Test;

import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.effektif.workflow.test.WorkflowTest;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;

/**
 * Tests combinations of parallel gateways and embedded subprocesses.
 *
 * <AUTHOR> Hilton
 */
public class ParallelEmbeddedSubprocessTest extends WorkflowTest {

  /**
   * Tests parallel tasks inside a subprocess.
   * <pre>
   *
   *     ┌──────────────────────┐
   *     │ subprocess           │
   *     │                      │
   *  ◯──┤ ◯─→<+>─→[s1]─→<+>─→◯ ├─→◯
   *     │     │          ↑     │
   *     │     └──→[s2]───┘     │
   *     └──────────────────────┘
   *
   * </pre>
   */
  @Test
  public void testSubprocessParallelGateway() {
    // @formatter:off
    ExecutableWorkflow workflow = new ExecutableWorkflow()
      .activity("start", new StartEvent()
        .transitionTo("subprocess"))
      .activity("subprocess", new EmbeddedSubprocess()
        .activity("subprocessstart", new StartEvent()
          .transitionTo("fork"))
        .activity("fork", new ParallelGateway()
          .transitionTo("s1")
          .transitionTo("s2"))
        .activity("s1", new ReceiveTask()
          .transitionTo("join"))
        .activity("s2", new ReceiveTask()
          .transitionTo("join"))
        .activity("join", new ParallelGateway()
          .transitionTo("endSubprocess"))
        .activity("endSubprocess", new EndEvent())
        .transitionTo("end"))
      .activity("end", new EndEvent());
    // @formatter:on

    deploy(workflow);

    WorkflowInstance workflowInstance = start(workflow);
    assertOpen(workflowInstance, "subprocess", "s1", "s2");

    workflowInstance = endTask(workflowInstance, "s1");
    workflowInstance = endTask(workflowInstance, "s2");
    assertTrue(workflowInstance.isEnded());
  }

  /**
   * Tests a subprocess inside parallel tasks.
   * <pre>
   *
   *          ┌────────────┐
   *          │ subprocess │
   *  ◯─→<+>─→┤            ├─→<+>─→◯
   *      │   │ [s1]─→[s2] │   ↑
   *      │   └────────────┘   │
   *      │                    │
   *      └───────→[t1]────────┘
   *
   * </pre>
   */
  @Test
  public void testParallelGatewaySubprocess() {
    // @formatter:off
    ExecutableWorkflow workflow = new ExecutableWorkflow()
      .activity("start", new StartEvent()
        .transitionTo("fork"))
      .activity("fork", new ParallelGateway()
        .transitionTo("subprocess")
        .transitionTo("t1"))
      .activity("subprocess", new EmbeddedSubprocess()
        .activity("s1", new ReceiveTask()
          .transitionTo("s2"))
        .activity("s2", new ReceiveTask())
        .transitionTo("join"))
      .activity("t1", new ReceiveTask()
        .transitionTo("join"))
      .activity("join", new ParallelGateway()
        .transitionTo("end"))
      .activity("end", new EndEvent());
    // @formatter:on

    deploy(workflow);

    WorkflowInstance workflowInstance = start(workflow);
    assertOpen(workflowInstance, "subprocess", "s1", "t1");

    workflowInstance = endTask(workflowInstance, "s1");
    workflowInstance = endTask(workflowInstance, "s2");
    assertOpen(workflowInstance, "t1");

    workflowInstance = endTask(workflowInstance, "t1");
    assertTrue(workflowInstance.isEnded());
  }




  @Test
  public void testNestedSubprocessVariablesTest112() {
    // @formatter:off
    ExecutableWorkflow workflow = new ExecutableWorkflow().subType("auto").sourceWorkflowId("aMMtPBercA__oneflow").type("one").appId("CRM")
            .activity("1730345019377", new StartEvent()
                    .transitionTo("1730345186554"))

            .activity("1730345186554", new BlockExecutionTask()
                    .extension("entityId", "object_RlhY0__c")
                    .extension("searchType", "all")
                    .extension("taskType", "query")
                    .extension("selectData", "first")
                    .extension("useFields", Arrays.asList("id", "name", "field_tJ24V__c", "field_pOF05__c", "field_2vCEF__c", "field_d5xM7__c", "field_w67vK__c", "field_Xxb9v__c"))
                    .outputList("$", "custom_variable##list_item__c")
                    .transitionTo("1730345059586"))

            .activity("1730345059586", new LoopTask()
                    .activity("subProcess", new EmbeddedSubprocess()
                            .multiInstance(
                                    new MultiInstance().sequential(true).asc(true)
                                            .variable("custom_variable##list_item__c", ObjectType.INSTANCE)
                                            .valuesExpression("custom_variable##list__c")
                            )
                            .activity("1730345060409", new StartEvent()
                                    .transitionTo("1730345087037"))
                            .activity("1730345087037",new ExclusiveGateway()
                                    .transitionWithConditionTo(new Or().condition(new And().condition(
                                            new Equals()
                                                    .left(new Binding<>().expression("custom_variable##list_item__c.field_Al22Z__c"))
                                                    .right(new Binding<>().value("cui1").type(new TextType())))),"a1")


                                    .transitionWithConditionTo(new Or().condition(new And().condition(
                                            new Equals()
                                                    .left(new Binding<>().expression("custom_variable##list_item__c.field_Al22Z__c"))
                                                    .right(new Binding<>().value("cui2").type(new TextType())))),"a2"))

                            .activity("a2", new BlockExecutionTask()
                                    .extension("taskType", "assign")
                                    .extension("assignment",Arrays.asList(new AssignmentItem("custom_variable##list_item__c.field_Al22Z__c","assign",0)))
                                    .transitionTo("1730345060410"))

                            .activity("a1", new BlockExecutionTask()
                                    .extension("taskType", "assign")
                                    .extension("assignment",Arrays.asList(new AssignmentItem("custom_variable##list_item__c.field_Al22Z__c","assign","999999")))
                                    .transitionTo("1730345060410"))

                            .activity("1730345060410", new EndEvent())
                    )
                    .transitionTo("1730345155443")
            )

            .activity("1730345155443", new EndEvent())
            .variable("custom_variable##list__c", new ListType())
            .property("customVariableTable", Arrays.asList(
                    new CustomVariable("list", "test","custom_variable##list__c","object","object_RlhY0__c")
            ));

    WorkflowEngine workflowEngine = cachedConfiguration.getWorkflowEngine();
    Deployment deployment = workflowEngine.deployWorkflow(workflow).checkNoErrorsAndNoWarnings();
    WorkflowInstance workflowInstance=workflowEngine.start(new TriggerInstance("")
            .workflowId(deployment.getWorkflowId())
            .data("custom_variable##list__c", Lists.of(
                    new HashMap<String, String>() {
                        {
                            put("field_Al22Z__c", "cui1");
                        }
                    },
                    new HashMap<String, String>() {
                      {
                        put("field_Al22Z__c", "cui2");
                      }
                    }
            ))
    );
    System.out.println(workflowInstance);

  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class AssignmentItem{
    private String key;
    private String operator;
    private Object value;
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class CustomVariable{
    private String type;
    private String label;
    private String id;
    private Object default_value;
    private String objectApiName;
    private String elementType;
    private String elementObjectApiName;

    /**
     * list object
     */
    public CustomVariable(String type, String label, String id, String elementType, String elementObjectApiName) {
      this.type = type;
      this.label = label;
      this.id = id;
      this.elementType = elementType;
      this.elementObjectApiName = elementObjectApiName;
    }
  }


}
