/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.impl.data.types;

import com.effektif.workflow.api.Configuration;
import com.effektif.workflow.api.types.DataType;
import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.data.AbstractDataType;
import com.effektif.workflow.impl.data.DataTypeImpl;
import com.effektif.workflow.impl.data.DataTypeService;
import com.effektif.workflow.impl.data.TypedValueImpl;
import com.effektif.workflow.impl.template.Hint;
import com.effektif.workflow.impl.template.Hints;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> Baeyens
 */
public class ObjectTypeImpl extends AbstractDataType<ObjectType>  {
  
  public Map<String,ObjectFieldImpl> fields;

  public ObjectTypeImpl() {
    super(ObjectType.INSTANCE);
  }

  public ObjectTypeImpl(ObjectType typeApi) {
    super(typeApi);
  }

  @Override
  public void setConfiguration(Configuration configuration) {
    super.setConfiguration(configuration);
    this.fields=addFields(configuration, type!=null ? type.getFields() : null);
  }

  public static Map<String, ObjectFieldImpl> addFields(Configuration configuration, List<ObjectField> fields) {
    if (fields !=null) {
      Map<String,ObjectFieldImpl> fieldImpls = new HashMap<>();
      DataTypeService dataTypeService = configuration.get(DataTypeService.class);
      for (ObjectField field: fields) {
        String key = field.getKey();
        DataType type = field.getType();
        DataTypeImpl typeImpl = dataTypeService.createDataType(type);
        fieldImpls.put(field.getKey(), new ObjectFieldImpl(key, typeImpl));
      }
      return fieldImpls;
    }
    return null;
  }

  @Override
  public DataTypeImpl parseDereference(String field, WorkflowParser parser) {
    if (fields!=null) {
      ObjectFieldImpl objectField = fields.get(field);
      if (objectField!=null) {
        return objectField.type;
      } else {
        parser.addWarning("Field '%s' does not exist", field);
      }
    }
    return new AnyTypeImpl();
  }

  @Override
  public TypedValueImpl dereference(Object value, String fieldName) {
    ObjectFieldImpl field = fields!=null ? fields.get(fieldName) : null;
    if (field!=null) {
      DataTypeImpl fieldType = field.type;
      Object fieldValue = field.getFieldValue(value);
      return new TypedValueImpl(fieldType, fieldValue);

    } else if ("toText".equals(fieldName)) {
      String textValue = convertInternalToText(value, new Hints().add(Hint.TO_TEXT));
      return new TypedValueImpl(new TextTypeImpl(), textValue);

    } else if (value instanceof Map) {
      Map mapValue = (Map) value;
      Object fieldValue = mapValue.get(fieldName);
      DataTypeService dataTypeService = configuration.get(DataTypeService.class);
      Class<?> fieldValueClass = fieldValue!=null ? fieldValue.getClass() : null;
      DataTypeImpl fieldDataType = dataTypeService.getDataTypeByValue(fieldValueClass);
      return new TypedValueImpl(fieldDataType, fieldValue);
    }
    return null;
  }

  @Override
  public String getFieldLabel(String field) {
    ObjectFieldImpl objectField = fields!=null ? fields.get(field) : null;
    return objectField!=null ? objectField.getName() : field;
  }
  @Override
  public boolean isStatic() {
    return false;
  }
}
