/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.impl.activity.types;

import com.effektif.workflow.api.activities.ExclusiveGateway;
import com.effektif.workflow.api.exception.EngineBranchDeadLoopException;
import com.effektif.workflow.api.exception.EngineBranchFetchSubDeptException;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.workflow.Activity;
import com.effektif.workflow.impl.WorkflowEngineImpl;
import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.activity.AbstractActivityType;
import com.effektif.workflow.impl.conditions.ConditionService;
import com.effektif.workflow.impl.conditions.EqualsImpl;
import com.effektif.workflow.impl.util.InstanceVariableUtil;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflow.TransitionImpl;
import com.effektif.workflow.impl.workflow.WorkflowImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.VariableInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;

import java.util.*;
import java.util.stream.Collectors;

import static com.effektif.workflow.api.ext.WorkflowConstants.EndState.EXCLUSIVE_GATEWAY_LOOP;


/**
 * <AUTHOR> Baeyens
 */
public class ExclusiveGatewayImpl extends AbstractActivityType<ExclusiveGateway> {

  private static final Logger log = WorkflowEngineImpl.log;

  ConditionService conditionService;


  public ExclusiveGatewayImpl() {
    super(ExclusiveGateway.class);
  }

  @Override
  public void parse(ActivityImpl activityImpl, ExclusiveGateway exclusiveGateway, WorkflowParser parser) {
    super.parse(activityImpl, exclusiveGateway, parser);
    conditionService = parser.getConfiguration(ConditionService.class);
  }

  @Override
  public void execute(ActivityInstanceImpl activityInstance) {
    avoidDeadLoop(activityInstance);
    ActivityImpl activity = activityInstance.activity;
    List<TransitionImpl> outgoingTransitions = activity.outgoingTransitions;

    TransitionImpl transition = findFirstTransitionThatMeetsCondition(activityInstance, outgoingTransitions);
    if (transition == null) {
      if (activity.defaultTransition != null) {
        transition = activity.defaultTransition;
      }
      //TODO 单元测试优化一下
//      else if (outgoingTransitions != null && outgoingTransitions.size() == 1) {
//        transition = outgoingTransitions.get(0);// 原版中 如果=1个 直接get(0)
//      }
      else if (outgoingTransitions != null && outgoingTransitions.size() > 1) {
        transition = handleUndefinedSelection(outgoingTransitions);
        //处理go_back
        if (transition == null) {
          transition = findTransitionWithGoBackCondition(outgoingTransitions);
        }
      }
    }



    if (transition != null) {      // 记录流程

      Map<String, Object> branchVariables = Maps.newHashMap();

      if (activityInstance.getWorkflowInstance().variableInstancesMap != null) {
        for (Map.Entry<String, VariableInstanceImpl> variableInstance : activityInstance.getWorkflowInstance().variableInstancesMap.entrySet()) {
          if (variableInstance != null) {
            branchVariables.put(variableInstance.getKey(), variableInstance.getValue().value);
          }
        }
      }


      activityInstance.takeTransition(transition);
//      workflowExecutingLogConsumer.sendMq(logEntity);
      ActivityImpl from = transition.from;
      ActivityImpl to = transition.to;
      log.info("take transition info,workflowInstantId:{},fromId:{},toId:{},transitionName:{}",
              activityInstance.getWorkflowInstance().getId().toString(),
              Objects.nonNull(from.activity) ? from.activity.getId() : null,
              Objects.nonNull(to) && Objects.nonNull(to.activity) ? to.activity.getId() : null,
              transition.name
      );
    } else {
      log.info("No transition selected.Gateway is blocked.workflowInstantId:{}", activityInstance.getWorkflowInstance().getId().toString());
      activityInstance.end();
      activityInstance.propagateToParent();
      //现在不能改动这个message，以后抽取出统一的errCode.
      throw new RuntimeException("Gateway is blocked");
    }
  }

  private void avoidDeadLoop(ActivityInstanceImpl activityInstance) {
    WorkflowInstanceImpl instance = activityInstance.getWorkflowInstance();
    boolean check=Boolean.TRUE.equals(instance.getProperty(WorkflowConstants.CHECK_EXCLUSIVE_GATEWAY_LOOP));
    if(check){
      WorkflowImpl workflow = instance.getWorkflow();
      Activity currentActivity = activityInstance.getActivity().activity;
      String id = currentActivity.getId();
      String name = currentActivity.getName();
      //15分钟内执行次数>50次， 或总次数>500次，则抛出异常
      Map<Integer,Long> currentActivityInstances = instance.activityInstances.stream().filter(item -> StringUtils.equals(item.getActivity().getId(), id)).collect(Collectors.toMap(item->Integer.valueOf(item.getId()),item->item.start));
      Long maxTime = currentActivityInstances.values().stream().max(Comparator.comparingLong(item -> item)).get();
      int hourOfQuarter = 15 * 60 * 1000;
      long lastOneHourExecuteTimes=currentActivityInstances.values().stream().filter(time -> time>=maxTime-hourOfQuarter).count();
      if(lastOneHourExecuteTimes > WorkflowConstants.EXCLUSIVE_GATEWAY_LOOP_MAX||currentActivityInstances.size()>WorkflowConstants.EXCLUSIVE_GATEWAY_LOOP_MAX*10){
        instance.setEnd(System.currentTimeMillis());
        instance.setEndState(EXCLUSIVE_GATEWAY_LOOP);
        throw new EngineBranchDeadLoopException(
                name,
                id,
                workflow.getSourceWorkflowId(),
                workflow.getId().getInternal(),
                workflow.getName(),
                instance.getId().getInternal(),lastOneHourExecuteTimes
        );
      }
    }
  }

  /**
   * called when this exclusive gateway is 'underspecified' so we have
   * to guess what's best to do at this point.
   * <p>
   * a) there is no outgoing transition with a condition that resolves to true
   * b) there is no default transition specified
   * c) and there is more than 1 transition
   */
  protected TransitionImpl handleUndefinedSelection( List<TransitionImpl> outgoingTransitions) {
    return findFirstTransitionWithoutCondition(outgoingTransitions);
  }

  protected TransitionImpl findFirstTransitionWithoutCondition( List<TransitionImpl> outgoingTransitions) {
    if (outgoingTransitions != null) {
      for (TransitionImpl outgoingTransition : outgoingTransitions) {
        if (outgoingTransition.condition == null) {
          return outgoingTransition;
        }
      }
    }
    return null;
  }

  protected TransitionImpl findTransitionWithGoBackCondition(List<TransitionImpl> outgoingTransitions) {
    if (outgoingTransitions != null) {
      for (TransitionImpl outgoingTransition : outgoingTransitions) {
        if (outgoingTransition.condition != null && outgoingTransition.condition instanceof EqualsImpl) {
          if ("go_back".equals(((EqualsImpl) outgoingTransition.condition).getRight().value.toString())) {
            return outgoingTransition;
          }
        }
      }
    }
    return null;
  }

  protected TransitionImpl findFirstTransitionThatMeetsCondition(ActivityInstanceImpl activityInstance, List<TransitionImpl> outgoingTransitions) {
    if (outgoingTransitions != null) {
//      updateBranchVariables(activityInstance,outgoingTransitions); 工作流代码，注释了
      TreeSet<TransitionImpl> tree = new TreeSet<>((t1, t2) -> {
        if (t1.serialNumber > t2.serialNumber) {
          return 1;
        } else if (t1.serialNumber == t2.serialNumber) {
          return -1; // iPaaS>> 为什么不返回0。推测必须正确设置serialNumber不出现相等的情况，否则会出现条件顺序混乱
        } else {
          return -1;
        }
      });
      tree.addAll(outgoingTransitions);
      try {
        for (TransitionImpl outgoingTransition : tree) {
          if (meetsCondition(outgoingTransition, activityInstance)) {
            log.debug("Excl gw takes transition " + outgoingTransition);
            return outgoingTransition;
          } else {
            log.debug("Excl gw condition " + outgoingTransition.condition + " not met: " + outgoingTransition);
          }
        }
      } catch (EngineBranchFetchSubDeptException e) {
        InstanceVariableUtil.setSubDeptErrorRetry(activityInstance,e);
        throw e;
      }
    }
    return null;
  }

  private void updateBranchVariables(ActivityInstanceImpl activityInstance, List<TransitionImpl> outgoingTransitions) {
    /**
     * 如果失败 记录失败原因, 并让其可以自动修复, 自动修复时 要观察其状态是否已经通过
     * 工作流失败时只作记录不做重试
     */
    InstanceVariableUtil.updateVariable(activityInstance,outgoingTransitions);

  }

  protected boolean meetsCondition(TransitionImpl outgoingTransition, ActivityInstanceImpl activityInstance) {
    boolean meetsCondition = false;
    if (outgoingTransition.condition != null) {
      meetsCondition = outgoingTransition.condition.eval(activityInstance);
      //特殊处理协同审批go_back原路返回逻辑
      if (meetsCondition && WorkflowConstants.AppId.APP_ID_XT.equals(activityInstance.workflow.getAppId())) {
        Map<String, VariableInstanceImpl> variableMap = activityInstance.workflowInstance.variableInstancesMap;
        if (variableMap != null && variableMap.size() > 0) {
          if (variableMap.containsKey(WorkflowConstants.SystemVariable.DECIDE) && variableMap.get(WorkflowConstants.SystemVariable.DECIDE).getValue() != null &&
            WorkflowConstants.Action.GO_BACK.equals(variableMap.get(WorkflowConstants.SystemVariable.DECIDE).getValue().toString())) {
            //处理auto_go_back
            if (variableMap.containsKey(WorkflowConstants.SystemVariable.AUTO_GO_BACK_FLAG) &&
              "true".equals(variableMap.get(WorkflowConstants.SystemVariable.AUTO_GO_BACK_FLAG).getValue().toString())) {
              return meetsCondition;
            }
            String activityId = outgoingTransition.to.id;
            if (variableMap.containsKey(WorkflowConstants.SystemVariable.PROVIOUS_USER_TASK_ACTIVITY_ID_LIST)){
              VariableInstanceImpl variable = variableMap.get(WorkflowConstants.SystemVariable.PROVIOUS_USER_TASK_ACTIVITY_ID_LIST);
              if (variable.getValue() instanceof List && !((List<String>)(variable.getValue())).contains(activityId)) {
                meetsCondition = false;
              }
            }
            if (meetsCondition && variableMap.containsKey(WorkflowConstants.SystemVariable.LATEST_USER_TASK_ID) &&
              !activityId.equals(variableMap.get(WorkflowConstants.SystemVariable.LATEST_USER_TASK_ID).getValue().toString())) {
              meetsCondition = false;
            }
          }
        }
      }
    }
    return meetsCondition;
  }

  @Override
  public boolean isFlushSkippable() {
    return true;
  }

  @Override
  public boolean saveTransitionsTaken() {
    return true;
  }
}
