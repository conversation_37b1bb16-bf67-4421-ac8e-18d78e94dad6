package com.effektif.workflow.api.model;

import java.util.HashMap;
import java.util.Map;

/**
 * desc: 阶段推进器任务变更记录项
 * author: liangnan
 * date: 2023/8/12 15:55
 */
public class StageTaskStateChangeRecord {
    public static final String ERROR = "error";
    public static final String ERROR_IN = "error_in";
    public static final String ERROR_OUT = "error_out";
    public static final String IN_PROGRESS = "in_progress";
    public static final String UNCOMPLETED = "uncompleted";

    private String state;
    private Long stamp;

    public StageTaskStateChangeRecord() {
    }

    public StageTaskStateChangeRecord(String state, Long stamp) {
        this.state = state;
        this.stamp = stamp;
    }

    public StageTaskStateChangeRecord(String state) {
        this.state = state;
        this.stamp = System.currentTimeMillis();
    }

    public static StageTaskStateChangeRecord errorIn() {
        return new StageTaskStateChangeRecord(ERROR_IN);
    }

    public static StageTaskStateChangeRecord errorOut() {
        return new StageTaskStateChangeRecord(ERROR_OUT);
    }

    public static StageTaskStateChangeRecord error() {
        return new StageTaskStateChangeRecord(ERROR);
    }

    public static StageTaskStateChangeRecord inProgress() {
        return new StageTaskStateChangeRecord(IN_PROGRESS);
    }

    public static StageTaskStateChangeRecord uncompleted() {
        return new StageTaskStateChangeRecord(UNCOMPLETED);
    }

    public static Map<String, Object> getRecord(String state) {
        Map<String, Object> taskStateRecordItem = new HashMap<>();
        taskStateRecordItem.put("state", state);
        taskStateRecordItem.put("stamp", System.currentTimeMillis());
        return taskStateRecordItem;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getStamp() {
        return stamp;
    }

    public void setStamp(Long stamp) {
        this.stamp = stamp;
    }
}
