# iPaaS 流程引擎 (ipaas-flow-engine) 开发文档

## 1. 模块概述

iPaaS 流程引擎模块是基于 Effektif 工作流引擎的核心服务层实现，提供类似 Zapier 的自动化流程能力。本模块专注于业务逻辑实现，通过API 接口为 web 和 base 模块提供服务。

## 2. 架构设计

### 2.1 基于 Effektif 的分层架构

本架构深度集成 Effektif 工作流引擎，利用其完整的生命周期管理、存储抽象和数据处理机制。

```plantuml
@startuml
package "ipaas-web" {
  [FlowManagementController 集成流管理] as FlowManagementController
  [TriggerManagementController] as TriggerManagementController
  [TriggerFeignClient]
}

package "ipaas-core" {
  [TriggerController]
  [TriggerRpcController]
  frame "trigger实现"{
  [PollingTriggerImpl] as PollingTrigger
  [WebhookTriggerImpl] as WebhookTrigger
  [ScheduleTriggerImpl] as ScheduleTrigger
  }
}

package "ipaas-api" {
  [FlowService]
  [TriggerService]
  [TriggerRpcService]
}

package "ipaas-flow-engine" {
  [FlowServiceImpl] as FlowServiceImpl
  [FlowExecutionService] as ExecutionServiceImpl
  [ActionService] as ActionServiceImpl
  [EffektifAdapter] as EffektifAdapter
}

package "effektif-workflow-api" {
  [WorkflowEngine] as EffektifEngine
  [ExecutableWorkflow] as EffektifWorkflow
  [TriggerInstance] as EffektifTrigger
  [WorkflowInstance] as EffektifInstance
  [DataContainer] as EffektifDataContainer
}

package "effektif-workflow-impl" {
  [WorkflowEngineImpl] as EffektifEngineImpl
  [WorkflowInstanceImpl] as EffektifInstanceImpl
  [MemoryConfiguration] as MemoryConfig
  [MongoConfiguration] as MongoConfig
  frame "Memory存储层" {
    [MemoryWorkflowStore]
    [MemoryWorkflowInstanceStore]
    [MemoryJobStore]
    [MemoryTaskStore]
  }
}

database "MongoDB" {
  [FlowRepository] as FlowRepo
  [ExecutionRepository] as ExecRepo
  [EffektifWorkflowStore] as EffektifWorkflowRepo
  [EffektifInstanceStore] as EffektifInstanceRepo
}

[TriggerFeignClient] ..> [TriggerRpcService] : 实现
[TriggerRpcController] ..> [TriggerRpcService] : 实现
[FlowServiceImpl] ..> [FlowService] : 实现

FlowManagementController --> FlowService : Jar API调用
TriggerManagementController -->TriggerFeignClient : 调用
TriggerFeignClient --> [TriggerRpcController] : RPC API调用

PollingTrigger ..> TriggerService : 实现
WebhookTrigger ..> TriggerService : 实现
ScheduleTrigger ..> TriggerService : 实现

FlowServiceImpl --> EffektifAdapter : Effektif集成
EffektifAdapter --> EffektifEngine : Effektif API调用
ExecutionServiceImpl --> EffektifAdapter : 执行管理
TriggerService --> ExecutionServiceImpl : 启动流程

EffektifEngine --> EffektifEngineImpl : 实现
EffektifEngineImpl --> MemoryConfig : 开发/测试环境
EffektifEngineImpl --> MongoConfig : 生产环境

FlowServiceImpl --> FlowRepo : 集成流配置存储
ExecutionServiceImpl --> ExecRepo : 执行历史
EffektifEngineImpl --> EffektifWorkflowRepo : 工作流存储
EffektifEngineImpl --> EffektifInstanceRepo : 实例存储
@enduml
```

### 2.2 核心服务组件

基于 Effektif 工作流引擎的核心组件设计：

1. **FlowService**：流程定义的创建、更新、发布和状态管理服务（基于 Effektif ExecutableWorkflow）
2. **BaseTriggerService**：基础触发器服务，接收触发数据并启动流程（基于 Effektif TriggerInstance）
3. **ActionService**：动作配置和执行服务（基于 Effektif ActivityType）
4. **FlowExecutionService**：流程实例的创建和执行管理服务（基于 Effektif WorkflowInstance）
5. **EffektifAdapter**：Effektif API 适配器，处理数据转换和集成
6. **ExecutionHistoryService**：执行历史记录和查询服务

### 2.3 Effektif 集成层次

#### 2.3.1 存储层集成

- **开发/测试环境**：使用 Effektif MemoryConfiguration，支持快速开发和测试
- **生产环境**：使用 Effektif MongoConfiguration，提供持久化和分布式支持
- **混合模式**：MongoMemoryConfiguration，工作流定义用内存，实例用 MongoDB

#### 2.3.2 数据处理层集成

- **持久化数据**：利用 DataContainer.data 存储业务关键数据和跨节点传递数据
- **临时数据**：利用 DataContainer.transientData 存储运行时上下文和敏感信息

#### 2.3.3 生命周期集成

- **工作流部署**：基于 Effektif 的解析、验证、存储和缓存机制
- **实例执行**：利用 Effektif 的锁定、异步执行和状态管理
- **错误处理**：基于 Effektif 的异常状态和恢复机制

### 2.4 模块职责分工

- **ipaas-flow-engine**：核心业务逻辑，Service 层实现，深度集成 Effektif API
- **ipaas-web**：Web API 层，调用 flow-engine 的服务
- **ipaas-core**：具体触发器实现（轮询、Webhook等），调用 flow-engine 的 BaseTriggerService
- **effektif-workflow-impl**：提供工作流引擎的底层实现和存储抽象

## 3. 核心领域模型

### 3.1 基于 Effektif 的领域模型

基于 Effektif DataContainer 和 ExecutableWorkflow 的领域模型：

```plantuml
@startuml
enum FlowStatus {
  DRAFT
  PUBLISHED
  RUNNING
  PAUSED
  STOPPED
  ARCHIVED
}

enum ExecutionStatus {
  PENDING
  RUNNING
  SUCCESS
  FAILED
  CANCELLED
  TIMEOUT
  RETRYING
}

class Flow {
  +String id
  +String name
  +String description
  +FlowStatus status
  +Trigger trigger
  +List<Action> actions
  +Map<String, Object> config
  +List<String> tags
  +String category
  +int version
  +Date createdAt
  +Date updatedAt
  +String createdBy
  +String updatedBy
  --基于 Effektif ExecutableWorkflow--
  +ExecutableWorkflow toExecutableWorkflow()
  +static Flow fromExecutableWorkflow(ExecutableWorkflow)
}

class Trigger {
  +String id
  +String name
  +String type
  +Map<String, Object> config
  +boolean enabled
  +String description
}

class Action {
  +String id
  +String name
  +String type
  +int order
  +Map<String, Object> config
  +String condition
  +boolean enabled
  +String errorHandling
  +Map<String, Object> inputMapping
  +Map<String, Object> outputMapping
}

class FlowExecution {
  +String id
  +String flowId
  +int flowVersion
  +ExecutionStatus status
  +Map<String, Object> triggerData
  +Map<String, Object> contextData
  +List<ActionExecution> actionExecutions
  +Date startTime
  +Date endTime
  +long duration
  +String errorMessage
  +String environment
  +String executedBy
}

class ActionExecution {
  +String id
  +String actionId
  +String name
  +ExecutionStatus status
  +Map<String, Object> input
  +Map<String, Object> output
  +Date startTime
  +Date endTime
  +long duration
  +String errorMessage
  +int retryCount
}

note right of Flow
  直接使用 Effektif TriggerInstance
  TriggerInstance 本身已包含 tenantId 字段
  继承自 DataContainer，支持 data/transientData
end note

Flow "1" *-- "1" Trigger
Flow "1" *-- "n" Action
FlowExecution "n" -- "1" Flow
FlowExecution "1" *-- "n" ActionExecution
@enduml
```

### 3.2 与 Effektif API 的映射关系

```plantuml
@startuml
package "iPaaS Models" {
  class Flow
  class FlowExecution
}

package "Effektif API Models" {
  class ExecutableWorkflow
  class TriggerInstance
  class WorkflowInstance
  class DataContainer
}

package "Effektif Impl Models" {
  class WorkflowInstanceImpl
  class ActivityInstanceImpl
}

Flow --> ExecutableWorkflow : 转换映射
FlowExecution --> WorkflowInstance : 基于WorkflowInstance
FlowExecution --> WorkflowInstanceImpl : 实例管理

note right of Flow
  基于 ExecutableWorkflow
  保持 Effektif 生命周期
end note

note right of TriggerInstance
  Effektif 原生支持多租户
  内置 tenantId 字段
  继承 DataContainer
  data: 持久化业务数据
  transientData: 临时运行时数据
end note

note right of FlowExecution
  基于 WorkflowInstance
  利用 Effektif 状态管理
  支持锁定和并发控制
end note
@enduml
```

## 4. 基于 Effektif 的流程状态流转

### 4.1 流程定义状态流转（iPaaS 层）

```plantuml
@startuml
[*] --> DRAFT
DRAFT --> PUBLISHED : 发布到Effektif
PUBLISHED --> RUNNING : 启动
RUNNING --> PAUSED : 暂停
PAUSED --> RUNNING : 恢复
RUNNING --> STOPPED : 停止
PUBLISHED --> STOPPED : 停止
STOPPED --> ARCHIVED : 归档
RUNNING --> ARCHIVED : 归档

note right of PUBLISHED
  调用 Effektif
  deployWorkflow()
  缓存到 WorkflowCache
end note
@enduml
```

### 4.2 工作流实例状态流转（基于 Effektif）

```plantuml
@startuml
[*] --> PENDING
PENDING --> RUNNING : Effektif.start()
RUNNING --> SUCCESS : 正常结束
RUNNING --> FAILED : 执行失败
RUNNING --> CANCELLED : cancel()
RUNNING --> TIMEOUT : 执行超时
FAILED --> RETRYING : 重试机制
RETRYING --> RUNNING : 重新执行

note right of RUNNING
  Effektif WorkflowInstance
  支持锁定机制
  异步/同步执行
  状态持久化
end note

note right of SUCCESS
  endAndPropagateToParent()
  workflowInstanceEnded()
  资源清理
end note
@enduml
```

### 4.3 Effektif 活动实例状态

```plantuml
@startuml
[*] --> starting
starting --> executing : execute()
executing --> propagateToParent : 完成
executing --> joining : 等待合并
joining --> propagateToParent : 合并完成
propagateToParent --> [*] : end()

note right of starting
  STATE_STARTING
  STATE_STARTING_MULTI_INSTANCE
  STATE_STARTING_MULTI_CONTAINER
end note

note right of executing
  活动具体执行逻辑
  可能产生异步工作
end note
@enduml
```

## 5. 基于 Effektif 的核心服务流程

### 5.1 流程创建与发布流程（深度集成 Effektif）

```plantuml
@startuml
participant "WebController" as WC
participant "FlowService" as FS
participant "EffektifAdapter" as EA
participant "FlowRepository" as FR
participant "EffektifEngine" as EE
participant "WorkflowCache" as WC_Cache

WC -> FS : createFlow(tenantId, flowDTO)
FS -> FS : 验证流程定义
FS -> EA : createTenantFlow(tenantId, flow)
EA -> EA : 包装tenantId到properties
EA -> FR : save(tenantFlow)
FR --> EA : flowId
EA --> FS : 返回flowId
FS --> WC : 返回创建结果

WC -> FS : publishFlow(tenantId, flowId)
FS -> FR : findByTenantIdAndId(tenantId, flowId)
FR --> FS : tenantFlow
FS -> FS : 验证流程完整性
FS -> EA : convertToExecutableWorkflow(tenantFlow)
EA -> EA : 转换为Effektif ExecutableWorkflow
EA -> EA : 设置tenantId到workflow.properties
EA -> EE : deployWorkflow(executableWorkflow)
EE -> EE : WorkflowParser.parse()
EE -> EE : workflowStore.insertWorkflow()
EE -> WC_Cache : workflowCache.put(workflowImpl)
EE --> EA : deployment
EA -> FR : updateStatus(tenantId, flowId, PUBLISHED)
EA --> FS : 返回发布结果
FS --> WC : 返回发布结果

note right of EE
  Effektif 完整部署流程：
  1. 解析和验证
  2. 生成WorkflowId
  3. 存储到WorkflowStore
  4. 缓存WorkflowImpl
  5. 触发器发布通知
end note
@enduml
```

### 5.2 基础触发器服务流程

```plantuml
@startuml
participant "BaseTriggerImpl" as BTI
participant "BaseTriggerService" as BTS
participant "EffektifAdapter" as EA
participant "EffektifEngine" as EE
participant "WorkflowInstanceStore" as WIS

note over BTI
  具体实现在 ipaas-core 模块
  如：PollingTriggerImpl
      WebhookTriggerImpl
      ScheduleTriggerImpl
end note

BTI -> BTS : triggerFlow(triggerId, triggerData)
BTS -> BTS : 验证触发器配置
BTS -> EA : findFlowByTriggerId(triggerId)
EA --> BTS : flow
BTS -> EA : createTriggerInstance(triggerData)
EA -> EA : 创建Effektif TriggerInstance
EA -> EA : 设置data字段（持久化业务数据）
EA -> EA : 设置transientData字段（临时运行时数据）
EA -> EE : start(triggerInstance)

EE -> EE : startInitialize(triggerInstance)
EE -> EE : 创建WorkflowInstanceImpl
EE -> EE : 设置锁定机制
EE -> EE : 应用触发器数据到变量
EE -> EE : startExecute(workflowInstance)
EE -> EE : executeWork() - 执行活动实例
EE -> WIS : insertWorkflowInstance()
EE --> EA : workflowInstance

EA -> BTS : recordExecution(execution)
BTS --> BTI : 返回执行结果

note right of EE
  Effektif 完整启动流程：
  1. 实例初始化和锁定
  2. 触发器数据应用
  3. 启动活动执行
  4. 异步/同步工作处理
  5. 状态持久化
end note
@enduml
```
