<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="crmLog" class="ch.qos.logback.core.ConsoleAppender">
        <file>${catalina.home}/logs/effektif-mongo.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/effektif-mongo.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{HH:mm:ss} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                }%n
            </pattern>
        </encoder>
    </appender>

    <logger name="com.facishare" level="INFO" additivity="false">
        <appender-ref ref="crmLog" />
    </logger>

    <root level="INFO">
        <appender-ref ref="crmLog"/>
    </root>

</configuration>
