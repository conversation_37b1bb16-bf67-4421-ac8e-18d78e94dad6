package com.fxiaoke.erpdss.ipaas.web.controller;

import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.fxiaoke.erpdss.ipaas.common.module.Result;
import com.fxiaoke.erpdss.ipaas.flow.service.FlowManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 流管理
 *
 * <AUTHOR> (^_−)☆
 */
@RestController
@RequestMapping("cep/flow")
public class FlowManagementController {
    @Autowired
    private FlowManagementService flowManagementService;

    @RequestMapping("deploy")
    public Result<ExecutableWorkflow> deploy(ExecutableWorkflow workflow) {
        return flowManagementService.deployFlow(workflow);
    }
}
