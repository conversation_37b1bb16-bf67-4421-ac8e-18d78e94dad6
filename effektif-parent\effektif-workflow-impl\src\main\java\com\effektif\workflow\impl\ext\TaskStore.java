package com.effektif.workflow.impl.ext;

import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.api.query.UserTaskQuery;

import java.util.List;
import java.util.Map;

/**
 * zhenghaibo
 * 16/6/12 15:38
 */
public interface TaskStore {

  TaskId generateTaskId();

  void insertTask(Task task);

  List<Task> findTasks(String tenantId, UserTaskQuery taskQuery);

  Task assignTask(String tenantId, String taskId, List<String> assigneeIds);

  Task completeTask(String tenantId, String taskId, String actionType);

  Task completeTask(String tenantId, String taskId, String actionType, List<ApprovalOpinion> opinionList, List<String> assigneeIds, Map<String, List<String>> nextTaskAssignee);

  Task completeTaskItem(String tenantId, String taskId, String actionType, List<ApprovalOpinion> opinionList, List<String> assigneeIds, String state);

  Task completeStageTask(String tenantId, String taskId, String actionType, List<ApprovalOpinion> opinionList, List<String> assigneeIds, String state);

  Task completeTask(String tenantId, String taskId, String actionType, String state, List<ApprovalOpinion> opinionList);

  Task updateApprovalOpinion(String tenantId, String taskId, List<ApprovalOpinion> opinionList, List<String> assigneeIds);

  void deleteTasks(String tenantId, UserTaskQuery taskQuery);

  /**
   * 删除代办task
   * 1、任务生成下一节点之后再删除当前的todo_task
   * 2、阶段子任务（taskType == "taskItem"）特殊，完成后不生成后续节点，这时要删除当前节点
   *
   * @param tenantId
   * @param taskId
   */
  void removeTodoTask(String tenantId, String taskId);

  void updateTaskState(String tenantId, String taskId, String state,List<ApprovalOpinion> opinions);

  Task findTaskById(String tenantId, String taskId);
}
