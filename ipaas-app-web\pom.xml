<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fxiaoke.erpdss</groupId>
        <artifactId>fs-erp-ipaas</artifactId>
        <version>2.0-SNAPSHOT</version>
    </parent>

    <artifactId>ipaas-app-web</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- iPaaS Dependencies -->
        <dependency>
            <groupId>com.fxiaoke.erpdss</groupId>
            <artifactId>ipaas-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.erpdss</groupId>
            <artifactId>ipaas-flow-engine</artifactId>
            <version>${project.version}</version>
            <scope>runtime</scope>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.erpdss</groupId>
            <artifactId>ipaas-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.erpdss</groupId>
            <artifactId>ipaas-spring-common</artifactId>
        </dependency>

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <!-- 导入公共配置模块的资源 -->
            <resource>
                <directory>../modules/ipaas-spring-common/src/main/resources</directory>
            </resource>
            <resource>
                <directory>../modules/ipaas-db-proxy/src/main/resources</directory>
            </resource>
        </resources>


        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <jvmArguments>
                        --add-opens java.base/java.net=ALL-UNNAMED
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/java.time=ALL-UNNAMED
                        --add-opens java.base/java.nio=ALL-UNNAMED
                    </jvmArguments>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>