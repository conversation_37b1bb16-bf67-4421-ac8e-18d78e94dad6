package com.effektif.workflow.impl.ext.latencyTask;

import com.effektif.workflow.api.activities.ExecutionTask;
import com.effektif.workflow.api.activities.UserLatencyTask;
import com.effektif.workflow.api.ext.ExecutionPojo;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.api.workflow.Extensible;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;

/**
 * liugh
 * 19/2/28 22:50
 */
public class AutoTask extends Extensible implements Serializable {
  private static final long serialVersionUID = 4517415651506340745L;
  protected TaskId id;
  protected String tenantId;
  protected String appId;
  protected String name;
  protected String entityId;
  protected String objectId;
  protected String workflowId;
  protected String sourceWorkflowId;
  protected String workflowInstanceId;
  protected String activityId;
  protected String activityInstanceId;
  protected Long createTime;
  protected Long modifyTime;

  /**
   * 定时节点完成方式：完成事件触发/超时触发
   */
  protected String actionType;

  protected String state;

  /**
   * 节点类型：自动节点/定时等待节点
   */
  protected String taskType;
  /**
   * 节点类型：自动节点/定时等待节点,
   * @TODO 该字段后面会作为 流程的类型 来使用
   */
  protected String type;
  protected String description;
  /**
   * 节点停留时间
   */
  protected Object latencyTime;
  /**
   * 节点停留时间的单位  1-天、2-小时、3-分钟
   */
  protected String latencyUnit;

  /**
   * 自动节点的后动作
   */
  protected List<ExecutionPojo> executionList;

  /**
   * 定时等待节点的等待方式：1-设置等待时间，2-设置等待到未来某个时间点
   */
  protected String timeType;
  /**
   * 定时等待节点的时间扩展
   */
  protected Map<String, Object> eventExtension;
  /**
   * bpm扩展属性
   */
  protected Map<String, Object> bpmExtension;

  /**
   * true:延时执行
   */
  private Boolean delay;
  /**
   * condition:条件等待;delay:延时等待
   */
  private String delayStrategy;

  /**
   * 支持公式,也支持常量
   */
  private String remindLatency;

  /**
   * 条件
   */
  protected Object rule;
  /**
   * 函数条件
   */
  protected Object functionRule;

  private String errMsg;

  private String taskExecuteState;

  private String applicantId;

  private Integer executionTimes;

  private String linkApp;
  //是否启用企业互联
  protected boolean linkAppEnable;
  //企业互联应用名称
  protected String linkAppName;
  //企业互联应用的类型
  protected int linkAppType;
  //企业互联外部企业字段
  protected String outerTenantField;
  //企业互联外部企业id
  protected String outerTenantId;
    //增加工作流过期索引时间
  protected Date expireDate;

  public AutoTask() {

  }

  public AutoTask(TaskId taskId, String activityInstanceId) {
    this.id = taskId;
    this.activityInstanceId = activityInstanceId;
  }

  /**
   * @param task
   * @param executionList
   * @param extMap
   * @return
   */
  public AutoTask buildExecutionTask(ExecutionTask task, List<ExecutionPojo> executionList, Map<String, String> extMap) {
    this.tenantId = extMap.get(WorkflowBindingEnum.tenantId.toString());
    this.appId = extMap.get(WorkflowBindingEnum.appId.toString());
    this.entityId = extMap.get(WorkflowBindingEnum.entityId.toString());
    this.objectId = extMap.get(WorkflowBindingEnum.objectId.toString());
    this.applicantId = extMap.get(WorkflowBindingEnum.applicantId.toString());
    this.workflowId = extMap.get("workflowId");
    this.workflowInstanceId = extMap.get("workflowInstanceId");
    this.name = task.getName();
    this.type = extMap.get(WorkflowBindingEnum.type.toString());
    this.taskType = TaskType.executionTask.toString();
    this.description = task.getDescription();
    this.activityId = task.getId();
    this.createTime = System.currentTimeMillis();
    this.state = WorkflowConstants.AutoTaskState.IN_PROGRESS;
    Object property = task.getProperty(WorkflowBindingEnum.bpmExtension.toString());
    if (property instanceof Map) {
      this.bpmExtension = (Map<String, Object>) property;
    }
    sortRowNo(executionList);
    this.executionList = deepCopyExecutionPojos(executionList);
    //延时等待节点参数补充
    this.delay = task.getDelay();
    this.delayStrategy = task.getDelayStrategy();
    this.latencyUnit = Objects.isNull(task.getLatencyUnit()) ? null : String.valueOf(task.getLatencyUnit());
    //时长不做解析,因为每次执行都会重新解析一次,直接存变量即可
    if(Objects.nonNull(latencyUnit)){
      this.remindLatency = String.valueOf(task.getRemindLatency());
      this.rule = task.getRule();
      this.functionRule = task.getFunctionRule();
    }
    this.linkApp = task.getLinkApp();
    this.linkAppType = task.getLinkAppType();
    this.linkAppName = task.getLinkAppName();
    this.linkAppEnable = task.isLinkAppEnable();
    this.outerTenantField = task.getOuterTenantField();
    this.outerTenantId = task.getOuterTenantId();

    return this;
  }

  private void sortRowNo(List<ExecutionPojo> executionList) {
    for (int i = 0; i < executionList.size(); i++) {
      ExecutionPojo pojo = executionList.get(i);
      if (pojo.getRowNo() == 0) {
        pojo.setRowNo(i);
      }
    }
  }

  public AutoTask buildLatencyTask(UserLatencyTask task, Map<String, String> extMap) {
    this.tenantId = extMap.get(WorkflowBindingEnum.tenantId.toString());
    this.appId = extMap.get(WorkflowBindingEnum.appId.toString());
    this.objectId = extMap.get(WorkflowBindingEnum.objectId.toString());
    this.entityId = extMap.get(WorkflowBindingEnum.entityId.toString());
    this.workflowId = extMap.get("workflowId");
    this.workflowInstanceId = extMap.get("workflowInstanceId");
    this.name = task.getName();
    this.type =  extMap.get(WorkflowBindingEnum.type.toString());
    this.taskType = TaskType.latencyTask.toString();
    this.description = task.getDescription();
    this.activityId = task.getId();
    this.createTime = System.currentTimeMillis();
    this.latencyTime = task.getLatencyTime();
    this.latencyUnit = task.getLatencyUnit();
    this.timeType = task.getTimeType();
    this.eventExtension = task.getEventExtension();
    this.state = WorkflowConstants.AutoTaskState.IN_PROGRESS;
    this.createTime = System.currentTimeMillis();
    this.sourceWorkflowId = extMap.get("sourceWorkflowId");
    Object property = task.getProperty(WorkflowBindingEnum.bpmExtension.toString());
    if (property != null && property instanceof Map) {
      this.bpmExtension = (Map<String, Object>) property;
    }
    this.linkAppName = extMap.get(WorkflowBindingEnum.linkAppName.name());
    this.outerTenantId = extMap.get(WorkflowBindingEnum.outerTenantId.name());
    this.outerTenantField = extMap.get(WorkflowBindingEnum.outerTenantField.name());
    this.linkApp = extMap.get(WorkflowBindingEnum.linkApp.name());
    this.linkAppEnable = Objects.isNull(extMap.get(WorkflowBindingEnum.linkAppEnable.name())) ? Boolean.FALSE : Boolean.parseBoolean(extMap.get(WorkflowBindingEnum.linkAppEnable.name()));
    this.linkAppType = Objects.isNull(extMap.get(WorkflowBindingEnum.linkAppType.name())) ? 0 : Integer.parseInt(extMap.get(WorkflowBindingEnum.linkAppType.name()));
    return this;
  }

  /**
   * 保存时去掉task属性
   *
   * @param executionList
   * @return
   */
  public static List<ExecutionPojo> deepCopyExecutionPojos(List<ExecutionPojo> executionList) {
    List<ExecutionPojo> rtnList = new ArrayList<>();
    if (executionList != null && !executionList.isEmpty()) {
      try {
        //将对象写入流中
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ObjectOutputStream objectOutputStream = new ObjectOutputStream(outputStream);
        objectOutputStream.writeObject(executionList);
        //从流中取出
        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        ObjectInputStream objectInputStream = new ObjectInputStream(inputStream);
        rtnList.addAll((ArrayList) objectInputStream.readObject());
        rtnList.forEach(executionPojo -> executionPojo.setTask(null));
      } catch (Exception e) {
        LoggerFactory.getLogger(AutoTask.class).error("deep copy ExecutionPojo error,executionList {},errorMsg: ", executionList, e);
      }

    }
    return rtnList;
  }

  public TaskId getId() {
    return id;
  }

  public void setId(TaskId id) {
    this.id = id;
  }

  public String getTenantId() {
    return tenantId;
  }

  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public Long getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Long createTime) {
    this.createTime = createTime;
  }

  public Long getModifyTime() {
    return modifyTime;
  }

  public void setModifyTime(Long modifyTime) {
    this.modifyTime = modifyTime;
  }

  public String getActivityId() {
    return activityId;
  }

  public void setActivityId(String activityId) {
    this.activityId = activityId;
  }

  public String getActivityInstanceId() {
    return activityInstanceId;
  }

  public void setActivityInstanceId(String activityInstanceId) {
    this.activityInstanceId = activityInstanceId;
  }

  public String getWorkflowInstanceId() {
    return workflowInstanceId;
  }

  public void setWorkflowInstanceId(String workflowInstanceId) {
    this.workflowInstanceId = workflowInstanceId;
  }

  public String getSourceWorkflowId() {
    return sourceWorkflowId;
  }

  public void setSourceWorkflowId(String sourceWorkflowId) {
    this.sourceWorkflowId = sourceWorkflowId;
  }

  public String getWorkflowId() {
    return workflowId;
  }

  public void setWorkflowId(String workflowId) {
    this.workflowId = workflowId;
  }

  public String getObjectId() {
    return objectId;
  }

  public void setObjectId(String objectId) {
    this.objectId = objectId;
  }

  public String getActionType() {
    return actionType;
  }

  public void setActionType(String actionType) {
    this.actionType = actionType;
  }

  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public String getState() {
    return state;
  }

  public void setState(String state) {
    this.state = state;
  }

  public String getLatencyUnit() {
    return latencyUnit;
  }

  public void setLatencyUnit(String latencyUnit) {
    this.latencyUnit = latencyUnit;
  }

  public enum TaskType {
    executionTask, latencyTask
  }

  public Object getLatencyTime() {
    return latencyTime;
  }

  public String getTimeType() {
    return timeType;
  }

  public String getType() {
    return type;
  }

  public String getTaskType() {
    return taskType;
  }

  public void setTaskType(String taskType) {
    this.taskType = taskType;
  }

  public void setType(String type) {
    this.type = type;
  }

  public String getEntityId() {
    return entityId;
  }

  public String getDescription() {
    return description;
  }

  public List<ExecutionPojo> getExecutionList() {
    return executionList;
  }

  public Map<String, Object> getEventExtension() {
    return eventExtension;
  }

  public void setEntityId(String entityId) {
    this.entityId = entityId;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public void setLatencyTime(Object latencyTime) {
    this.latencyTime = latencyTime;
  }

  public void setExecutionList(List<ExecutionPojo> executionList) {
    this.executionList = executionList;
  }

  public void setTimeType(String timeType) {
    this.timeType = timeType;
  }

  public void setEventExtension(Map<String, Object> eventExtension) {
    this.eventExtension = eventExtension;
  }

  public Map<String, Object> getBpmExtension() {
    return bpmExtension;
  }

  public void setBpmExtension(Map<String, Object> bpmExtension) {
    this.bpmExtension = bpmExtension;
  }

  public Boolean getDelay() {
    return delay;
  }

  public void setDelay(Boolean delay) {
    this.delay = delay;
  }

  public String getDelayStrategy() {
    return delayStrategy;
  }

  public void setDelayStrategy(String delayStrategy) {
    this.delayStrategy = delayStrategy;
  }

  public String getRemindLatency() {
    return remindLatency;
  }

  public void setRemindLatency(String remindLatency) {
    this.remindLatency = remindLatency;
  }

  public Object getRule() {
    return rule;
  }

  public void setRule(Object rule) {
    this.rule = rule;
  }

  public Object getFunctionRule() {
    return functionRule;
  }

  public void setFunctionRule(Object functionRule) {
    this.functionRule = functionRule;
  }

  public String getErrMsg() {
    return errMsg;
  }

  public void setErrMsg(String errMsg) {
    this.errMsg = errMsg;
  }

  public String getTaskExecuteState() {
    return taskExecuteState;
  }

  public void setTaskExecuteState(String taskExecuteState) {
    this.taskExecuteState = taskExecuteState;
  }

  public String getApplicantId() {
    return applicantId;
  }

  public void setApplicantId(String applicantId) {
    this.applicantId = applicantId;
  }

  public Integer getExecutionTimes() {
    return executionTimes;
  }

  public void setExecutionTimes(Integer executionTimes) {
    this.executionTimes = executionTimes;
  }


  public String getLinkApp() {
    return linkApp;
  }

  public void setLinkApp(String linkApp) {
    this.linkApp = linkApp;
  }

  public boolean isLinkAppEnable() {
    return linkAppEnable;
  }

  public void setLinkAppEnable(boolean linkAppEnable) {
    this.linkAppEnable = linkAppEnable;
  }

  public String getLinkAppName() {
    return linkAppName;
  }

  public void setLinkAppName(String linkAppName) {
    this.linkAppName = linkAppName;
  }

  public int getLinkAppType() {
    return linkAppType;
  }

  public void setLinkAppType(int linkAppType) {
    this.linkAppType = linkAppType;
  }

  public String getOuterTenantId() {
    return outerTenantId;
  }

  public void setOuterTenantId(String outerTenantId) {
    this.outerTenantId = outerTenantId;
  }

  public AutoTask expireDate(Date date) {
    this.expireDate = date;
    return this;
  }
}
