package com.effektif.mongo;

/**
 * zhenghaibo
 * 2017/3/6 17:53
 */
public interface TaskFields {
  String _ID = "_id";
  String NAME = "name";
  String DESCRIPTION = "description";
  String SOURCE_WORKFLOW_ID = "sourceWorkflowId";
  String WORKFLOW_ID = "workflowId";
  String WORKFLOW_INSTANCE_ID = "workflowInstanceId";
  String TENANT_ID = "tenantId";
  String APP_ID = "appId";
  String TYPE = "type";
  String ENTITY_ID = "entityId";
  String OBJECT_ID = "objectId";
  String APPLICANT_ID = "applicantId";
  String ASSIGNEE_IDS = "assigneeIds";
  String CANDIDATE_IDS = "candidateIds";
  String CARBON_COPY_IDS = "carbonCopyIds";
  String READ_CARBON_COPY_LOGS = "readCarbonCopyLogs";
  String ASSIGNEE = "assignee";
  //是否在实例中更换过审批人
  String ASSIGNEE_CHANGED = "assigneeChanged";
  //已废弃
  String ASSIGNEE_CHANGE_LOG = "assigneeChangeLog";
  //更换审批人历史
  String APPROVER_MODIFY_LOG = "approverModifyLog";
  String TASK_DELEGATE_LOG = "taskDelegateLog";
  //是否为审批例外人
  String CANDIDATE_EDITABLE = "candidateEditable";
  String TASK_TYPE = "taskType";
  String ACTION_TYPE = "actionType";
  String CREATE_TIME = "createTime";
  String MODIFY_TIME = "modifyTime";
  String MODIFIER = "modifier";
  String COMPLETED = "completed";
  String CANCELED = "canceled";
  String OPINIONS = "opinions";
  String STATE = "state";
  String SUB_STATE = "subState";
  String REMIND = "remind";
  String REMIND_LATENCY = "remindLatency";
  String LATENCY_UNIT = "latencyUnit";
  String REMIND_ID = "remindId";
  String ERR_MSG = "errMsg";
  String ERR_MSG_I18N = "errMsgI18N";
  String ACTIVITY_ID = "activityId";
  String ACTIVITY_INSTANCE_ID = "activityInstanceId";
  String WORKFLOW_NAME = "workflowName";
  String WORKFLOW_DESCRIPTION = "workflowDescription";
  String BPM_EXTENSION = "bpmExtension";
  String EXTENSION = "extension";
  String RULE = "rule";
  String REJECT_RULE = "rejectRule";
  String EXECUTION = "execution";
  String TIMEOUT_EXECUTION = "timeoutExecution";
  String REMINDERS = "reminders";
  String REMINDERSV2 = "remindersV2";
  String DELETED = "deleted";
  String SOURCE_TRANSITION = "sourceTransition";
  String TASK_EXECUTE_STATE = "taskExecuteState";
  String TASK_EXECUTION_EXECUTED_INDEX = "taskExecutionExecutedIndex";

  String DEMAND_BEYOND_ASSIGNEE = "demandBeyondAssignee";
  String BEYOND_ASSIGNEE = "beyondAssignee";
  String ALL_PASS_TYPE = "allPassType";
  String EXTERNAL_APPLY_TASK = "externalApplyTask";
  String ASSIGN_NEXT_TASK = "assignNextTask";
  String CANDIDATE_BY_PRE_TASK = "candidateByPreTask";
  String VERSION = "version";

  String  SUBMITTER = "submitter";
  String  ERROR_STATE = "errorState";

  String NEXT_TASK_ASSIGNEE = "nextTaskAssignee";

  String NEXT_TASK_ASSIGNEE_SCOPE = "nextTaskAssigneeScope";

  String PARENT_TASK_ID = "parentTaskId";
  String STAGE_ID = "stageId";

  String TERMINAL = "terminal";
  String NODE_TYPE = "nodeType";
  String IS_AUTO_AGREE = "isAutoAgree";
  //是否允许加签
  String TAG_ALLOW = "tagAllow";
  String TASK_TAG_IDS = "taskTagIds";
  String TAG_INFO = "tagInfo";
  String MASTER_TASK_ID = "masterTaskId";
  String AUTO_AGREED = "autoAgreed";
  String REMIND_LOGS = "remindLogs";
  String FUNCTION_RULE = "functionRule";
  String REJECT_FUNCTION_RULE = "rejectFunctionRule";
  String MOVE_TO_CURRENT_ACTIVITY_WHEN_REJECT = "moveToCurrentActivityWhenReject";
  String AUTO_AGREE_WHEN_BEYOND = "autoAgreeWhenBeyond";
  /**
   * 810 回复内容
   */
  String REPLIES = "replies";
  /**
   * 审批流 驳回到任意节点使用
   */
  String FINAL_STATE = "finalState";

  String TASK_ID  = "taskId";
  String PROCESS_IDS  = "processIds";
  String TASK_ITEM_CREATE_ERROR = "taskItemCreateError";
  String EXISTS_DATA_CHANGE_LOG = "existsDataChangeLog";
  String STATE_CHANGE_RECORD = "stateChangeRecord";
  String CUSTOM_EXTENSION = "customExtension";
  String ELEMENT_API_NAME = "elementApiName";
  String CUSTOM = "custom";
  String END_TIME = "endTime";
  String SUB_WORKFLOW_INSTANCE_ID = "subWorkflowInstanceId";
  String SUB_WORKFLOW_ID = "subWorkflowId";
  String SUB_SOURCE_WORKFLOW_ID = "subSourceWorkflowId";
  String SUB_WORKFLOW_NAME = "subWorkflowName";
  String SUB_PROCESS_REJECT_STRATEGY = "subProcessRejectStrategy";
  String SUB_PROCESS_LOSS_STRATEGY = "subProcessLossStrategy";
  String SUB_PROCESS_END_REJECT_TO_BEFORE_TASK_ACTIVITYID = "subProcessEndrejectToBeforeTaskActivityId";
  String REJECT_FROM_TASK_ID = "fromTaskId";
  String REJECT_FROM_ACTIVITY_ID = "fromActivityId";
  String REJECT_FROM_TASK_NAME = "fromTaskName";
  String PARALLEL_REJECTED_BEFORE_STATE = "parallelRejectedBeforeState";
  String PARALLEL_REJECTED_NON_GATEWAY_AND_RETURN = "parallelRejectedNonGatewayAndReturn";
  String PARALLEL_REJECTED_ENDED_THEN_RESUBMIT_AND_RETURN = "parallelRejectedEndedThenResubmitAndReturn";
  String PARALLEL_REJECTED_TO_CURRENT_NODE_TASK_ID = "parallelRejectedToCurrentNodeTaskId";
  String ASSIGNEE_FUNCTION_REQUEST_ID = "assigneeFunctionRequestId";
  String OPERATE_LOGS = "operateLogs";
  String OUTER_TENANT_ID = "outerTenantId";
}
