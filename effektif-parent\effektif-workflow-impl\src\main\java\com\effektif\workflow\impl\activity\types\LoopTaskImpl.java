package com.effektif.workflow.impl.activity.types;

import com.effektif.workflow.api.activities.LoopTask;
import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.activity.AbstractActivityType;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;

import java.util.List;


/**
 * <AUTHOR>
 * 循环节点， 其与嵌入子流程一致， 为了不影响嵌入子流程的功能，便于个性化设置； 这里新建独立类型
 */
public class LoopTaskImpl extends AbstractActivityType<LoopTask> {

  protected List<ActivityImpl> startActivities;

  public LoopTaskImpl() {
    super(LoopTask.class);
  }

  @Override
  public void parse(ActivityImpl activityImpl, LoopTask loopTask, WorkflowParser parser) {
    super.parse(activityImpl, loopTask, parser);
    this.startActivities = parser.getStartActivities(activityImpl);
  }

  @Override
  public void execute(ActivityInstanceImpl activityInstance) {
    if (startActivities!=null && !startActivities.isEmpty()) {
        for (ActivityImpl startActivity: startActivities) {
        activityInstance.execute(startActivity);
      }
    } else {
      activityInstance.onwards();
    }
  }

  @Override
  public String toString() {
    return activity.getId()!=null ? activity.getId() : Integer.toString(System.identityHashCode(this));
  }

}
