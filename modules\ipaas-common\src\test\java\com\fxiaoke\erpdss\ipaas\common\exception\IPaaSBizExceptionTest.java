package com.fxiaoke.erpdss.ipaas.common.exception;

import com.fxiaoke.erpdss.ipaas.common.module.ResultCode;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * IPaaSBizException 单元测试
 * 
 * <AUTHOR> (^_−)☆
 */
class IPaaSBizExceptionTest {

    @Test
    void testConstructorWithMessage() {
        // Given
        String message = "业务异常消息";

        // When
        IPaaSBizException exception = new IPaaSBizException(message);

        // Then
        assertThat(exception.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getCause()).isNull();
    }

    @Test
    void testConstructorWithMessageAndCause() {
        // Given
        String message = "业务异常消息";
        Throwable cause = new RuntimeException("原因异常");

        // When
        IPaaSBizException exception = new IPaaSBizException(message, cause);

        // Then
        assertThat(exception.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getCause()).isEqualTo(cause);
    }

    @Test
    void testConstructorWithCodeAndMessage() {
        // Given
        String code = "CUSTOM_BIZ_001";
        String message = "自定义业务异常";

        // When
        IPaaSBizException exception = new IPaaSBizException(code, message);

        // Then
        assertThat(exception.getCode()).isEqualTo(code);
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getCause()).isNull();
    }

    @Test
    void testConstructorWithCodeMessageAndCause() {
        // Given
        String code = "CUSTOM_BIZ_002";
        String message = "自定义业务异常";
        Throwable cause = new IllegalArgumentException("参数错误");

        // When
        IPaaSBizException exception = new IPaaSBizException(code, message, cause);

        // Then
        assertThat(exception.getCode()).isEqualTo(code);
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getCause()).isEqualTo(cause);
    }

    @Test
    void testInheritanceFromIPaaSException() {
        // Given
        IPaaSBizException exception = new IPaaSBizException("测试消息");

        // When & Then
        assertThat(exception).isInstanceOf(IPaaSException.class);
        assertThat(exception).isInstanceOf(RuntimeException.class);
    }

    @Test
    void testWithNullMessage() {
        // Given
        String message = null;

        // When
        IPaaSBizException exception = new IPaaSBizException(message);

        // Then
        assertThat(exception.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(exception.getMessage()).isNull();
    }

    @Test
    void testWithEmptyMessage() {
        // Given
        String message = "";

        // When
        IPaaSBizException exception = new IPaaSBizException(message);

        // Then
        assertThat(exception.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(exception.getMessage()).isEqualTo(message);
    }

    @Test
    void testWithNullCode() {
        // Given
        String code = null;
        String message = "测试消息";

        // When
        IPaaSBizException exception = new IPaaSBizException(code, message);

        // Then
        assertThat(exception.getCode()).isNull();
        assertThat(exception.getMessage()).isEqualTo(message);
    }

    @Test
    void testSerialVersionUID() {
        // Given
        IPaaSBizException exception = new IPaaSBizException("测试");

        // When & Then
        // 验证序列化版本ID存在
        assertThat(exception.getClass().getDeclaredFields())
                .anyMatch(field -> "serialVersionUID".equals(field.getName()));
    }

    @Test
    void testLombokAnnotationPresent() {
        // Given
        IPaaSBizException exception = new IPaaSBizException("测试");

        // When & Then
        // 验证类上有Lombok注解（@StandardException会生成构造函数）
        // 通过检查是否有Lombok生成的构造函数来验证
        assertThat(exception.getClass().getDeclaredConstructors()).hasSizeGreaterThan(0);

        // 验证类继承关系正确
        assertThat(exception).isInstanceOf(IPaaSException.class);
    }
}
