/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.impl;

import org.slf4j.Logger;


/**
 * <AUTHOR>
 */
public class SynchronousExecutorService implements ExecutorService {
  
  private static final Logger log = WorkflowEngineImpl.log;

  @Override
  public void startup() {}

  @Override
  public void shutdown() {}
  
  @Override
  public int getQueueDepth() {return 0;}
  
  @Override
  public void execute(Runnable command) {
    if (log.isDebugEnabled()) log.debug("Command executes synchronous");
    command.run();
  }
}
