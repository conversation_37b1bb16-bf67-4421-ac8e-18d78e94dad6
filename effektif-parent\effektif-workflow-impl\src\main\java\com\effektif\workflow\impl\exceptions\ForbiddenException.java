/*
 * Copyright (c) 2015, Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.impl.exceptions;

/**
 * Represents a HTTP 403 FORBIDDEN exception.
 * This exception will occur in case a user is authenticated but not
 * to do certain actions. (e.g. a required feature is missing) 
 * 
 * <AUTHOR>
 *
 * TODO Move to engine {@link com.effektif.workflow.impl.exceptions}
 */
public class ForbiddenException extends HttpMappedException {

  private static final long serialVersionUID = 1L;

  public ForbiddenException(String message) {
    super(message);
  }

  public ForbiddenException(String message, Throwable t) {
    super(message, t);
  }

  @Override
  public int getStatusCode() {
    return HttpStatusCode.FORBIDDEN;
  }

  public static void checkTrue(boolean condition, String message) {
    if (!condition) {
      throw new ForbiddenException(message);
    }
  }

  public static void checkNotNull(Object object, String message) {
    checkTrue(object!=null, message);
  }
}
