package com.effektif.workflow.impl.exceptions;

import lombok.Data;


@Data
public class ActivityInstanceNotExistsException extends RuntimeException{


    private String activityInstanceId;
    private String workflowInstanceId;

    public ActivityInstanceNotExistsException(String workflowInstanceId,String activityInstanceId) {
        this.workflowInstanceId = workflowInstanceId;
        this.activityInstanceId = activityInstanceId;
    }

    @Override
    public String toString() {
        return  "workflowInstanceId:" + workflowInstanceId+ ",activityInstanceId:" + activityInstanceId ;
    }
}
