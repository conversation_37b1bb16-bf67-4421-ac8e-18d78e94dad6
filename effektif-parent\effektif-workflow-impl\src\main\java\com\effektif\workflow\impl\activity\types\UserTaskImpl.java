package com.effektif.workflow.impl.activity.types;

import com.effektif.workflow.api.activities.UserTask;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.activity.AbstractActivityType;
import com.effektif.workflow.impl.activity.types.userTask.UserTaskManager;
import com.effektif.workflow.impl.ext.Task;
import com.effektif.workflow.impl.ext.TaskStore;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;

/**
 * zhenghaibo
 * 16/5/13 15:13
 */
public class UserTaskImpl extends AbstractActivityType<UserTask> {

  private TaskStore taskStore;

  public UserTaskImpl() {
    super(UserTask.class);
  }

  @Override
  public void parse(ActivityImpl activityImpl, UserTask userTask, WorkflowParser parser) {
    super.parse(activityImpl, userTask, parser);
    this.taskStore = parser.getConfiguration(TaskStore.class);
  }

  public TaskStore getTaskStore() {
    return taskStore;
  }

  @Override
  public void execute(ActivityInstanceImpl activityInstance) {
    UserTaskManager.generateTask(this,activityInstance);
  }


  public void insertTask(Task task) {
    taskStore.insertTask(task);
  }
  public TaskId generateTaskId(){
    return getTaskStore().generateTaskId();
  }
}

