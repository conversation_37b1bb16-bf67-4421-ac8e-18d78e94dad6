package com.effektif.workflow.impl.memory;

import com.effektif.workflow.api.model.Id;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.ext.blockTask.BlockTask;
import com.effektif.workflow.impl.ext.blockTask.BlockTaskStore;
import com.facishare.paas.workflow.bus.api.type.TaskState;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;


public class MemoryBlockTaskStore implements BlockTaskStore {
    protected int nextId = 1;
    protected Map<Id, BlockTask> tasks = Collections.synchronizedMap(new LinkedHashMap<Id, BlockTask>());

    @Override
    public TaskId generateTaskId() {
        return new TaskId(Integer.toString(nextId++));
    }

    @Override
    public void insertTask(BlockTask task) {
        tasks.put(task.getId(), task);

        if (task.getId() == null) {
            String taskId = Integer.toString(nextId++);
            task.setId(new TaskId(taskId));
        }
        task.setModifyTime(System.currentTimeMillis());
        tasks.put(task.getId(), task);
    }

    @Override
    public BlockTask completeTask(String tenantId, String taskId, String actionType) {
        BlockTask autoTask = tasks.get(new TaskId(taskId));
        autoTask.setModifyTime(System.currentTimeMillis());
        autoTask.setState(TaskState.pass.name());
        return autoTask;
    }
}
