package com.fxiaoke.erpdss.ipaas.all.integration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * iPaaS App All 模块集成测试
 * 测试聚合应用中包含的各个子模块的CheckController接口
 * 
 * <AUTHOR> (^_−)☆
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
class WebBaseCheckControllerIT {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Test
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    /**
     * 测试Base模块的check接口
     */
    @Test
    void testBaseModuleCheckEndpoint() throws Exception {
        if (mockMvc == null) {
            setUp();
        }
        
        mockMvc.perform(get("/base/check"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType("text/plain;charset=UTF-8"))
                .andExpect(content().string("hello,erpdss base!"));
    }

    /**
     * 测试Web模块的check接口
     */
    @Test
    void testWebModuleCheckEndpoint() throws Exception {
        if (mockMvc == null) {
            setUp();
        }
        
        mockMvc.perform(get("/web/check"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType("text/plain;charset=UTF-8"))
                .andExpect(content().string("hello,erpdss web!"));
    }

    /**
     * 测试所有模块的check接口 - 批量测试
     */
    @Test
    void testAllModulesCheckEndpoints() throws Exception {
        if (mockMvc == null) {
            setUp();
        }
        
        // 测试Base模块
        mockMvc.perform(get("/base/check"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().string("hello,erpdss base!"));
        
        // 测试Web模块
        mockMvc.perform(get("/web/check"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().string("hello,erpdss web!"));
    }

    /**
     * 测试模块间的独立性 - 验证各模块返回不同内容
     */
    @Test
    void testModuleIndependence() throws Exception {
        if (mockMvc == null) {
            setUp();
        }
        
        // 获取Base模块响应
        String baseResponse = mockMvc.perform(get("/base/check"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        // 获取Web模块响应
        String webResponse = mockMvc.perform(get("/web/check"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        // 验证响应内容不同
        assert !baseResponse.equals(webResponse);
        assert baseResponse.contains("base");
        assert webResponse.contains("web");
    }

    /**
     * 测试应用健康状态 - 验证所有模块都能正常响应
     */
    @Test
    void testApplicationHealth() throws Exception {
        if (mockMvc == null) {
            setUp();
        }
        
        // 测试Base模块健康状态
        mockMvc.perform(get("/base/check")
                .header("User-Agent", "Health-Check")
                .header("Accept", "text/plain"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(result -> {
                    String content = result.getResponse().getContentAsString();
                    assert content.contains("hello");
                    assert content.contains("base");
                });
        
        // 测试Web模块健康状态
        mockMvc.perform(get("/web/check")
                .header("User-Agent", "Health-Check")
                .header("Accept", "text/plain"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(result -> {
                    String content = result.getResponse().getContentAsString();
                    assert content.contains("hello");
                    assert content.contains("web");
                });
    }

    /**
     * 测试错误场景 - 不存在的路径
     */
    @Test
    void testNonExistentEndpoints() throws Exception {
        if (mockMvc == null) {
            setUp();
        }
        
        // 测试不存在的Base路径
        mockMvc.perform(get("/base/nonexistent"))
                .andDo(print())
                .andExpect(status().isNotFound());
        
        // 测试不存在的Web路径
        mockMvc.perform(get("/web/nonexistent"))
                .andDo(print())
                .andExpect(status().isNotFound());
        
        // 测试完全不存在的模块路径
        mockMvc.perform(get("/unknown/check"))
                .andDo(print())
                .andExpect(status().isNotFound());
    }

    /**
     * 测试HTTP方法错误场景
     */
    @Test
    void testWrongHttpMethods() throws Exception {
        if (mockMvc == null) {
            setUp();
        }
        
        // 测试Base模块POST方法
        mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post("/base/check"))
                .andDo(print())
                .andExpect(status().isMethodNotAllowed());
        
        // 测试Web模块POST方法
        mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post("/web/check"))
                .andDo(print())
                .andExpect(status().isMethodNotAllowed());
    }
}