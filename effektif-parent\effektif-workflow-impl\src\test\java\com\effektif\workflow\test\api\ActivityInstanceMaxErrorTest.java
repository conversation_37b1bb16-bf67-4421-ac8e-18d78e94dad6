package com.effektif.workflow.test.api;

import com.effektif.workflow.api.activities.*;
import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.types.TextType;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflow.MultiInstance;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.effektif.workflow.impl.exceptions.ActivityInstanceBeyondMaxException;
import com.effektif.workflow.impl.util.Lists;
import com.effektif.workflow.test.WorkflowTest;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static com.effektif.workflow.api.ext.WorkflowBindingEnum.maxActivityInstance;
import static org.junit.Assert.assertTrue;


/**
 * <AUTHOR>
 */
public class ActivityInstanceMaxErrorTest extends WorkflowTest {


    /**
     * 设置 maxActivityInstance ,当实例节点超过该值时 结束
     */
    @Test
    public void testBeyondMaxActivityInstance() {
        try {
            // @formatter:off
            ExecutableWorkflow workflow = new ExecutableWorkflow()
                    .activity("start", new StartEvent()
                            .transitionTo("t1"))
                    .activity("t1", new NoneTask().transitionTo("t2"))
                    .activity("t2", new NoneTask().transitionTo("t3"))
                    .activity("t3", new NoneTask()
                            .transitionTo("end"))
                    .activity("end", new EndEvent());
            // @formatter:on

            deploy(workflow);
            TriggerInstance triggerInstance = new TriggerInstance("71557")
                    .workflowId(workflow.getId());
            triggerInstance.transientData(maxActivityInstance.name(), 1);

            workflowEngine.start(triggerInstance);
            assertTrue(false);
        }catch(ActivityInstanceBeyondMaxException e){
            assertTrue(true);
        }
    }
    /**
     * 超过最大的默认限制
     */
    @Test
    public void testBeyondDefaultMaxActivityInstance() {
        try {
            // @formatter:off
            ExecutableWorkflow workflow = new ExecutableWorkflow()
                    .activity("start", new StartEvent()
                            .transitionTo("t1"))
                    .activity("t1", new LoopTask().activity("subProcess",
                            new EmbeddedSubprocess()
                                    .multiInstance(new MultiInstance().valuesExpression("loopVariables").variable("loopVariable",new TextType()))
                                    .activity("sn1", new NoneTask())
                    ).transitionTo("t2"))
                    .activity("t2", new NoneTask()
                            .transitionTo("end"))
                    .activity("end", new EndEvent());
            // @formatter:on

            deploy(workflow);
            List<String> loopVariables=new ArrayList<>();
            for (int i = 0; i < 200; i++) {
                loopVariables.add(String.valueOf(i));
            }
            TriggerInstance triggerInstance = new TriggerInstance("71557")
                    .workflowId(workflow.getId()).data("loopVariables", loopVariables);

            workflowEngine.start(triggerInstance);
            assertTrue(false);
        }catch(ActivityInstanceBeyondMaxException e){
            assertTrue(e.getMax()==199);
            assertTrue(e.getCurrentValue()==200);
        }
    }

    /**
     * one flow 支持 最大 10000 个实例节点
     */
    @Test
    public void testBeyondDefaultMaxActivityInstanceOfOne() {
        try {
            // @formatter:off
            ExecutableWorkflow workflow = new ExecutableWorkflow().type("one")
                    .activity("start", new StartEvent()
                            .transitionTo("t1"))
                    .activity("t1", new LoopTask().activity("subProcess",
                            new EmbeddedSubprocess()
                                    .multiInstance(new MultiInstance().valuesExpression("loopVariables").variable("loopVariable",new TextType()))
                                    .activity("sn1", new NoneTask())
                    ).transitionTo("t2"))
                    .activity("t2", new NoneTask()
                            .transitionTo("end"))
                    .activity("end", new EndEvent());
            // @formatter:on

            deploy(workflow);
            List<String> loopVariables=new ArrayList<>();
            for (int i = 0; i < 20000; i++) {
                loopVariables.add(String.valueOf(i));
            }
            TriggerInstance triggerInstance = new TriggerInstance("71557")
                    .workflowId(workflow.getId()).data("loopVariables", loopVariables);

            workflowEngine.start(triggerInstance);
            assertTrue(false);
        }catch(ActivityInstanceBeyondMaxException e){
            assertTrue(e.getMax()==20000);
            assertTrue(e.getCurrentValue()==20001);
        }
    }
}
