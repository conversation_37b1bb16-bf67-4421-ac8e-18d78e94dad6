package com.effektif.workflow.api.ext;

import java.io.Serializable;
import java.util.List;

/**
 * 后动作变量 赋值时 配置使用
 */
public class AfterActionCustomVariableConfig implements Serializable {

    private List<String> id;
    private String type;
    private Object value;

    public boolean isFixed() {
        return "fixed".equals(type);
    }

    public boolean isFunction() {
        return "function".equals(type);
    }

    public List<String> getId() {
        return id;
    }

    public void setId(List<String> id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }
}
