package com.effektif.workflow.impl.activity.types;

import com.effektif.workflow.api.activities.UserLatencyTask;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.activity.AbstractActivityType;
import com.effektif.workflow.impl.ext.latencyTask.AutoTask;
import com.effektif.workflow.impl.ext.latencyTask.AutoTaskStore;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.VariableInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import com.facishare.paas.workflow.bus.EngineEventBus;
import com.facishare.paas.workflow.bus.model.MQContext;
import com.facishare.paas.workflow.bus.api.CreateTaskEvent;
import com.facishare.paas.workflow.bus.api.type.FlowTag;
import com.facishare.paas.workflow.bus.api.type.TaskState;
import com.facishare.paas.workflow.bus.model.BusConstant;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * liugh
 * 19/2/26 16:00
 */
public class LatencyTaskImpl extends AbstractActivityType<UserLatencyTask> {
  protected AutoTaskStore taskStore;

  public LatencyTaskImpl() {
    super(UserLatencyTask.class);
  }

  @Override
  public void parse(ActivityImpl activityImpl, UserLatencyTask latencyTask, WorkflowParser parser) {
    super.parse(activityImpl, latencyTask, parser);
    this.taskStore = parser.getConfiguration(AutoTaskStore.class);
  }

  @Override
  public void execute(ActivityInstanceImpl activityInstance) {
    TaskId taskId = taskStore.generateTaskId();
    activity.setTaskId(taskId.toString());
    AutoTask autoTask = new AutoTask(taskId, activityInstance.getId()).buildLatencyTask(activity, getExtMap(activityInstance));
    taskStore.insertTask(autoTask);

    try {
      Class<?> bean = Class.forName("com.facishare.paas.workflow.kernel.support.LatencyTaskSupport");
      Method method = bean.getMethod("afterTaskInsert", AutoTask.class);
      method.invoke(bean.newInstance(), autoTask);
      EngineEventBus.post(CreateTaskEvent.create(
              MQContext.create(autoTask.getTenantId(), autoTask.getAppId(),"" ),
              FlowTag.workflow_bpm,
              taskId.toString(),
              TaskState.valueOfSkipNotFound(autoTask.getState()),
              null,
              autoTask.getWorkflowInstanceId(),
              BusConstant.LATENCY_TASK_TYPE,
              autoTask.getEntityId(),
              autoTask.getObjectId(),
              autoTask.getLinkApp())
      );
    } catch (Exception e) {
      throw new RuntimeException("LatencyTaskSupport afterTaskInsert error. ", e);
    }
  }

  private Map<String, String> getExtMap(ActivityInstanceImpl activityInstance) {
    Map<String, String> extMap = new HashMap<>();
    String entityId = null;
    String objectId = null;
    Map<String, Object> extInfoMap = new HashMap<>();
    Map<String, Object> variableMap = new HashMap<>();
    Map<String, VariableInstanceImpl> variableInstancesMap = activityInstance.workflowInstance.variableInstancesMap;
    if (null != variableInstancesMap) {
      variableInstancesMap.keySet().forEach(key -> {
        if (null != variableInstancesMap.get(key) && null != variableInstancesMap.get(key).value) {
          variableMap.put(key, variableInstancesMap.get(key).value);
        }
      });
    }
    String type=activityInstance.getWorkflow().getType();
    extInfoMap.put("type", type);
    if (type.equals(WorkflowConstants.WorkflowType.BPM)) {
      extInfoMap.put("properties", activityInstance.activity.getProperties());
      extInfoMap.put("variables", variableMap);
      extInfoMap.put(WorkflowBindingEnum.appId.toString(), activityInstance.workflowInstance.getAppId());
      try {
        Class<?> bpmServiceExtension = Class.forName("com.facishare.paas.workflow.kernel.support.BPMServiceSupport");
        Method beforeMethod = bpmServiceExtension.getMethod("beforeTaskInsert", Map.class);
        Map<String, Object> beforeResultObject = (Map<String, Object>) beforeMethod.invoke(bpmServiceExtension.newInstance(), extInfoMap);
        entityId = beforeResultObject.get(WorkflowBindingEnum.entityId.toString()).toString();
        objectId = beforeResultObject.get(WorkflowBindingEnum.objectId.toString()).toString();
        variableMap.put("activity_" + activity.getId() + "##" + entityId, objectId);
      } catch (ClassNotFoundException e) {
        log.error("class com.facishare.paas.workflow.kernel.support.BPMServiceSupport not found, error. ", e);
      } catch (NoSuchMethodException e) {
        log.error("no method name beforeTaskInsert, error. ", e);
      } catch (Exception e) {
        log.error("BPMServiceSupport#beforeTaskInsert invoke error. ", e);
      }
    } else {
      entityId = this.getWorkflowInstanceProperty(activityInstance.workflowInstance, WorkflowBindingEnum.entityId.toString());
      objectId = this.getWorkflowInstanceProperty(activityInstance.workflowInstance, WorkflowBindingEnum.objectId.toString());
    }
    extMap.put(WorkflowBindingEnum.entityId.toString(), entityId);
    extMap.put(WorkflowBindingEnum.objectId.toString(), objectId);
    extMap.put(WorkflowBindingEnum.tenantId.toString(), activityInstance.getWorkflow().getTenantId());
    extMap.put(WorkflowBindingEnum.appId.toString(), activityInstance.workflowInstance.getAppId());
    extMap.put("workflowId", activityInstance.workflow.getId().toString());
    extMap.put("workflowInstanceId", activityInstance.workflowInstance.getId().toString());
    extMap.put("sourceWorkflowId", activityInstance.workflow.getSourceWorkflowId());
    return extMap;
  }

  private String getWorkflowInstanceProperty(WorkflowInstanceImpl workflowInstance, String key) {
    if (workflowInstance == null || key == null) {
      return null;
    }
    if (workflowInstance.getProperty(key) != null) {
      return this.getObjectHelper(workflowInstance.getProperty(key));
    }
    return this.getObjectHelper(workflowInstance.getTransientProperty(key));
  }

  private String getObjectHelper(Object obj) {
    String result = null;
    if (obj != null) {
      result = obj.toString();
    }
    return result;
  }
}

