package com.fxiaoke.erpdss.ipaas.springcommon.api;

import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.fxiaoke.erpdss.ipaas.common.module.Result;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR> (^_−)☆
 */
@RequestMapping("api/triggers/")
public interface TriggerRpcApi {
    /**
     * 启动工作流实例
     *
     * @param triggerInstance 触发实例
     * @return 工作流实例
     */
    @RequestMapping("start")
    Result<WorkflowInstance> start(@RequestBody TriggerInstance triggerInstance);
}
