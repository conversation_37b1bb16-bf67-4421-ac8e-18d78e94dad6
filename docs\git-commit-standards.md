# Git 提交规范

## 概述

本文档定义了 iPaaS 项目的 Git 提交消息规范，基于 [Conventional Commits](https://www.conventionalcommits.org/)
标准，确保提交历史清晰、可读且便于自动化处理。

## 提交消息格式

### 基本格式

```
<type>[optional scope]: <description>

[optional body]

[optional footer]
```

### IDE插件

- **IntelliJ IDEA**: AI Commit

### 提示词 Prompt

```markdown
Generate a commit message using the Conventional Commit Convention:

- Summarize changes with specificity
- Optionally include benefits in the body
- Use emojis for expression
- Keep message within 72 characters, break down if needed
- Use {locale} language

Structure:
<type>[optional scope]: <description>

[optional body]

Example:
✨ feat(api): add endpoint for user authentication

Possible scopes:
- base: 基座模块
- web: Web模块
- common: 公共模块
- all: 聚合部署模块
- db: 数据库代理
- connector: 连接器代理
- auth: 认证相关
- api: API接口
- config: 配置

Possible types:
- 🐛 fix: For bug fixes
- ✨ feat: For new features
- 📝 docs: For documentation changes
- 🧹 refactor: For code refactoring without changing functionality
- 🚀 perf: For performance improvements
- 🔒 security: For security-related fixes
- 🚧 chore: For maintenance tasks
- 🧪 test: For test related changes

Diff:
{diff}
```

## 分支命名规范

### 分支类型

- **feature/**: 新功能分支
- **bugfix/**: Bug修复分支
- **hotfix/**: 紧急修复分支
- **release/**: 发布分支
- **docs/**: 文档分支

### 命名格式

```
<type>/<scope>-<description>
```
