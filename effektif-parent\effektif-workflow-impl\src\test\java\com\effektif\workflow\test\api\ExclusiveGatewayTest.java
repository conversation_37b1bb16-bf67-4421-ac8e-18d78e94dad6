/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.test.api;

import com.effektif.workflow.api.activities.*;
import com.effektif.workflow.api.condition.*;
import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.types.BooleanType;
import com.effektif.workflow.api.types.ListType;
import com.effektif.workflow.api.types.NumberType;
import com.effektif.workflow.api.types.TextType;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflow.Transition;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.effektif.workflow.impl.exceptions.ActivityInstanceBeyondMaxException;
import com.effektif.workflow.test.WorkflowTest;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR> Baeyens
 */
public class ExclusiveGatewayTest extends WorkflowTest {

    /**
     * Tests that the process takes the single outgoing flow
     * leaving an exclusive gateway if it has no condition specified.
     * <p>
     * The specification says to throw an exception.  Effektif
     * interprets this by taking the single, non-condition transition
     * if there is one.
     * <pre>
     *
     *  ◯─→<X>─→[t1]─→◯
     *
     * </pre>
     */
    @Test
    public void testSingleOutgoingFlow() {
        // @formatter:off
        ExecutableWorkflow workflow = new ExecutableWorkflow()
            .activity("start", new StartEvent()
                .transitionTo("gateway"))
            .activity("gateway", new ExclusiveGateway().defaultTransitionId("default")
                .transitionTo(new Transition().id("default").toId("userTask").name("transition_name1")))
            .activity("userTask", new UserTask().assignee(new HashMap<>())
                .transitionTo("wait"))
            .activity("wait", new ReceiveTask()
                .transitionTo("end"))
            .activity("end", new EndEvent());
        // @formatter:on

        workflow.setType("approvalflow");
        deploy(workflow);
        WorkflowInstance workflowInstance = start(workflow);
        assertOpen(workflowInstance, "userTask");
    }

    /**
     * Tests that the process continues on an exclusive gateway that only has a default flow.
     * <pre>
     *
     *  ◯─→<X>-/─→[t1]─→◯
     *
     * </pre>
     */
    @Test
    public void testSingleOutgoingFlowDefault() {
        // @formatter:off
        ExecutableWorkflow workflow = new ExecutableWorkflow()
            .activity("start", new StartEvent()
                .transitionTo("gateway"))
            .activity("gateway", new ExclusiveGateway()
                .transitionTo(new Transition().id("default").toId("wait").name("transition_name1"))
                .defaultTransitionId("default"))
            .activity("wait", new ReceiveTask()
                .transitionTo("end"))
            .activity("end", new EndEvent());
        // @formatter:on

        deploy(workflow);
        WorkflowInstance workflowInstance = start(workflow);
        assertOpen(workflowInstance, "wait");
        assertFalse(workflowInstance.isEnded());
    }

    /**
     * Tests that the process continues from an exclusive gateway with a default transition.
     * <pre>
     *
     *  ◯─→<X>──→◯
     *      │
     *      └→[wait]
     * </pre>
     */
    @Test
    public void testDefaultTransition() {
        // @formatter:off
        ExecutableWorkflow workflow = new ExecutableWorkflow()
            .activity("start", new StartEvent()
                .transitionTo("gateway"))
            .activity("gateway", new ExclusiveGateway()
                .defaultTransitionId("wait")
                .transitionTo("end")
                .transitionTo(new Transition().id("wait").toId("receive")))
            .activity("receive", new ReceiveTask())
            .activity("end", new EndEvent());
        // @formatter:on

        deploy(workflow);
        WorkflowInstance workflowInstance = start(workflow);
        assertOpen(workflowInstance, "receive");
    }

    /**
     * Tests an exclusive gateway with a numeric condition.
     * <pre>
     *
     *  ◯─→<X>──→◯
     *      │
     *      └→[wait]
     * </pre>
     */
    @Test
    public void testSimpleCondition() {
        // @formatter:off
        ExecutableWorkflow workflow = new ExecutableWorkflow()
            .variable("waitingRequired", new BooleanType())
            .activity("start", new StartEvent()
                .transitionTo("gateway"))
            .activity("gateway", new ExclusiveGateway()
                .transitionTo(new Transition().id("continue").toId("end")
                    .condition(new IsFalse().leftExpression("waitingRequired")))
                .transitionTo(new Transition().id("wait").toId("receive")
                    .condition(new IsTrue().leftExpression("waitingRequired"))))
            .activity("receive", new ReceiveTask())
            .activity("end", new EndEvent());
        // @formatter:on

        deploy(workflow);

        WorkflowInstance endingWorkflow = workflowEngine.start(
            new TriggerInstance("").workflowId(workflow.getId()).data("waitingRequired", Boolean.FALSE));
        assertTrue(endingWorkflow.isEnded());

        WorkflowInstance waitingWorkflow = workflowEngine.start(
            new TriggerInstance("").workflowId(workflow.getId()).data("waitingRequired", Boolean.TRUE));
        assertOpen(waitingWorkflow, "receive");
    }

    @Test
    public void testExclusiveGateway() {
        ExecutableWorkflow workflow = new ExecutableWorkflow()
            .variable("v", new NumberType())
            .activity("start", new StartEvent()
                .transitionTo("?"))
            .activity("?", new ExclusiveGateway()
                .defaultTransitionId("default"))
            .transition(new Transition().serialNumber(1)
                .condition(new LessThan()
                    .leftExpression("v")
                    .rightValue(10.5))
                .fromId("?").toId("t1"))
            .transition(new Transition().serialNumber(2)
                .condition(new LessThan()
                    .leftExpression("v")
                    .rightValue(100.5))
                .fromId("?").toId("t2"))
            .transition(new Transition().serialNumber(3)
                .id("default")
                .fromId("?").toId("t3"))
            .activity("t1", new ReceiveTask())
            .activity("t2", new ReceiveTask())
            .activity("t3", new ReceiveTask());

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("v", 5));

        assertOpen(workflowInstance, "t1");

        workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("v", 70));

        assertOpen(workflowInstance, "t2");

        workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("v", 500));

        assertOpen(workflowInstance, "t3");
    }

    @Test
    public void testInCondition() {
        List<String> firstList = new ArrayList<>();
        firstList.add("a");
        firstList.add("b");
        firstList.add("c");
        firstList.add("d");
        List<String> secondList = new ArrayList<>();
        secondList.add("d");
        secondList.add("e");
        secondList.add("f");
        ExecutableWorkflow workflow = new ExecutableWorkflow()
            .variable("someone", new TextType())
            .activity("start", new StartEvent()
                .transitionTo("g1"))
            .activity("g1", new ExclusiveGateway()
                .defaultTransitionId("default"))
            .transition(new Transition().serialNumber(3)
                .condition(new In()
                    .leftExpression("someone")
                    .rightValue(firstList))
                .fromId("g1").toId("t1"))
            .transition(new Transition().serialNumber(1)
                .condition(new In()
                    .leftExpression("someone")
                    .rightValue(secondList))
                .fromId("g1").toId("t2"))
            .transition(new Transition().serialNumber(2)
                .condition(new In()
                    .leftExpression("someone")
                    .rightValue(secondList))
                .fromId("g1").toId("t3"))
            .transition(new Transition().serialNumber(3)
                .condition(new In()
                    .leftExpression("someone")
                    .rightValue(secondList))
                .fromId("g1").toId("t4"))
            .transition(new Transition().serialNumber(9)
                .condition(new In()
                    .leftExpression("someone")
                    .rightValue(secondList))
                .id("default")
                .fromId("g1").toId("t5"))
            .activity("t1", new ReceiveTask())
            .activity("t2", new ReceiveTask())
            .activity("t3", new ReceiveTask())
            .activity("t4", new ReceiveTask())
            .activity("t5", new ReceiveTask());

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", "d"));

        assertOpen(workflowInstance, "t2");

        //        workflowInstance = workflowEngine.start(new TriggerInstance("")
        //            .workflowId(workflow.getId())
        //            .data("someone", "d"));
        //
        //        assertOpen(workflowInstance, "t2");
        //
        //        workflowInstance = workflowEngine.start(new TriggerInstance("")
        //            .workflowId(workflow.getId())
        //            .data("someone", "d"));
        //
        //        assertOpen(workflowInstance, "t3");
    }

    @Test
    public void testNotInCondition() {
        List<String> firstList = new ArrayList<>();
        firstList.add("a");
        firstList.add("b");
        firstList.add("c");
        List<String> secondList = new ArrayList<>();
        secondList.add("c");
        secondList.add("d");
        secondList.add("e");
        ExecutableWorkflow workflow = new ExecutableWorkflow()
            .variable("someone", new TextType())
            .activity("start", new StartEvent()
                .transitionTo("g1"))
            .activity("g1", new ExclusiveGateway()
                .defaultTransitionId("default"))
            .transition(new Transition()
                .condition(new NotIn()
                    .leftExpression("someone")
                    .rightValue(firstList))
                .fromId("g1").toId("t1"))
            .transition(new Transition()
                .condition(new NotIn()
                    .leftExpression("someone")
                    .rightValue(secondList))
                .fromId("g1").toId("t2"))
            .transition(new Transition()
                .id("default")
                .fromId("g1").toId("t3"))
            .activity("t1", new ReceiveTask())
            .activity("t2", new ReceiveTask())
            .activity("t3", new ReceiveTask());

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", "d"));

        assertOpen(workflowInstance, "t1");

        workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", "a"));

        assertOpen(workflowInstance, "t2");

        workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", "c"));

        assertOpen(workflowInstance, "t3");
    }

    @Test
    public void testHasAnyOfCondition() {
        List<String> firstList = new ArrayList<>();
        firstList.add("a");
        firstList.add("b");
        firstList.add("c");
        List<String> secondList = new ArrayList<>();
        secondList.add("d");
        secondList.add("e");
        secondList.add("f");
        ExecutableWorkflow workflow = new ExecutableWorkflow()
            .variable("someone", new ListType())
            .activity("start", new StartEvent()
                .transitionTo("g1"))
            .activity("g1", new ExclusiveGateway()
                .defaultTransitionId("default"))
            .transition(new Transition()
                .condition(new HasAnyOf()
                    .leftExpression("someone")
                    .rightValue(firstList))
                .fromId("g1").toId("t1"))
            .transition(new Transition()
                .condition(new HasAnyOf()
                    .leftExpression("someone")
                    .rightValue(secondList))
                .fromId("g1").toId("t2"))
            .transition(new Transition()
                .id("default")
                .fromId("g1").toId("t3"))
            .activity("t1", new ReceiveTask())
            .activity("t2", new ReceiveTask())
            .activity("t3", new ReceiveTask());

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", Arrays.asList(new String[] {"a", "a1", "a2"})));

        assertOpen(workflowInstance, "t1");

        workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", Arrays.asList(new String[] {"f", "f1", "f2"})));

        assertOpen(workflowInstance, "t2");

        workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", Arrays.asList(new String[] {"g"})));

        assertOpen(workflowInstance, "t3");
    }

    @Test
    public void testHasNoneOfCondition() {
        List<String> firstList = new ArrayList<>();
        firstList.add("a");
        firstList.add("b");
        firstList.add("c");
        List<String> secondList = new ArrayList<>();
        secondList.add("d");
        secondList.add("e");
        secondList.add("f");
        ExecutableWorkflow workflow = new ExecutableWorkflow()
            .variable("someone", new ListType())
            .activity("start", new StartEvent()
                .transitionTo("g1"))
            .activity("g1", new ExclusiveGateway()
                .defaultTransitionId("default"))
            .transition(new Transition()
                .condition(new HasNoneOf()
                    .leftExpression("someone")
                    .rightValue(firstList))
                .fromId("g1").toId("t1"))
            .transition(new Transition()
                .condition(new HasNoneOf()
                    .leftExpression("someone")
                    .rightValue(secondList))
                .fromId("g1").toId("t2"))
            .transition(new Transition()
                .id("default")
                .fromId("g1").toId("t3"))
            .activity("t1", new ReceiveTask())
            .activity("t2", new ReceiveTask())
            .activity("t3", new ReceiveTask());

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", Arrays.asList(new String[] {"d", "a1", "a2"})));

        assertOpen(workflowInstance, "t1");

        workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", Arrays.asList(new String[] {"a", "f1", "f2"})));

        assertOpen(workflowInstance, "t2");

        workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", Arrays.asList(new String[] {"a", "d"})));

        assertOpen(workflowInstance, "t3");
    }

    @Test
    public void testHasAllOfCondition() {
        List<String> firstList = new ArrayList<>();
        firstList.add("a");
        firstList.add("b");
        firstList.add("c");
        List<String> secondList = new ArrayList<>();
        secondList.add("d");
        secondList.add("e");
        secondList.add("f");
        ExecutableWorkflow workflow = new ExecutableWorkflow()
            .variable("someone", new ListType())
            .activity("start", new StartEvent()
                .transitionTo("g1"))
            .activity("g1", new ExclusiveGateway()
                .defaultTransitionId("default"))
            .transition(new Transition()
                .condition(new HasAllOf()
                    .leftExpression("someone")
                    .rightValue(firstList))
                .fromId("g1").toId("t1"))
            .transition(new Transition()
                .condition(new HasAllOf()
                    .leftExpression("someone")
                    .rightValue(secondList))
                .fromId("g1").toId("t2"))
            .transition(new Transition()
                .id("default")
                .fromId("g1").toId("t3"))
            .activity("t1", new ReceiveTask())
            .activity("t2", new ReceiveTask())
            .activity("t3", new ReceiveTask());

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", Arrays.asList(new String[] {"a", "b"})));

        assertOpen(workflowInstance, "t1");

        workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", Arrays.asList(new String[] {"d", "f"})));

        assertOpen(workflowInstance, "t2");

        workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("someone", Arrays.asList(new String[] {"a", "d"})));

        assertOpen(workflowInstance, "t3");
    }

    @Test(expected = ActivityInstanceBeyondMaxException.class)
    public void testLoop() {
        ExecutableWorkflow workflow = new ExecutableWorkflow()
            .variable("v", new NumberType())
            .activity("start", new StartEvent()
                .transitionTo("none"))
            .activity("none", new NoneTask().transitionTo("?"))
            .activity("?", new ExclusiveGateway()
                .defaultTransitionId("default"))
            .transition(new Transition().serialNumber(1)
                .condition(new LessThan()
                    .leftExpression("v")
                    .rightValue(10.5))
                .fromId("?").toId("none"))
            .transition(new Transition().serialNumber(2)
                .condition(new LessThan()
                    .leftExpression("v")
                    .rightValue(100.5))
                .fromId("?").toId("t2"))
            .transition(new Transition().serialNumber(3)
                .id("default")
                .fromId("?").toId("t3"))
            .activity("t1", new ReceiveTask())
            .activity("t2", new ReceiveTask())
            .activity("t3", new ReceiveTask());

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("")
            .workflowId(workflow.getId())
            .data("v", 5));

        System.out.println("============================= ok");
    }

}
