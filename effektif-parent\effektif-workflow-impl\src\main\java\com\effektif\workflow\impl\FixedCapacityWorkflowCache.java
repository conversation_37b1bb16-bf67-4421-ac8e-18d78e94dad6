package com.effektif.workflow.impl;

import com.effektif.workflow.api.model.WorkflowId;
import com.effektif.workflow.impl.workflow.WorkflowImpl;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: gaozl
 * Date: 16-11-14
 * Time: 上午10:56
 * To change this template use File | Settings | File Templates.
 */
public class FixedCapacityWorkflowCache implements WorkflowCache {

    protected Map<WorkflowId, WorkflowImpl> workflows = Collections.synchronizedMap(new LRUHashMap<>());

    @Override
    public WorkflowImpl get(WorkflowId workflowId) {
        return workflows.get(workflowId);
    }

    @Override
    public void put(WorkflowImpl workflow) {
        workflows.put(workflow.id, workflow);
    }

    @Override
    public Map<WorkflowId, WorkflowImpl> getWorkflows() {
        return workflows;
    }

    @Override
    public void clear() {
        workflows.clear();
    }
}


class LRUHashMap<K, V> extends LinkedHashMap<K, V> {

    private int capacity = 500;

    @Override
    protected boolean removeEldestEntry(Map.Entry<K, V> eldest) {
        return size() > capacity;
    }

}
