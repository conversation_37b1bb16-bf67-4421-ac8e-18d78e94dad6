package com.fxiaoke.erpdss.ipaas.springcommon.util;

import com.github.trace.TraceContext;
import lombok.experimental.UtilityClass;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR> (^_−)☆
 */
@UtilityClass
public class TraceUtil {
    public @Nullable String getLocale() {
        return TraceContext.get().getLocale();
    }

    public @Nullable String getTraceId(){
        return TraceContext.get().getTraceId();
    }
}
