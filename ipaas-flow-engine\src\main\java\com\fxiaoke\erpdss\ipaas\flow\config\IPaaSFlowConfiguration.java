package com.fxiaoke.erpdss.ipaas.flow.config;

import com.effektif.mongo.MongoConfiguration;
import com.effektif.workflow.api.WorkflowEngine;
import com.effektif.workflow.impl.WorkflowEngineImpl;
import com.fxiaoke.erpdss.ipaas.IPaaS;
import com.github.mongo.support.DatastoreExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * MongoDB配置
 *
 * <AUTHOR> (^_−)☆
 */
@Configuration
@ComponentScan(basePackageClasses = IPaaS.class)
public class IPaaSFlowConfiguration extends MongoConfiguration {

    private DatastoreExt mongoDatastore;

    /**
     * 在db-proxy加载
     */
    @Autowired
    public void setDatastoreExt(DatastoreExt mongoDatastore) {
        this.mongoDatastore = mongoDatastore;
    }


    @PostConstruct
    private void init() {
        dataStore(mongoDatastore);
    }

    @Bean
    public WorkflowEngine workflowEngine() {
        WorkflowEngineImpl engine = (WorkflowEngineImpl) this.getWorkflowEngine();
        return engine;
    }
}
