package com.effektif.workflow.test.api;

import com.effektif.workflow.api.activities.UserTask;
import com.effektif.workflow.api.types.TextType;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.impl.ext.Task;
import com.effektif.workflow.test.WorkflowTest;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;


public class UserTaskTest extends WorkflowTest {

    @Test
    public void testTaskRole() throws Exception {
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .type("approvalflow")
                    .variable("manager", new TextType())
                    .activity("1", new UserTask()
                        .assignee(new HashMap(){
                            {put("personId", Lists.newArrayList("manager"));}
                        })
                        .transitionToNext())
                    .activity("2", new UserTask()
                        .assignee(new HashMap(){
                            {put("personId", Lists.newArrayList("manager"));}
                        }));

                deploy(workflow);

                start(workflow);

                Task task = taskFactory.findTasks(null,null).get(0);

                String taskId = task.getId().getInternal();
                taskFactory.assignTask(null,taskId, Lists.newArrayList("joesmoe"));
                taskFactory.completeTask(null,taskId);

                task = taskFactory.findTasks(null,null).stream().filter(item ->!Boolean.TRUE.equals(item.getCompleted())).findFirst().get();
                taskFactory.assignTask(null,task.getId().getInternal(), Lists.newArrayList("joesmoe"));
                task=taskFactory.findTasks(null,null).stream().filter(item ->!Boolean.TRUE.equals(item.getCompleted())).findFirst().get();
                assertEquals("2", task.getActivityId());
                assertEquals(Lists.newArrayList("joesmoe"), task.getAssigneeIds());
    }

    @Test
    public void testTaskQuery() throws Exception {
        String tenantId = "";
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("1", new UserTask())
                .activity("2", new UserTask())
                .activity("3", new UserTask());

        workflow.type("approvalflow");
        deploy(workflow);
        start(workflow);

        List<Task> tasks = taskFactory.findTasks(tenantId, null);
        Assert.assertFalse(tasks.isEmpty());
        Task task1 = tasks.get(0);
        Task task = taskFactory.completeTask(tenantId, task1.getId().getInternal());
        assertEquals(task.getActivityId(),"1");
        assertEquals(task.getState(),"in_progress");
    }

}
