/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Main workflow engine API for
 * {@link com.effektif.workflow.api.Configuration} and
 * {@link com.effektif.workflow.api.WorkflowEngine} - see
 * <a href="https://github.com/effektif/effektif/wiki/Getting-started">Getting started</a> and
 * <a href="https://github.com/effektif/effektif/wiki/Workflow-engine-types">Workflow engine types</a>.
 */
package com.effektif.workflow.api;