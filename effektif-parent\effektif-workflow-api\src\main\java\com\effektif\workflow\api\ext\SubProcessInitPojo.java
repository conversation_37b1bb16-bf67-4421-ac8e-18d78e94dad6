package com.effektif.workflow.api.ext;


import java.io.Serializable;
import java.util.Map;

/**
 * 子流程启动时 需要获取主流程上的信息,反射从fs-paas-workflow中获取相关信息
 */

public class SubProcessInitPojo implements Serializable {

    private Map<String,Object> transientData;
    private Map<String,Object> data;

    public SubProcessInitPojo(Map<String, Object> transientData, Map<String, Object> data) {
        this.transientData = transientData;
        this.data = data;
    }

    public SubProcessInitPojo() {
    }

    public Map<String, Object> getTransientData() {
        return transientData;
    }

    public void setTransientData(Map<String, Object> transientData) {
        this.transientData = transientData;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }
}
