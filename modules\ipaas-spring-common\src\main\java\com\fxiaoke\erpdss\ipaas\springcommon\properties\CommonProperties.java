package com.fxiaoke.erpdss.ipaas.springcommon.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 公共配置
 * <AUTHOR> (^_−)☆
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "common")
public class CommonProperties {
    /**
     * 检查接口的欢迎语
     */
    private String welcomeMessage;
}
