package com.example.service;

import org.springframework.stereotype.Service;

/**
 * 外部Service类
 * <p>
 * 不在 com.fxiaoke.erpdss.ipaas 包下，不应该被切面拦截
 * 
 * <AUTHOR> (^_−)☆
 */
@Service
public class ExternalService {

    /**
     * 正常执行的方法（不应该被切面拦截）
     */
    public String normalMethod(String input) {
        return "外部服务处理结果: " + input;
    }

    /**
     * 抛出异常的方法（不应该被切面拦截）
     */
    public String exceptionMethod(String input) {
        throw new RuntimeException("外部服务异常: " + input);
    }
}
