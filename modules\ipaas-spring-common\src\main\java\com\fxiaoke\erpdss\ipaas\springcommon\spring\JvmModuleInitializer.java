package com.fxiaoke.erpdss.ipaas.springcommon.spring;

import cn.hutool.core.util.VersionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;

/**
 * <AUTHOR> (^_−)☆
 */
@Slf4j
public class JvmModuleInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        // 检查Java版本
        String javaVersion = System.getProperty("java.version");
        if (VersionUtil.isGreaterThanOrEqual(javaVersion, "9")) {
            // Java 9+
            tryAddOpens();
        }
    }

    private void tryAddOpens() {
        try {
            // 使用反射尝试开放模块访问
            Class<?> moduleClass = Class.forName("java.lang.Module");
            java.lang.reflect.Method addOpensMethod = moduleClass.getDeclaredMethod("addOpens", String.class, moduleClass);

            Object baseModule = String.class.getModule();
            Object unnamedModule = this.getClass().getClassLoader().getUnnamedModule();

            // 开放java.net包
            addOpensMethod.invoke(baseModule, "java.net", unnamedModule);
            addOpensMethod.invoke(baseModule, "java.lang", unnamedModule);
            addOpensMethod.invoke(baseModule, "java.util", unnamedModule);

        } catch (Exception e) {
            // 静默失败，JVM参数未正确设置时的后备方案
            log.info("Warning: Could not add opens via reflection: " + e.getMessage());
        }
    }
}