package com.effektif.workflow.impl.memory;

import com.effektif.workflow.api.model.Id;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.ext.latencyTask.AutoTask;
import com.effektif.workflow.impl.ext.latencyTask.AutoTaskStore;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;


public class MemoryAutoTaskStore implements AutoTaskStore {
    protected int nextId = 1;
    protected Map<Id, AutoTask> tasks = Collections.synchronizedMap(new LinkedHashMap<Id, AutoTask>());

    @Override
    public TaskId generateTaskId() {
        return new TaskId(Integer.toString(nextId++));
    }

    @Override
    public void insertTask(AutoTask task) {
        tasks.put(task.getId(), task);

        if (task.getId() == null) {
            String taskId = Integer.toString(nextId++);
            task.setId(new TaskId(taskId));
        }
        task.setModifyTime(System.currentTimeMillis());
        tasks.put(task.getId(), task);
    }

    @Override
    public void insertWorkflowTask(AutoTask task) {
        insertTask(task);
    }

    /**
     * todo TaskFields.COMPLETED 字段不存在,但是设置了这个值
     */
    @Override
    public AutoTask completeTask(String tenantId, String taskId, String actionType) {
        AutoTask autoTask = tasks.get(new TaskId(taskId));
        autoTask.setModifyTime(System.currentTimeMillis());
        autoTask.setActionType(actionType);
        return autoTask;
    }
}
