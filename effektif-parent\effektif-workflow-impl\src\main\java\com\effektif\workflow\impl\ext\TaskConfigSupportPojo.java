package com.effektif.workflow.impl.ext;

import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * @Description :
 * <AUTHOR> cuiyongxu
 * @Date : 2022/5/9-3:30 PM
 **/
public interface TaskConfigSupportPojo {


    enum TypeSupport {

        parse_remind_latency("解析允许停留时长");

        String desc;

        TypeSupport(String desc) {
        }
    }

    @Data
    class BpmEntityIdAndObjectId {
        private String entityId;
        private String objectId;
        private Map<String,Object> bpmExtension;

        public static BpmEntityIdAndObjectId of(String entityId, String objectId, Map<String,Object> bpmExtension){
            BpmEntityIdAndObjectId bpmDatBean = new BpmEntityIdAndObjectId();
            bpmDatBean.setEntityId(entityId);
            bpmDatBean.setObjectId(objectId);
            bpmDatBean.setBpmExtension(bpmExtension);
            return bpmDatBean;

        }
    }

    @Data
    class RemindLatencySupport extends BasicSupport {
        private String activityId;
        private String entityId;
        private String objectId;
        private Boolean remind;
        private Object remindLatency;
        private String latencyUnit;
        private Map<String, Object> variables;

        public String getLatencyUnit() {
            return latencyUnit;
        }

        public Integer getLatencyUnitInterger() {
            if (StringUtils.isNotEmpty(latencyUnit)) {
                return Integer.parseInt(latencyUnit);
            }
            return null;
        }

        public static RemindLatencySupport of(String tenantId, String appId, String type, String activityId, String entityId, String objectId, Map<String, Object> variable,
                                              Boolean remind,Object remindLatency,Object latencyUnit) {
            TaskConfigSupportPojo.RemindLatencySupport remindLatencySupport = new TaskConfigSupportPojo.RemindLatencySupport();
            remindLatencySupport.setVariables(variable);
            remindLatencySupport.setAppId(appId);
            remindLatencySupport.setTenantId(tenantId);
            remindLatencySupport.setActivityId(activityId);
            remindLatencySupport.setType(type);
            remindLatencySupport.setRemind(remind);
            remindLatencySupport.setRemindLatency(remindLatency);
            if(Objects.isNull(latencyUnit)){
                latencyUnit = 2;//小时
            }
            //UserTask latencyUnit 是 Interger ;ExecutionTask 是 String ,解析停留时间共用统一个 model 故此处做了转换
            if(latencyUnit instanceof Integer){
                remindLatencySupport.setLatencyUnit(latencyUnit.toString());
            }else{
                remindLatencySupport.setLatencyUnit((String) latencyUnit);
            }

            //业务流不能直接使用,仅限审批,阶段推进器使用(单一对象)
            remindLatencySupport.setEntityId(entityId);
            remindLatencySupport.setObjectId(objectId);

            return remindLatencySupport;
        }

        public Boolean getRemind() {
            return remind != null && remind;
        }


        public Map<String, Object> getVariables() {
            return Objects.isNull(variables) ? Maps.newHashMap() : variables;
        }

        @Override
        public TypeSupport supportType() {
            return TypeSupport.parse_remind_latency;
        }
    }

    @Data
    abstract class BasicSupport {
        private String tenantId;
        private String appId;
        private String userId;
        private String type;
        /**
         * 延时执行需要抛出异常,而 userTaskImpl 如果异常 这返回0
         */
        private boolean isDelayTask;

        public abstract TypeSupport supportType();
    }

}
