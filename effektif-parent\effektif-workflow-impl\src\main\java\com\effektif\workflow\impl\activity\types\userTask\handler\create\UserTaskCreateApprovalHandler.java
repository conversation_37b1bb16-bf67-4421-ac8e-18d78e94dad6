package com.effektif.workflow.impl.activity.types.userTask.handler.create;

import com.effektif.workflow.api.activities.ParallelGateway;
import com.effektif.workflow.api.activities.UserTask;
import com.effektif.workflow.api.ext.ExecutionPojo;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.impl.activity.types.UserTaskImpl;
import com.effektif.workflow.impl.ext.Task;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflow.WorkflowImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.effektif.workflow.api.ext.WorkflowConstants.AssigneeType.GROUP_HANDLER;

/**
 * <AUTHOR>
 * @date 2023/10/1
 * @apiNote
 **/
public class UserTaskCreateApprovalHandler extends UserTaskCreateHandler{
    public static volatile String APPROVAL_FLOW_SERVICE_SUPPORT_CLASS_NAME = "com.facishare.paas.workflow.kernel.support.ApprovalFlowServiceSupport";
    @Override
    @SneakyThrows
    public Task generateTask(UserTaskImpl userTaskImpl, ActivityInstanceImpl activityInstance, Task task) {
        WorkflowInstanceImpl workflowInstance = activityInstance.workflowInstance;
        UserTask activity = userTaskImpl.getActivity();
        Map<String, Object> extension;
        if (activity.getProperties() != null && Objects.nonNull(extension = (Map<String, Object>) activity.getProperties().get("extension"))) {
            task.setExtension(extension);
        }

        if (task.inParallelGateWay()) {
            parseParallelProperty(activityInstance, task);
        }
        //对task后动作进行处理  --  pass有后动作存在，reject后动作不存在。但reject在库中为 reject[] 时. 获取task，会把paas的值复制到reject中。 导致reject时会执行pass的后动作
        Map<String, List<ExecutionPojo>> executions = task.getExecution();
        if (executions != null && !executions.isEmpty()) {
            task.setExecution(executions.keySet().stream()
                    .filter(key -> executions.get(key) != null && executions.get(key).size() > 0)
                    .collect(Collectors.toMap(key -> key, executions::get)));
        }
        task.property(GROUP_HANDLER, activity.getGroupHandler());


        //判断是否空，兼容6.2版本正在执行的流程
        String version = getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.version.toString());
        if (StringUtils.isNotBlank(version)) {
            task.setVersion(Integer.valueOf(version));
        }
        task.setEnableMoveToCurrentActivityWhenReject((Boolean) activity.
                getProperty(WorkflowBindingEnum.enableMoveToCurrentActivityWhenReject.toString()));
        task.setCandidateEditable(activity.getCandidateEditable());
        task.setDemandSuperior(activity.getDemandSuperior());
        task.setDemandBeyondAssignee(activity.getDemandBeyondAssignee());
        task.setBeyondAssignee(activity.getBeyondAssignee());
        task.setSequence((Boolean) activity.getProperty(WorkflowBindingEnum.sequence.toString()));
        task.setIsAutoAgree(activity.getAutoAgree());
        task.setAutoAgreeWhenEqualsWithApplicant(activity.getAutoAgreeWhenEqualsWithApplicant());
        task.setAutoAgreeWhenHistorySame(activity.getAutoAgreeWhenHistorySame());
        //任务审核通过之后需要进入的activityId
        Map<String, Object> workflowTransientProperties = workflowInstance.getTransientProperties();
        if (Objects.nonNull(workflowTransientProperties)) {
            task.setMoveToActivityIdWhenComplete((String) workflowTransientProperties
                    .get(WorkflowBindingEnum.moveToActivityIdWhenComplete.name()));
            task.setParallelRejectedToCurrentNodeTaskId((String) workflowTransientProperties
                    .get(WorkflowBindingEnum.parallelRejectedToCurrentNodeTaskId.name()));
        }
        parseReminders(task, activity,null);
        userTaskImpl.insertTask(task);

        Class<?> approvalFlowServiceSupport = Class.forName(APPROVAL_FLOW_SERVICE_SUPPORT_CLASS_NAME);
        Method method = approvalFlowServiceSupport.getMethod("afterTaskInsert", String.class, String.class);
        method.invoke(approvalFlowServiceSupport.newInstance(), task.getTenantId(), task.getId().getInternal());

        return task;
    }

    /**
     * 设置并行网关属性
     */
    private static void parseParallelProperty(ActivityInstanceImpl activityInstance, Task task) {
        WorkflowImpl workflow = activityInstance.workflow;
        ActivityImpl parallelGateActivity = workflow.getActivityByIdLocal(task.getParallelGateWayId());
        ParallelGateway parallelGateway = (ParallelGateway) parallelGateActivity.activity;
        task.setParallelRejectedEndedThenResubmitAndReturn(parallelGateway.getParallelRejectedEndedThenResubmitAndReturn());
        task.setParallelRejectedNonGatewayAndReturn(parallelGateway.getParallelRejectedNonGatewayAndReturn());
    }

    @Override
    public String getType(){
        return WorkflowConstants.WorkflowType.APPROVAL_FLOW;
    }
}
