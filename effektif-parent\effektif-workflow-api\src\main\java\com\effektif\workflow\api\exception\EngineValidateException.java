package com.effektif.workflow.api.exception;

/**
 * <AUTHOR>
 * @date 8/12/24
 * @apiNote 引擎定义check exception
 **/
public class EngineValidateException extends EngineException{
    public EngineValidateException(String message) {
        super(message);
    }

    public EngineValidateException(String message, Throwable cause) {
        super(message, cause);
    }

    public EngineValidateException(Throwable cause) {
        super(cause);
    }
}
