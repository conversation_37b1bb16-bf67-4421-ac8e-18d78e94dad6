package com.effektif.workflow.api.exception;

/**
 * <AUTHOR>
 * @creat_date: 2021/9/22
 * @creat_time: 15:17
 * @since 7.5.0
 */
public class EngineBranchFetchSubDeptException extends EngineException{
  public EngineBranchFetchSubDeptException() {
  }

  public EngineBranchFetchSubDeptException(String message) {
    super(message);
  }

  public EngineBranchFetchSubDeptException(String message, Throwable cause) {
    super(message, cause);
  }
  public EngineBranchFetchSubDeptException(Throwable cause) {
    super(cause);
  }
}
