package com.effektif.workflow.test.api;

import com.effektif.workflow.api.activities.*;
import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.types.TextType;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflow.MultiInstance;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.effektif.workflow.impl.util.Lists;
import com.effektif.workflow.test.WorkflowTest;
import org.junit.Test;

import static org.junit.Assert.assertTrue;


/**
 * <AUTHOR>
 */
public class LoopTaskTest extends WorkflowTest {


    /**
     * Tests a Loop contain subprocess.
     * <pre>
     *
     *           ┌─────────────────────────────────────┐
     *           │ loop multiInstance :subprocess1     │
     *  [start]──┤                                     ├─→[t3]─>   [end]
     *           │ [s1]                                │
     *           └─────────────────────────────────────┘
     *
     * </pre>
     */
    @Test
    public void testNestedSubprocess() {
        // @formatter:off
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("start", new StartEvent()
                        .transitionTo("loopTask"))
                .activity("loopTask", new LoopTask()
                        .activity("subProcess", new EmbeddedSubprocess()
                                .multiInstance(new MultiInstance()
                                        .valuesExpression("reviewers")
                                        .variable("reviewer", TextType.INSTANCE))
                                .activity("reviewer", msgExpression("reviewer").transitionTo("s1"))
                                .activity("s1", new ReceiveTask())
                        )
                        .transitionTo("t3")
                )
                .activity("t3", new ReceiveTask()
                        .transitionTo("end"))
                .activity("end", new EndEvent());
        // @formatter:on

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("71557")
                .workflowId(workflow.getId())
                .data("reviewers", Lists.of("jack", "tom")).data("a", "b"));

        assertOpen(workflowInstance, "loopTask", "subProcess", "subProcess", "subProcess","s1","s1");

        workflowInstance = endTask(workflowInstance, "s1");
        workflowInstance = endTask(workflowInstance, "s1");
        assertOpen(workflowInstance, "t3");
        workflowInstance = endTask(workflowInstance, "t3");
        assertTrue(workflowInstance.isEnded());
    }


    /**
     * 无子流程
     * <pre>
     *
     *           ┌───────────────────────┐
     *           │ loop multiInstance :subprocess1   │
     *  [start]──┤                       ├─→[t3]─>[end]
     *           │ []                  │
     *           └───────────────────────┘
     *
     * </pre>
     */
    @Test
    public void testEmptyLoopTaskSubprocess() {
        // @formatter:off
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("start", new StartEvent()
                        .transitionTo("loopTask"))
                .activity("loopTask", new LoopTask()
                        .transitionTo("t3")
                )
                .activity("t3", new ReceiveTask()
                        .transitionTo("end"))
                .activity("end", new EndEvent());
        // @formatter:on

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("71557")
                .workflowId(workflow.getId())
                .data("reviewers", Lists.of("jack", "tom")).data("a", "b"));

        assertOpen(workflowInstance, "t3");
        workflowInstance = endTask(workflowInstance, "t3");
        assertTrue(workflowInstance.isEnded());
    }


    /**
     * 子流程有序执行
     * <pre>
     *
     *           ┌───────────────────────────────────┐
     *           │ loop multiInstance :subprocess1   │
     *  [start]──┤                                   ├─→[t3]─>[end]
     *           │ []                                │
     *           └───────────────────────────────────┘
     *
     * </pre>
     */
    @Test
    public void testLoopTaskSubprocessSequence() {
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("start", new StartEvent()
                        .transitionTo("loopTask"))
                .activity("loopTask",
                        new LoopTask()
                                .activity("subprocess", new EmbeddedSubprocess()
                                        .multiInstance(
                                                new MultiInstance()
                                                        .sequential(true)
                                                        .valuesExpression("reviewers")
                                                        .variable("reviewer", TextType.INSTANCE))
                                        .activity("blockTask", new BlockExecutionTask().executeType("auto")))
                                .transitionTo("t3")
                )
                .activity("t3", new ReceiveTask()
                        .transitionTo("end"))
                .activity("end", new EndEvent());

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("71557")
                .workflowId(workflow.getId())
                .data("reviewers", Lists.of("jack", "tom","jack", "tom","jack", "tom","jack", "tom","jack", "tom")).data("a", "b"));

        assertOpen(workflowInstance, "t3");
        workflowInstance = endTask(workflowInstance, "t3");
        assertTrue(workflowInstance.isEnded());
    }
    @Test
    public void testLoopTaskSubprocessSequenceDesc() {
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("start", new StartEvent()
                        .transitionTo("loopTask"))
                .activity("loopTask",
                        new LoopTask()
                                .activity("subprocess", new EmbeddedSubprocess()
                                        .multiInstance(
                                                new MultiInstance()
                                                        .sequential(true).asc(false)
                                                        .valuesExpression("reviewers")
                                                        .variable("reviewer", TextType.INSTANCE))
                                        .activity("blockTask", new BlockExecutionTask().executeType("auto")))
                                .transitionTo("t3")
                )
                .activity("t3", new ReceiveTask()
                        .transitionTo("end"))
                .activity("end", new EndEvent());

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("71557")
                .workflowId(workflow.getId())
                .data("reviewers", Lists.of("jack", "tom","jack", "tom","jack", "tom","jack", "tom","jack", "tom")).data("a", "b"));

        assertOpen(workflowInstance, "t3");
        workflowInstance = endTask(workflowInstance, "t3");
        assertTrue(workflowInstance.isEnded());
    }
    /**
     * loopTask 无子节点
     * <pre>
     *
     *           ┌───────────────────────────────────┐
     *           │ loop multiInstance :subprocess1   │
     *  [start]──┤                                   ├─→[t3]─>[end]
     *           │ []                                │
     *           └───────────────────────────────────┘
     *
     * </pre>
     */
    @Test
    public void testLoopTaskSubprocessNonSequence() {
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("start", new StartEvent()
                        .transitionTo("loopTask"))
                .activity("loopTask",
                        new LoopTask()
                                .activity("subprocess", new EmbeddedSubprocess()
                                        .multiInstance(
                                                new MultiInstance()
                                                        .valuesExpression("reviewers")
                                                        .variable("reviewer", TextType.INSTANCE))
                                        .activity("blockTask", new BlockExecutionTask().executeType("auto")))
                                .transitionTo("t3")
                )
                .activity("t3", new ReceiveTask()
                        .transitionTo("end"))
                .activity("end", new EndEvent());

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("71557")
                .workflowId(workflow.getId())
                .data("reviewers", Lists.of("jack", "tom","jack", "tom","jack", "tom","jack", "tom","jack", "tom")).data("a", "b"));

        assertOpen(workflowInstance, "t3");
        workflowInstance = endTask(workflowInstance, "t3");
        assertTrue(workflowInstance.isEnded());
    }
    /**
     * loopTask 无子节点
     * <pre>
     *
     *           ┌───────────────────────────────────┐
     *           │ loop multiInstance :subprocess1   │
     *  [start]──┤                                   ├─→[t3]─>[end]
     *           │ []                                │
     *           └───────────────────────────────────┘
     *
     * </pre>
     */
    @Test
    public void testLoopTaskSubprocessIsNull() {
        ExecutableWorkflow workflow = new ExecutableWorkflow()
                .activity("start", new StartEvent()
                        .transitionTo("loopTask"))
                .activity("loopTask",
                        new LoopTask().transitionTo("t3")
                )
                .activity("t3", new ReceiveTask()
                        .transitionTo("end"))
                .activity("end", new EndEvent());

        deploy(workflow);

        WorkflowInstance workflowInstance = workflowEngine.start(new TriggerInstance("71557")
                .workflowId(workflow.getId())
                .data("reviewers", Lists.of( "tom")));

        assertOpen(workflowInstance, "t3");
        workflowInstance = endTask(workflowInstance, "t3");
        assertTrue(workflowInstance.isEnded());
    }
}
