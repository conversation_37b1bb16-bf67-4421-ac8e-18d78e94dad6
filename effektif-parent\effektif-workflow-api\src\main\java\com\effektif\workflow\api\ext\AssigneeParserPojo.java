package com.effektif.workflow.api.ext;

import com.effektif.workflow.api.activities.HandlerConfig;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * zhenghaibo
 * 2017/3/1 20:04
 */
public class AssigneeParserPojo implements Serializable {
    private static final long serialVersionUID = -6768431061158331734L;
    private Map<String, List<String>> assigneeMap;
    private List<HandlerConfig> groupHandler;
    /**
     * 函数解析人
     */
    protected Map<String,Object> assigneeFunction;
    protected String assigneeType;
    private String tenantId;
    private String appId;
    private String type;
    private String userId;
    private String taskId;
    private String applicantId;
    private String workflowId;
    private String workflowInstanceId;
    private Map<String, Object> bpmExtension;
    private String activityId;
    private String entityId;
    private String objectId;
    private Map<String, Object> variables;
    private String linkAppId;
    private Integer linkAppType;
    private String sourceWorkflowId;
    private String workflowName;
    private String taskName;
    private String submitter;

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Map<String, List<String>> getAssigneeMap() {
        return assigneeMap;
    }

    public List<HandlerConfig> getGroupHandler() {
        return groupHandler;
    }

    public String getSubmitter() {
        return submitter;
    }

    public void setAssigneeMap(Map<String, List<String>> assigneeMap) {
        this.assigneeMap = assigneeMap;
    }

    public AssigneeParserPojo assigneeMap(Map<String, List<String>> assigneeMap) {
        this.assigneeMap = assigneeMap;
        return this;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public AssigneeParserPojo type(String type){
        this.setType(type);
        return this;
    }
    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public AssigneeParserPojo tenantId(String tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public AssigneeParserPojo appId(String appId) {
        this.appId = appId;
        return this;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public AssigneeParserPojo userId(String userId) {
        this.userId = userId;
        return this;
    }

    public AssigneeParserPojo taskName(String taskName) {
        this.taskName = taskName;
        return this;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public AssigneeParserPojo taskId(String taskId) {
        this.taskId = taskId;
        return this;
    }

    public String getWorkflowInstanceId() {
        return workflowInstanceId;
    }

    public void setWorkflowInstanceId(String workflowInstanceId) {
        this.workflowInstanceId = workflowInstanceId;
    }

    public AssigneeParserPojo workflowInstanceId(String workflowInstanceId) {
        this.workflowInstanceId = workflowInstanceId;
        return this;
    }

    public AssigneeParserPojo groupHandler(List<HandlerConfig> groupHandler) {
        this.groupHandler = groupHandler;
        return this;
    }

    public AssigneeParserPojo submitter(String submitter) {
        this.submitter = submitter;
        return this;
    }

    public AssigneeParserPojo entityId(String entityId) {
        this.entityId = entityId;
        return this;
    }
    public AssigneeParserPojo objectId(String objectId) {
        this.objectId = objectId;
        return this;
    }

    public Map<String, Object> getBpmExtension() {
        return bpmExtension;
    }

    public void setBpmExtension(Map<String, Object> bpmExtension) {
        this.bpmExtension = bpmExtension;
    }

    public AssigneeParserPojo bpmExtension(Map<String, Object> bpmExtension) {
        this.bpmExtension = bpmExtension;
        return this;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public AssigneeParserPojo activityId(String activityId) {
        this.activityId = activityId;
        return this;
    }

    public String getApplicantId() {
        return applicantId;
    }

    public void setApplicantId(String applicantId) {
        this.applicantId = applicantId;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }

    public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public String getLinkAppId() {
        return linkAppId;
    }

    public void setLinkAppId(String linkAppId) {
        this.linkAppId = linkAppId;
    }

    public Integer getLinkAppType() {
        return linkAppType;
    }

    public void setLinkAppType(Integer linkAppType) {
        this.linkAppType = linkAppType;
    }

    public Map<String, Object> getAssigneeFunction() {
        return assigneeFunction;
    }

    public void setAssigneeFunction(Map<String, Object> assigneeFunction) {
        this.assigneeFunction = assigneeFunction;
    }

    public String getSourceWorkflowId() {
        return sourceWorkflowId;
    }

    public void setSourceWorkflowId(String sourceWorkflowId) {
        this.sourceWorkflowId = sourceWorkflowId;
    }

    public String getWorkflowName() {
        return workflowName;
    }

    public void setWorkflowName(String workflowName) {
        this.workflowName = workflowName;
    }

    public String getAssigneeType() {
        return assigneeType;
    }

    public void setAssigneeType(String assigneeType) {
        this.assigneeType = assigneeType;
    }

    public boolean isAssigneeFunction(){
        return WorkflowConstants.AssigneeType.ASSIGNEE_FUNCTION.equals(assigneeType);
    }
}
