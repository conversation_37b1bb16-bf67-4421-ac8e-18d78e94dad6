package com.fxiaoke.erpdss.ipaas.common.i18n;

import cn.hutool.core.util.StrUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.function.BiFunction;

/**
 * 不需要spring管理的util
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@UtilityClass
public class I18nUtil {

    //默认是无实现的，在spring-common模块替换了i18nClient实现
    private BiFunction<String, String, String> i18nFunction = (i18nKey, defaultValue) -> defaultValue;

    synchronized public void _SetI18nFunction(BiFunction<String, String, String> i18nFunction) {
        I18nUtil.i18nFunction = i18nFunction;
    }

    public String get(String i18nKey, String defaultValue, Object... args) {
        String pattern = get2(i18nKey, defaultValue);
        if (args == null) {
            return pattern;
        }
        try {
            return StrUtil.indexedFormat(pattern, args);
        } catch (Exception e) {
            //异常时可能是格式化错误，尝试直接使用默认值进行格式化
            try {
                return StrUtil.indexedFormat(defaultValue, args);
            } catch (Exception ignore) {
                //默认值都失败了，不应该，但也不报错，会直接返回i18nValue
                log.warn("I18nUtil indexedFormat failed,{},{}", i18nKey, defaultValue);
            }
        }
        return pattern;
    }

    private String get2(String i18nKey, String defaultValue) {
        if (StrUtil.isBlank(i18nKey)) {
            return defaultValue;
        }
        try {
            return i18nFunction.apply(i18nKey, defaultValue);
        } catch (Exception e) {
            log.warn("I18nUtil get failed", e);
            return defaultValue;
        }
    }
}
