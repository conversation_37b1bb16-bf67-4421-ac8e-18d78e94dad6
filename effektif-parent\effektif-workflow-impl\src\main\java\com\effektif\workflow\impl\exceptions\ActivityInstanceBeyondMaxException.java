package com.effektif.workflow.impl.exceptions;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/9
 * @apiNote
 **/
@Data
public class ActivityInstanceBeyondMaxException extends RuntimeException{

    private Integer max;
    private Integer currentValue;

    public ActivityInstanceBeyondMaxException(Integer max, Integer currentValue) {
        this.max = max;
        this.currentValue = currentValue;
    }

    @Override
    public String toString() {
        return  "max:" + max + ", currentValue:" + currentValue;
    }
}
