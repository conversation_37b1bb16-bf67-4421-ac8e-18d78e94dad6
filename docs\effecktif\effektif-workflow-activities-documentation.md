# Effektif Workflow Activities 完整文档

本文档详细介绍了 `com.effektif.workflow.api.activities` 包中所有工作流活动类型的功能、用途和配置方式。

## 目录

1. [事件类型](#事件类型)
2. [任务类型](#任务类型)
3. [网关类型](#网关类型)
4. [子流程类型](#子流程类型)
5. [服务任务类型](#服务任务类型)
6. [特殊任务类型](#特殊任务类型)
7. 

---

## 事件类型

### 1. StartEvent (开始事件)

**类名**: `StartEvent`
**BPMN元素**: `startEvent`
**类型名**: `startEvent`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
skinparam circleBackgroundColor #C8E6C9
skinparam circleBorderColor #388E3C
skinparam rectangleBackgroundColor #FFF3E0
skinparam rectangleBorderColor #E65100

circle "开始" as start
rectangle "下一个活动" as next
start --> next : 激活流程
note right of start : 工作流入口点\n可以有多个开始事件
@enduml
```

**功能描述**:

- 激活其出口流，启动工作流程
- 一个流程可以有零个或多个开始事件
- 作为工作流的入口点

**使用示例**:

```java
new StartEvent()
    .id("start")
    .name("流程开始")
    .transitionTo("nextActivity")
```

### 2. EndEvent (结束事件)

**类名**: `EndEvent`
**BPMN元素**: `endEvent`
**类型名**: `endEvent`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
skinparam circleBackgroundColor #FFCDD2
skinparam circleBorderColor #D32F2F
skinparam rectangleBackgroundColor #FFF3E0
skinparam rectangleBorderColor #E65100

rectangle "前一个活动" as prev
circle "结束" as end
prev --> end : 完成流程
note right of end : 工作流出口点\n结束整个流程
@enduml
```

**功能描述**:

- 完成传入流，如果没有其他活动流则结束整个工作流
- 作为工作流的出口点

**使用示例**:

```java
new EndEvent()
    .id("end")
    .name("流程结束")
```

---

## 任务类型

### 3. UserTask (用户任务)

**类名**: `UserTask`
**BPMN元素**: `userTask`
**类型名**: `userTask`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
skinparam actorBackgroundColor #E1F5FE
skinparam actorBorderColor #0277BD
skinparam rectangleBackgroundColor #FFF3E0
skinparam rectangleBorderColor #E65100

rectangle "用户任务" as task
actor "审批人" as user
rectangle "下一步" as next

task --> user : 分配任务
user --> task : 完成审批
task --> next : 继续流程

note top of task : 支持复杂审批配置\n- 审批人设置\n- 会签策略\n- 超时处理\n- 加签转派
@enduml
```

**功能描述**:

- 需要人工干预的任务，等待用户操作
- 支持复杂的审批流程配置
- 包含丰富的审批人配置和规则设置

**核心属性**:

- `assignee`: 审批人配置 (Map<String, List<String>>)
- `assigneeFunction`: 函数解析人配置
- `assignType`: 指派类型
- `taskType`: 审批任务类型
- `dueDate`: 任务截止时间
- `allowEscalate`: 是否允许任务转派
- `tagAllow`: 是否允许加签
- `autoAgreeWhenEqualsWithApplicant`: 与发起人一致时自动通过

**使用示例**:

```java
new UserTask()
    .id("approval")
    .name("审批任务")
    .assignee(assigneeMap)
    .taskType("approval")
    .allowEscalate(true)
```

### 4. ReceiveTask (接收任务)

**类名**: `ReceiveTask`
**BPMN元素**: `receiveTask`
**类型名**: `receiveTask`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
skinparam rectangleBackgroundColor #E0F2F1
skinparam rectangleBorderColor #00695C
skinparam cloudBackgroundColor #E8F5E8
skinparam cloudBorderColor #2E7D32

rectangle "接收任务" as receive
cloud "外部消息" as message
rectangle "下一步" as next

message --> receive : 发送消息
receive --> next : 收到消息后继续

note bottom of receive : 等待外部消息\n保持阻塞状态\n直到收到消息
@enduml
```

**功能描述**:

- 等待接收消息的任务
- 在收到消息之前保持等待状态

**使用示例**:

```java
new ReceiveTask()
    .id("waitForMessage")
    .name("等待消息")
```

### 5. NoneTask (空任务)

**类名**: `NoneTask`
**BPMN元素**: `task`
**类型名**: `task`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
skinparam rectangleBackgroundColor #F5F5F5
skinparam rectangleBorderColor #757575

rectangle "前一步" as prev
rectangle "空任务" as none
rectangle "下一步" as next

prev --> none : 进入
none --> next : 直接通过

note bottom of none : 占位符任务\n不执行任何操作\n直接继续流程
@enduml
```

**功能描述**:

- 不执行任何操作，直接继续执行
- 用作开发过程中的占位符
- 也称为 noop 或 pass-through 任务

**使用示例**:

```java
new NoneTask()
    .id("placeholder")
    .name("占位任务")
```

---

## 网关类型

### 6. ExclusiveGateway (排他网关)

说明：平台仅使用if else，容易理解

**类名**: `ExclusiveGateway`
**BPMN元素**: `exclusiveGateway`
**类型名**: `exclusiveGateway`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black

:前一步;
if (条件判断?) then (条件A满足)
  :路径A;
elseif (条件B满足) then (yes)
  :路径B;
else (默认路径)
  :默认路径;
endif
:继续流程;

note right : 排他网关特点：\n- 条件分支\n- 只选择一条路径\n- 支持默认路径
@enduml
```

**功能描述**:

- 用于条件分支和汇聚
- 基于条件选择唯一的执行路径
- 支持默认路径配置

**核心属性**:

- `gatewayType`: 网关类型

**使用示例**:

```java
new ExclusiveGateway()
    .id("decision")
    .name("条件判断")
    .transitionWithConditionTo(condition, "path1")
    .defaultTransitionId("defaultPath")
```

### 7. ParallelGateway (并行网关)

**类名**: `ParallelGateway`
**BPMN元素**: `parallelGateway`
**类型名**: `parallelGateway`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black

:前一步;
fork
  :任务A;
fork again
  :任务B;
end fork
:下一步;

note right : 并行网关特点：\n- Fork: 创建并行路径\n- Join: 等待所有路径完成\n- 同时执行多个任务
@enduml
```

**功能描述**:

- 用于并行执行任务的分叉和汇聚
- Fork: 创建多个并行执行路径
- Join: 等待所有路径完成后继续

**核心属性**:

- `parallelRejectedEndedThenResubmitAndReturn`: 驳回并结束流程后重新提交跳回原节点
- `parallelRejectedNonGatewayAndReturn`: 驳回至非网关节点后跳回原节点

**使用示例**:

```java
new ParallelGateway()
    .id("fork")
    .name("并行分叉")
    .transitionTo("task1")
    .transitionTo("task2")
```

---

## 子流程类型

### 8. SubProcess (子流程)

**类名**: `SubProcess`
**BPMN元素**: `callActivity`
**类型名**: `subProcess`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
skinparam rectangleBackgroundColor #E3F2FD
skinparam rectangleBorderColor #1976D2
skinparam packageBackgroundColor #FFF8E1
skinparam packageBorderColor #F57C00
skinparam circleBackgroundColor #C8E6C9
skinparam circleBorderColor #388E3C

rectangle "主流程" as main {
    rectangle "前一步" as prev
    rectangle "子流程调用" as call
    rectangle "下一步" as next

    prev --> call : 输入参数
    call --> next : 输出结果
}

package "子工作流" as sub {
    circle "开始" as subStart
    rectangle "子流程逻辑" as subLogic
    circle "结束" as subEnd

    subStart --> subLogic
    subLogic --> subEnd
}

call ..> sub : 调用外部流程
sub ..> call : 返回结果

note right of call : 参数映射\n驳回策略\n等待子流程完成
@enduml
```

**功能描述**:

- 调用另一个工作流并等待其完成
- 支持输入输出参数映射
- 支持复杂的驳回策略配置

**核心属性**:

- `subWorkflowId`: 子工作流ID
- `subWorkflowSourceId`: 子工作流源ID
- `subWorkflowInputs`: 子工作流输入参数映射
- `subProcessRejectStrategy`: 子流程驳回策略
- `moveToCurrentActivityWhenReject`: 驳回时是否移动到当前活动

**驳回策略**:

- `REJECT_TO_BEFORE_TASK`: 驳回到前置任务
- `REJECT_FULL_PROCESS`: 驳回整个流程

**使用示例**:

```java
new SubProcess()
    .id("subProcess")
    .name("子流程")
    .subWorkflowSourceId("subWorkflowId")
    .inputValue("param1", "value1")
    .output("result", "outputVar")
```

### 9. EmbeddedSubprocess (嵌入式子流程)

**类名**: `EmbeddedSubprocess`
**BPMN元素**: `subProcess`
**类型名**: `embeddedSubprocess`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
skinparam rectangleBackgroundColor #E0F2F1
skinparam rectangleBorderColor #00695C
skinparam packageBackgroundColor #E0F2F1
skinparam packageBorderColor #00695C
skinparam circleBackgroundColor #C8E6C9
skinparam circleBorderColor #388E3C

rectangle "主流程" as main {
    rectangle "前一步" as prev

    package "嵌入式子流程" as embedded {
        circle "子开始" as subStart
        rectangle "子任务1" as subTask1
        rectangle "子任务2" as subTask2
        circle "子结束" as subEnd

        subStart --> subTask1
        subTask1 --> subTask2
        subTask2 --> subEnd
    }

    rectangle "下一步" as next

    prev --> embedded
    embedded --> next
}

note bottom of embedded : 内嵌子流程逻辑\n不调用外部流程\n共享主流程上下文
@enduml
```

**功能描述**:

- 在当前流程内嵌入子流程逻辑
- 与 SubProcess 不同，不调用外部流程

---

## 服务任务类型

### 10. JavaServiceTask (Java服务任务)

**类名**: `JavaServiceTask`
**BPMN元素**: `serviceTask` (type="java")
**类型名**: `javaServiceTask`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
skinparam rectangleBackgroundColor #FCE4EC
skinparam rectangleBorderColor #C2185B
skinparam componentBackgroundColor #FFF8E1
skinparam componentBorderColor #F57C00

rectangle "前一步" as prev
rectangle "Java服务任务" as javaTask
component "Java方法" as method
rectangle "下一步" as next

prev --> javaTask : 输入参数
javaTask --> method : 调用方法
method --> javaTask : 返回结果
javaTask --> next : 继续流程

note right of method : Bean方法调用\n或静态方法调用\n参数绑定\n结果映射
@enduml
```

**功能描述**:

- 调用Java方法执行业务逻辑
- 支持静态方法调用和Bean方法调用

**核心属性**:

- `beanName`: Bean名称 (与javaClass互斥)
- `javaClass`: Java类 (与javaClass互斥)
- `methodName`: 方法名
- `argBindings`: 参数绑定列表

**使用示例**:

```java
new JavaServiceTask()
    .id("javaTask")
    .name("Java服务")
    .beanName("myService")
    .methodName("processData")
    .argValue("param1")
    .argExpression("variable1")
```

### 11. HttpServiceTask (HTTP服务任务)

**类名**: `HttpServiceTask`
**BPMN元素**: `serviceTask` (type="http")
**类型名**: `httpServiceTask`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
skinparam rectangleBackgroundColor #FFEBEE
skinparam rectangleBorderColor #D32F2F
skinparam cloudBackgroundColor #E3F2FD
skinparam cloudBorderColor #1976D2

rectangle "前一步" as prev
rectangle "HTTP服务任务" as httpTask
cloud "外部API" as api
rectangle "下一步" as next

prev --> httpTask : 准备请求
httpTask --> api : HTTP请求
api --> httpTask : HTTP响应
httpTask --> next : 处理响应

note right of api : RESTful API\nHTTP/HTTPS\nJSON/XML\n认证授权
@enduml
```

**功能描述**:

- 执行HTTP接口调用
- 支持RESTful API集成

**使用示例**:

```java
new HttpServiceTask()
    .id("httpTask")
    .name("HTTP服务")
```

---

## 特殊任务类型

### 12. ExecutionTask (执行任务)

**类名**: `ExecutionTask`
**BPMN元素**: `executionTask`
**类型名**: `executionTask`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black

:前一步;
:执行任务|
if (是否延时执行?) then (是)
  :等待延时|
  note right: 延时策略\n时间单位配置
else (否)
  :立即执行|
endif
:业务逻辑处理|
:下一步;

note right of 执行任务 : ExecutionTask特点：\n• 自定义业务逻辑\n• 延时/条件等待\n• 企业互联集成\n• 支持多种执行策略
@enduml
```

**功能描述**:

- 执行自定义业务逻辑
- 支持延时执行和条件等待
- 支持企业互联应用集成

**核心属性**:

- `itemList`: 执行项列表
- `delay`: 是否延时执行
- `delayStrategy`: 延时策略 (condition/delay)
- `latencyUnit`: 延时单位 (1-天、2-小时、3-分钟)
- `rule`: 条件规则
- `linkApp`: 企业互联应用ID

**使用示例**:

```java
new ExecutionTask()
    .id("execution")
    .name("执行任务")
    .item(executionItem)
    .delay(true)
    .delayStrategy("delay")
```

### 13. BlockExecutionTask (阻塞执行任务)

**类名**: `BlockExecutionTask`
**BPMN元素**: `blockExecutionTask`
**类型名**: `blockExecutionTask`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
skinparam rectangleBackgroundColor #FFE0B2
skinparam rectangleBorderColor #E65100

rectangle "前一步" as prev
rectangle "阻塞执行任务" as blockTask
rectangle "屏幕操作" as screen
rectangle "下一步" as next

prev --> blockTask : 进入阻塞
blockTask --> screen : 执行操作
screen --> blockTask : 操作完成
blockTask --> next : 解除阻塞

note bottom of blockTask : 纷享 One Flow\n阻塞式执行\n屏幕操作配置
@enduml
```

**功能描述**:

- 纷享 One Flow 阻塞的自动节点任务
- 支持屏幕操作节点配置

**核心属性**:

- `executeType`: 执行类型 (auto/task)
- `item`: 执行项
- `extension`: 扩展配置信息
- `outputList`: 输出列表

### 14. LoopTask (循环任务)

**类名**: `LoopTask`
**BPMN元素**: `loopTask`
**类型名**: `loopTask`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
skinparam rectangleBackgroundColor #C8E6C9
skinparam rectangleBorderColor #388E3C

rectangle "前一步" as prev
rectangle "循环任务" as loop
rectangle "循环体" as body
rectangle "下一步" as next

prev --> loop
loop --> body : 开始循环
body --> loop : 循环条件检查
loop --> next : 循环结束

note right of loop : 重复执行逻辑\n循环条件控制\n纷享 One Flow
@enduml
```

**功能描述**:

- 纷享 One Flow 循环任务
- 用于重复执行特定逻辑

### 15. UserLatencyTask (用户延时任务)

**类名**: `UserLatencyTask`

**示意图**:

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black

:前一步;
:用户延时任务|
:设置延时等待|
note right: 配置延时时间\n延时单位设置
:延时时间到达|
:用户操作处理|
:下一步;

note right of 用户延时任务 : UserLatencyTask特点：\n• 用户相关延时\n• 时间延迟处理\n• 用户交互控制\n• 支持灵活的时间配置
@enduml
```

**功能描述**:

- 用户相关的延时任务
- 支持时间延迟处理

---

## 抽象基类

### AbstractBindableActivity

**功能描述**:

- 可绑定活动的抽象基类
- 提供输入输出绑定功能
- 支持多实例配置

### ServiceTask

**功能描述**:

- 服务任务的抽象基类
- JavaServiceTask 和 HttpServiceTask 的父类

---

## 配置类和辅助类

### HandlerConfig

**功能描述**:

- 人员分组配置信息

### TimeoutExecution

**功能描述**:

- 节点超时执行动作配置

### ExecutionItem

**功能描述**:

- 执行项配置

### Execution

**功能描述**:

- 执行相关配置

### ErrMsgI18N

**功能描述**:

- 错误消息国际化支持

---

## 综合工作流示例

以下是一个包含多种活动类型的完整工作流示例：todo:不符合集成平台流程

```plantuml
@startuml
!theme plain
skinparam backgroundColor white
skinparam defaultFontColor black
title 完整工作流示例 - 请假审批流程

start
:提交申请;<<procedure>>
note right: UserTask\n用户任务

if (金额判断?) then (< 1000)
  :直接主管审批;<<procedure>>
  note right: UserTask\n审批任务
else (>= 1000)
  :HR审批;<<procedure>>
  note right: UserTask\n高级审批
endif

fork
  :发送通知;<<procedure>>
  note right: HttpServiceTask\nHTTP服务调用
fork again
  :更新考勤;<<procedure>>
  note right: JavaServiceTask\nJava服务调用
end fork

:子流程调用\n(薪资调整);<<procedure>>
note right: SubProcess\n调用外部流程

:等待确认;<<procedure>>
note right: ReceiveTask\n等待外部消息

stop
@enduml
@enduml
```

## 总结

Effektif工作流引擎提供了丰富的活动类型，涵盖了：

1. **基础流程控制**: StartEvent、EndEvent、网关类型
2. **人工任务**: UserTask 支持复杂的审批流程
3. **自动任务**: 各种服务任务和执行任务
4. **流程组织**: 子流程和嵌入式子流程
5. **企业集成**: 支持HTTP服务、Java服务、企业互联等

每种活动类型都遵循BPMN 2.0标准，同时扩展了企业级功能，如审批流程、驳回策略、延时执行等，满足复杂业务场景的需求。

### 设计特点

- **标准兼容**: 完全遵循BPMN 2.0规范
- **企业扩展**: 增加了审批流程、驳回策略等企业级功能
- **灵活配置**: 支持丰富的参数配置和条件设置
- **集成能力**: 提供多种服务集成方式
- **可视化**: 所有节点都有清晰的图形表示
