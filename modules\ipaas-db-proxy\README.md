# iPaaS DB Proxy 模块

## 概述

iPaaS DB Proxy 是 iPaaS 平台的数据库代理模块，提供数据库连接代理和数据访问服务。该模块支持 MongoDB 数据库，并提供完整的集成测试支持。

## 功能特性

- **数据库连接管理**: 提供 MongoDB 连接的代理服务
- **数据访问层**: 基于 Spring Data MongoDB 的数据访问
- **集成测试**: 使用 Testcontainers 进行完整的数据库集成测试
- **Docker 支持**: 支持使用 Docker 本地启动数据库资源

## 技术栈

- **Java 21**: 编程语言
- **Spring Boot**: 应用框架
- **Spring Data MongoDB**: 数据访问层
- **MongoDB 4.0**: 数据库
- **Testcontainers**: 集成测试容器化
- **JUnit 5**: 测试框架
- **AssertJ**: 断言库

## 项目结构

```
modules/ipaas-db-proxy/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/fxiaoke/erpdss/ipaas/dbproxy/
│   │   │       ├── IPaaSDbProxyApplication.java      # 主应用类
│   │   │       ├── config/
│   │   │       │   └── MongoConfig.java              # MongoDB 配置
│   │   │       ├── model/
│   │   │       │   └── TestDocument.java             # 测试文档模型
│   │   │       ├── repository/
│   │   │       │   └── TestDocumentRepository.java   # 数据访问层
│   │   │       └── service/
│   │   │           └── DatabaseProxyService.java     # 数据库代理服务
│   │   └── resources/
│   │       └── application.yml                       # 应用配置
│   └── test/
│       ├── java/
│       │   └── com/fxiaoke/erpdss/ipaas/dbproxy/
│       │       └── DatabaseConnectionIT.java         # 集成测试
│       └── resources/
│           ├── application-test.yml                  # 测试配置
│           └── init-mongo.js                         # MongoDB 初始化脚本
├── docker-compose.test.yml                           # Docker Compose 测试配置
├── pom.xml                                          # Maven 配置
└── README.md                                        # 项目文档
```

## 快速开始

### 环境要求

- JDK 21+
- Maven 3.6+
- Docker (用于集成测试)

### 构建项目

```bash
# 编译项目
mvn clean compile

# 运行单元测试
mvn test

# 运行集成测试
mvn verify
```

### 使用 Docker 启动测试数据库

```bash
# 启动 MongoDB 测试容器
docker-compose -f docker-compose.test.yml up -d

# 停止测试容器
docker-compose -f docker-compose.test.yml down
```

## 集成测试

### 测试特性

- **自动化容器管理**: 使用 Testcontainers 自动启动和停止 MongoDB 容器
- **数据库版本**: MongoDB 4.0
- **测试隔离**: 每个测试方法都有独立的数据环境
- **完整覆盖**: 涵盖 CRUD 操作、查询功能、错误处理等

### 运行集成测试

```bash
# 运行所有集成测试
mvn verify

# 运行特定的集成测试
mvn test -Dtest=DatabaseConnectionIT -Dspring.profiles.active=test

# 使用 Docker Compose 运行测试
docker-compose -f docker-compose.test.yml up -d
mvn test -Dtest=DatabaseConnectionIT
docker-compose -f docker-compose.test.yml down
```

### 测试用例

集成测试包含以下测试用例：

1. **数据库连接测试**: 验证数据库连接是否正常
2. **CRUD 操作测试**: 测试创建、读取、更新、删除操作
3. **批量操作测试**: 测试批量数据操作
4. **查询功能测试**: 测试各种查询方法
5. **MongoDB 特定功能测试**: 测试 MongoDB 特有功能
6. **错误处理测试**: 测试异常情况处理
7. **并发操作测试**: 测试并发数据操作

## 配置说明

### 应用配置 (application.yml)

```yaml
fxiaoke:
  starter:
    config:
      mongo:
        name: fs-erpdss-ipaas-db
        sections: mongo
```

### 测试配置 (application-test.yml)

```yaml
spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017/ipaas_db_proxy_test

test:
  mongodb:
    image: "mongo:4.0"
    database: "ipaas_db_proxy_test"
    testcontainers-enabled: true
```

## API 接口

### DatabaseProxyService

主要的数据库代理服务类，提供以下功能：

- `testConnection()`: 测试数据库连接
- `getDatabaseInfo()`: 获取数据库信息
- `createTestDocument()`: 创建测试文档
- `findTestDocumentById()`: 根据ID查找文档
- `updateTestDocument()`: 更新文档
- `deleteTestDocument()`: 删除文档
- `countTestDocuments()`: 统计文档数量

## 开发指南

### 添加新的数据模型

1. 在 `model` 包中创建新的文档类
2. 使用 `@Document` 注解标记集合名称
3. 在 `repository` 包中创建对应的 Repository 接口
4. 在 `service` 包中添加业务逻辑

### 编写测试

1. 单元测试使用 `*Test.java` 命名
2. 集成测试使用 `*IT.java` 命名
3. 使用 `@Testcontainers` 注解启用容器测试
4. 使用 `@ActiveProfiles("test")` 激活测试配置

## 故障排除

### 常见问题

1. **Docker 连接问题**: 确保 Docker 服务正在运行
2. **端口冲突**: 检查 27017 端口是否被占用
3. **内存不足**: 确保有足够的内存运行 MongoDB 容器
4. **网络问题**: 检查防火墙和网络配置

### 日志配置

测试环境下可以通过以下配置查看详细日志：

```yaml
logging:
  level:
    com.fxiaoke.erpdss.ipaas.dbproxy: DEBUG
    org.springframework.data.mongodb: DEBUG
    org.testcontainers: INFO
```

## 贡献指南

1. 遵循项目的编码规范
2. 为新功能添加相应的测试
3. 更新相关文档
4. 提交前运行完整的测试套件

## 许可证

本项目遵循公司内部许可证。
