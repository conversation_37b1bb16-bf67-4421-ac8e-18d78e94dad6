/* Copyright (c) 2014, Effektif GmbH.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License. */
package com.effektif.mongo;

interface JobFields {

  String KEY = "key";
  String DUE_DATE = "dueDate";
  String LOCK = "lock";
  String EXECUTIONS = "executions";
  String RETRIES = "retries";
  String RETRY_DELAY = "retryDelay";
  String DONE = "done";
  String DEAD = "dead";
  String ORGANIZATION_ID = "organizationId";
  String PROCESS_ID = "processId";
  String WORKFLOW_ID = "workflowId";
  String WORKFLOW_INSTANCE_ID = "workflowInstanceId";
  String LOCK_WORKFLOW_INSTANCE = "lockWorkflowInstance";
  String ACTIVITY_INSTANCE_ID = "activityInstanceId";
  String ERROR = "error";
  String LOGS = "logs";
  String TIME = "time";
  String DURATION = "duration";
  String OWNER = "owner";
  String JOB_TYPE = "jobType";
}