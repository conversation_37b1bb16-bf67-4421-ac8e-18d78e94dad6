package com.effektif.workflow.impl.activity.types.userTask.handler.create;

import com.effektif.workflow.api.FlowVersion;
import com.effektif.workflow.api.activities.UserTask;
import com.effektif.workflow.api.ext.TaskDelegateConfig;
import com.effektif.workflow.api.ext.TaskDelegateLogPojo;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.workflow.Extensible;
import com.effektif.workflow.impl.activity.types.UserTaskImpl;
import com.effektif.workflow.impl.ext.Task;
import com.effektif.workflow.impl.ext.TaskConfigSupportPojo;
import com.effektif.workflow.impl.workflow.WorkflowImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang.math.NumberUtils;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

import static com.effektif.workflow.api.ext.WorkflowConstants.AssigneeType.GROUP_HANDLER;

/**
 * <AUTHOR>
 * @date 2023/10/1
 * @apiNote 任务创建时，与业务相关入口
 **/
public abstract class UserTaskCreateHandler {
    public static volatile String USERTASK_SUPPORT_CLASS_NAME = "com.facishare.paas.workflow.kernel.support.UserTaskSupport";
    public static volatile String TASK_DELEGATE_LOG_SUPPORT_CLASS_NAME = "com.facishare.paas.workflow.kernel.support.TaskDelegateLogSupport";
    public static volatile String TASK_CONFIG_SUPPORT = "com.facishare.paas.workflow.kernel.support.TaskConfigSupport";

    public abstract Task generateTask(UserTaskImpl userTaskImpl, ActivityInstanceImpl activityInstance, Task task);

    public static Task setTaskBasicProperties(UserTaskImpl userTaskImpl, UserTask userTaskActivityConfig, ActivityInstanceImpl activityInstance) {
        Task task = new Task();

        //设置与流程定义相关属性
        WorkflowImpl workflow = activityInstance.workflow;
        WorkflowInstanceImpl workflowInstance = activityInstance.workflowInstance;
        task.setType(workflow.getType());
        task.setTenantId(workflow.getTenantId());
        task.setWorkflowId(workflow.id.getInternal());
        task.setSourceWorkflowId(workflow.sourceWorkflowId);
        task.setWorkflowName(workflow.getName());
        task.setWorkflowDescription(workflow.getDescription());

        //设置与任务相关属性
        task.setId(userTaskImpl.generateTaskId());
        task.setNodeType(userTaskActivityConfig.getNodeType());
        task.setName(userTaskActivityConfig.getName());
        task.setDescription(userTaskActivityConfig.getDescription());
        task.setDueDate(userTaskActivityConfig.getDueDate());
        task.setActivityNotify(true);
        task.setActivityId(userTaskActivityConfig.getId());
        task.setActivityInstanceId(activityInstance.id);
        task.setWorkflowInstanceId(workflowInstance.id.getInternal());
        task.setAssignType(userTaskActivityConfig.getAssignType());
        task.setAssignee(userTaskActivityConfig.getAssignee());
        task.setAssigneeFunction(userTaskActivityConfig.getAssigneeFunction());
        task.setAssigneeType(userTaskActivityConfig.getAssigneeType());
        task.property(GROUP_HANDLER, userTaskActivityConfig.getGroupHandler());
        task.setTaskType(userTaskActivityConfig.getTaskType());
        //节点完成条件（BPM/CRM）
        task.setRule(userTaskActivityConfig.getRule());
        task.setRejectRule(userTaskActivityConfig.getRejectRule());
        task.setFunctionRule(userTaskActivityConfig.getFunctionRule());
        task.setRejectFunctionRule(userTaskActivityConfig.getRejectFunctionRule());
        task.setTimeoutExecution(userTaskActivityConfig.getTimeoutExecution());

        task.setAppId(workflow.getAppId());
        task.setApplicantId(getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.applicantId.toString()));
        task.setOuterSubmitter(getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.outerSubmitter.toString()));
        //数据提交人
        task.setSubmitter(getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.submitter.toString()));
        task.setApplicantAccount(getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.applicantAccount.toString()));
        /**
         * 设置企业互联相关信息
         */
        setLinkApp(task, userTaskActivityConfig);
        /**
         * 超时提醒相关
         */
        setReminders(task, userTaskActivityConfig);

        task.setState(WorkflowConstants.UserTaskStatus.IN_PROGRESS);

        Long currentTime = System.currentTimeMillis();
        task.setCreateTime(currentTime);
        task.setModifyTime(currentTime);


        task.setExecution(userTaskActivityConfig.getExecution());
        task.setSourceTransition((Map<String, String>) userTaskActivityConfig.getProperty(WorkflowConstants.SOURCE_TRANSITION));
        task.setAllPassType(userTaskActivityConfig.getAllPassType());
        task.setCandidateByPreTask(userTaskActivityConfig.getCandidateByPreTask());

        task.setEntityId(getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.entityId.toString()));
        task.setObjectId(getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.objectId.toString()));
        task.setExistsDataChangeLog(Boolean.FALSE);
        /**
         * 880 业务元素扩展
         */
        task.setCustom(userTaskActivityConfig.isCustom());
        task.setCustomCandidateIds(userTaskActivityConfig.isCustomCandidateConfig());
        task.setCustomExtension(userTaskActivityConfig.getCustomExtension());
        task.setElementApiName(userTaskActivityConfig.getElementApiName());
        task.setImportObject(userTaskActivityConfig.isImportObject());
        task.setVersion(FlowVersion.VERSION);
        task.setCountersignStrategy(userTaskActivityConfig.getCountersignStrategy());

        return task;
    }
    @NoArgsConstructor
    @Data
    public static class CustomCandidateIds{
        boolean success = false;
        String message;
        List<String> data;

        public CustomCandidateIds(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        public CustomCandidateIds(boolean success,List<String> data) {
            this.success = success;
            this.data = data;
        }
    }

    /**
     * 获取业务元素自定义处理人
     */
    @SneakyThrows
    private CustomCandidateIds getCustomCandidateIds(Task task){
        /**
         * workflowId
         * activityId
         * workflowName
         * sourceWorkflowId
         * taskId
         * entityId
         * objectId
         * extension
         * customExtension
         */
        Class<?> userTaskSupport = Class.forName(USERTASK_SUPPORT_CLASS_NAME);
        Method customCandidateAnalysisMethod = userTaskSupport.getMethod("getCustomCandidateIds", Task.class);
        return (CustomCandidateIds) customCandidateAnalysisMethod.invoke(userTaskSupport.newInstance(), task);
    }

    protected Task setCustomCandidateIds(Task task) {
        CustomCandidateIds candidateResult = getCustomCandidateIds(task);
        if(candidateResult.isSuccess()){
            task.setCandidateIds(candidateResult.getData());
        }else{
            task.setCandidateIds(Lists.newArrayList());
            task.setErrMsg(candidateResult.getMessage());
        }
        return task;
    }
    /**
     * 设置超时提醒
     * @param task
     * @param userTaskActivityConfig
     */
    protected static void setReminders(Task task, UserTask userTaskActivityConfig) {
        task.setRemind((Boolean) userTaskActivityConfig.getProperty(WorkflowBindingEnum.remind.toString()));
        task.setRemindLatency(userTaskActivityConfig.getProperty(WorkflowBindingEnum.remindLatency.toString()));
        task.setLatencyUnit((Integer) userTaskActivityConfig.getProperty(WorkflowBindingEnum.latencyUnit.toString()));
        task.setReminders(userTaskActivityConfig.getProperty(WorkflowBindingEnum.reminders.toString()));
        task.setRemindersV2(userTaskActivityConfig.getProperty(WorkflowBindingEnum.remindersV2.toString()));
    }

    /**
     * 查询委托设置
     */
    @SneakyThrows
    public static Consumer<Task> setDelegateCandidateAndLogs(Task task) {
        Class<?> taskDelegateLogSupport = Class.forName(TASK_DELEGATE_LOG_SUPPORT_CLASS_NAME);
        Method taskDelegateLogSupportMethod = taskDelegateLogSupport.getMethod("setDelegateCandidateAndLogs", BiConsumer.class, TaskDelegateConfig.TaskDelegateQuery.class);

        BiConsumer<List<String>,List<TaskDelegateLogPojo>>  biConsumer = (candidateIds, taskDelegateLogPojos) -> {
            task.setCandidateIds(candidateIds);
            task.setTaskDelegateLog(taskDelegateLogPojos);
        };
        return (Consumer<Task>) taskDelegateLogSupportMethod.invoke(taskDelegateLogSupport.newInstance(), biConsumer,TaskDelegateConfig.TaskDelegateQuery.create(task.getTenantId(),
                task.getAppId(), task.getType(), task.getId().toString(), task.getEntityId(), task.getObjectId(), task.getCandidateIds(),task.getLinkApp(), task.getSourceWorkflowId()));
    }


    /**
     * 批量查询委托设置
     */
    @SneakyThrows
    protected static Function<Task,Consumer<Task>> setBatchDelegateCandidateAndLogs(Task task, Map<String, List<String>> candidateIds) {
        Class<?> taskDelegateLogSupport = Class.forName(TASK_DELEGATE_LOG_SUPPORT_CLASS_NAME);
        Method taskDelegateLogSupportMethod = taskDelegateLogSupport.getMethod("setBatchDelegateCandidateAndLogs", TaskDelegateConfig.BatchTaskDelegateQuery.class);
        Function<Task,Consumer<Task>> batchTaskDelegateQueryResult = (Function<Task,Consumer<Task>>) taskDelegateLogSupportMethod.invoke(taskDelegateLogSupport.newInstance(),
                TaskDelegateConfig.BatchTaskDelegateQuery.create(task.getTenantId(),task.getType(),task.getAppId(),task.getEntityId(),task.getSourceWorkflowId(),candidateIds));
        return batchTaskDelegateQueryResult;
    }
    /**
     * 解析任务的超时提醒
     */
    @SneakyThrows
    protected static void parseReminders(Task task, UserTask userTask, Map variableMap) {

        Boolean remind = (Boolean) userTask.getProperty(WorkflowBindingEnum.remind.toString());
        Object remindLatency  = userTask.getProperty(WorkflowBindingEnum.remindLatency.toString());
        Integer latencyUnit = (Integer) userTask.getProperty(WorkflowBindingEnum.latencyUnit.toString());

        TaskConfigSupportPojo.RemindLatencySupport remindLatencySupport = TaskConfigSupportPojo.RemindLatencySupport.of(
                task.getTenantId(),
                task.getAppId(),
                task.getType(),
                task.getActivityId(),
                task.getEntityId(),
                task.getObjectId(),
                variableMap,remind,remindLatency,latencyUnit
        );
        Class<?> taskActionSupport = Class.forName(TASK_CONFIG_SUPPORT);
        Method taskActionSupportExecute = taskActionSupport.getMethod("execute", TaskConfigSupportPojo.BasicSupport.class);
        Map<String, Object> result = (Map<String, Object>) taskActionSupportExecute.invoke(taskActionSupport.newInstance(), remindLatencySupport);
        if (null != result && result.size() > 0) {
            if (result.containsKey(WorkflowBindingEnum.remindLatency.toString())) {
                task.setRemindLatency(result.get(WorkflowBindingEnum.remindLatency.toString()));
            }
        }
    }

    protected static void setLinkApp(Task task, Extensible activity){
        Object linkAppEnable = activity.getProperty(WorkflowBindingEnum.linkAppEnable.toString());
        if(Objects.nonNull(linkAppEnable) && Boolean.valueOf(linkAppEnable.toString())){
            //设置外部互联应用相关的属性
            task.setLinkAppEnable(Boolean.TRUE);
            task.setLinkApp(activity.getProperty(WorkflowBindingEnum.linkApp.toString()).toString());
            if(Objects.nonNull(activity.getProperty(WorkflowBindingEnum.linkAppName.toString()))){
                task.setLinkAppName(activity.getProperty(WorkflowBindingEnum.linkAppName.toString()).toString());
            }
            Object outerTenantField = activity.getProperty(WorkflowBindingEnum.outerTenantField.toString());
            if(Objects.nonNull(outerTenantField)){
                task.setOuterTenantField(outerTenantField.toString());
            }
            Object linkAppType = activity.getProperty(WorkflowBindingEnum.linkAppType.toString());
            if(Objects.nonNull(linkAppType) && NumberUtils.isNumber(linkAppType.toString())){
                BigDecimal bigDecimal = NumberUtils.createBigDecimal(linkAppType.toString());
                task.setLinkAppType(bigDecimal.intValue());
            }
            Object outerTenantId = activity.getProperty(WorkflowBindingEnum.outerTenantId.toString());
            if(Objects.nonNull(outerTenantId)){
                task.setOuterTenantId(outerTenantId.toString());
            }
        }
    }
    protected static String getObjectHelper(Object obj) {
        String result = null;
        if (obj != null) {
            result = obj.toString();
        }
        return result;
    }

    protected static String getWorkflowProperty(WorkflowImpl workflow, String key) {
        if (workflow == null || key == null) {
            return null;
        }
        return getObjectHelper(workflow.getProperty(key));
    }

    protected static String getWorkflowInstanceProperty(WorkflowInstanceImpl workflowInstance, String key) {
        if (workflowInstance == null || key == null) {
            return null;
        }
        if (workflowInstance.getProperty(key) != null) {
            return getObjectHelper(workflowInstance.getProperty(key));
        }
        return getObjectHelper(workflowInstance.getTransientProperty(key));
    }

    /**
     * 流程类型
     */
    public abstract String getType();
}
