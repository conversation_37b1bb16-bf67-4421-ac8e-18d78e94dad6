# Service层AOP切面使用说明

## 概述

`ServiceLogAspect` 是一个用于Service层的AOP切面，提供统一的日志记录和异常处理功能。

## 功能特性

### 1. 自动日志记录
- 方法执行开始时记录参数
- 方法执行成功时记录返回值和执行时间
- 支持配置是否记录参数、返回值和执行时间

### 2. 统一异常处理
- **IPaaSBizException**: 业务异常，只记录简单info日志，不打印堆栈
- **IPaaSSystemException**: 系统异常，记录error日志和完整异常堆栈
- **其他异常**: 自动包装为IPaaSSystemException处理

### 3. 统一返回格式
- 异常情况下自动返回统一的`Result<T>`格式
- 正常执行时不改变原有返回值

## 使用方式

### 1. 自动拦截
切面会自动拦截以下方法：
- `com.fxiaoke.erpdss.ipaas` 包下类名以"Service"结尾的类的所有public方法

```java
package com.fxiaoke.erpdss.ipaas.user.service;

@Service
public class UserService {

    // 这个方法会被自动拦截（因为在指定包下且类名以Service结尾）
    public User getUserById(Long id) {
        // 业务逻辑
        return user;
    }
}
```

**注意**：只有在 `com.fxiaoke.erpdss.ipaas` 包及其子包下的Service类才会被自动拦截。

### 2. 使用@ServiceLog注解
可以使用`@ServiceLog`注解进行更精细的控制：

```java
@Service
public class UserService {
    
    // 基本使用
    @ServiceLog("获取用户信息")
    public User getUserById(Long id) {
        return userRepository.findById(id);
    }
    
    // 不记录参数（适用于敏感数据）
    @ServiceLog(value = "用户登录", logParams = false)
    public LoginResult login(String username, String password) {
        // 登录逻辑
        return loginResult;
    }
    
    // 不记录返回值（适用于敏感返回数据）
    @ServiceLog(value = "获取用户详情", logResult = false)
    public UserDetail getUserDetail(Long id) {
        // 获取详情逻辑
        return userDetail;
    }
    
    // 不记录执行时间
    @ServiceLog(value = "快速查询", logExecutionTime = false)
    public List<User> quickQuery() {
        // 快速查询逻辑
        return users;
    }
}
```

### 3. 异常处理示例

```java
@Service
public class OrderService {
    
    @ServiceLog("创建订单")
    public Order createOrder(OrderRequest request) {
        // 参数校验失败 - 抛出业务异常
        if (request.getAmount() <= 0) {
            throw new IPaaSBizException("订单金额必须大于0");
        }
        
        try {
            // 调用外部服务
            return externalService.createOrder(request);
        } catch (Exception e) {
            // 系统异常
            throw new IPaaSSystemException("创建订单失败", e);
        }
    }
}
```

## 日志输出示例

### 正常执行
```
[trace123] UserService.getUserById 开始执行，参数: [1]
[trace123] UserService.getUserById 执行成功，耗时: 15ms，返回值: {"id":1,"name":"张三"}
```

### 业务异常
```
[trace123] OrderService.createOrder 开始执行，参数: [{"amount":0}]
[trace123] OrderService.createOrder 执行失败，耗时: 5ms，业务异常: [s306240001] 订单金额必须大于0
```

### 系统异常
```
[trace123] OrderService.createOrder 开始执行，参数: [{"amount":100}]
[trace123] OrderService.createOrder 执行失败，耗时: 1200ms，系统异常: [s306240000] 创建订单失败
java.lang.RuntimeException: 外部服务调用失败
    at com.example.ExternalService.createOrder(ExternalService.java:25)
    ...
```

## 配置说明

### @ServiceLog注解参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | "" | 操作描述 |
| logParams | boolean | true | 是否记录参数 |
| logResult | boolean | true | 是否记录返回值 |
| logExecutionTime | boolean | true | 是否记录执行时间 |

### 切点规则

1. `@ServiceLog`注解的方法
2. `com.fxiaoke.erpdss.ipaas` 包下类名以"Service"结尾的类的public方法

## 注意事项

1. **只拦截public方法**: 私有方法、protected方法不会被拦截
2. **异常返回值**: 发生异常时，原方法的返回值类型会被替换为`Result<T>`
3. **性能考虑**: 参数和返回值的JSON序列化可能影响性能，对于大对象建议关闭相关日志
4. **敏感数据**: 对于包含敏感信息的方法，建议设置`logParams = false`或`logResult = false`

## 依赖要求

确保项目中包含以下依赖：
- Spring Boot AOP Starter
- ipaas-common模块（异常类和Result类）
- Jackson（JSON序列化）
