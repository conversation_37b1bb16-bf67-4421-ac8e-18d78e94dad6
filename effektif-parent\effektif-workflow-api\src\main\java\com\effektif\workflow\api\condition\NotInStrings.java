package com.effektif.workflow.api.condition;

import com.effektif.workflow.api.bpmn.BpmnElement;
import com.effektif.workflow.api.json.TypeName;

/**
 * 当前左侧的值 是否 在 右侧的字符串中
 * 如； 中国 notInStrings "中国;上海;北京" 结果为 false
 */
@TypeName("notInStrings")
@BpmnElement("notInStrings")
public class NotInStrings extends Comparator {
    private static final long serialVersionUID = -6737672885372585159L;

    @Override protected String getName() {
        return "notInStrings";
    }
}
