package com.effektif.workflow.api.ext;

import com.effektif.workflow.api.activities.ExecutionTask;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * zhenghaibo
 * 2017/3/14 13:24
 */
public class ExecutionPojo implements Serializable {
  private static final long serialVersionUID = -8539066498976541093L;

  private String requestId;
  private String tenantId;
  private String appId;
  private String taskType;
  private Boolean async;
  private String type;
  private int rowNo;
  private Map<String, Object> variables;
  private ExecutionTask task;

  public Map<String, Object> getVariables() {
    return variables;
  }

  public void setVariables(Map<String, Object> variables) {
    this.variables = variables;
  }

  public ExecutionTask getTask() {
    return task;
  }

  public void setTask(ExecutionTask task) {
    this.task = task;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  /**
   * success,error,ignore
   */
  private String executionState;

  /**
   * 后动作错误信息
   */
  private String actionErrorMsg;


  /**
   * 后动作错误信息
   */
  private String activityId;

  /**
   * 后动作执行忽略操作人
   */
  private String userId;
  private long modifyTime;
  //创建时间， 当前自动执行项的创建时间
  private long createTime;

  private String sender;
  /**
   * map<type,Set<id>>
   * type:PERSON,DEPT,ROLE,DEPT_LEADER
   */
  private LinkedHashMap<String, Set<String>> recipients;
  private LinkedHashMap<String, Set<String>> ccRecipients;
  private LinkedHashMap<String, Set<String>> bccRecipients;
  private Set<String> emailAddress;
  private String title;
  private String content;
  private String template;

  //zz 2017.11.13 start
  private String afterActionDefinitionId;
  private String afterActionMappingId;

  // zz  2017.11.13  end

  // wangbf 2018.01.19 start
  private Map<String, Object> actionMapping;
  private List<ActionParam> actionParams;
  // wangbf 2018.01.19 end

  /**
   * map<entityId,<field,value>>
   * value:constant
   * $$__value:variable
   * %%__value:expression
   */
  private LinkedHashMap<String, LinkedHashMap<String, String>> fieldMapping;

  private String updateFieldJson;
  private Object updateFieldObject;
  private Map<String, Object> triggerParam;

  private Map<String, String> workflowMap;
  private List customVariables;

  private Boolean useRelated;
  private String relatedObjectId;
  private List<Map<String, Object>> inputs;
  private String sourceWorkflowId;

  public List<Map<String, Object>> getInputs() {
    return inputs;
  }

  public void setInputs(List<Map<String, Object>> inputs) {
    this.inputs = inputs;
  }

  public String getSourceWorkflowId() {
    return sourceWorkflowId;
  }

  public void setSourceWorkflowId(String sourceWorkflowId) {
    this.sourceWorkflowId = sourceWorkflowId;
  }

  public Boolean getUseRelated() {
    return useRelated;
  }

  public void setUseRelated(Boolean useRelated) {
    this.useRelated = useRelated;
  }

  public String getRelatedObjectId() {
    return relatedObjectId;
  }

  public void setRelatedObjectId(String relatedObjectId) {
    this.relatedObjectId = relatedObjectId;
  }

  public String getRelatedEntityId() {
    return relatedEntityId;
  }

  public void setRelatedEntityId(String relatedEntityId) {
    this.relatedEntityId = relatedEntityId;
  }

  private String relatedEntityId;

  public String getTenantId() {
    return tenantId;
  }

  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }

  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public String getTaskType() {
    return taskType;
  }

  public void setTaskType(String taskType) {
    this.taskType = taskType;
  }

  public String getSender() {
    return sender;
  }

  public void setSender(String sender) {
    this.sender = sender;
  }

  public LinkedHashMap<String, Set<String>> getRecipients() {
    return recipients;
  }

  public LinkedHashMap<String, Set<String>> getCcRecipients() {
    return ccRecipients;
  }

  public LinkedHashMap<String, Set<String>> getBccRecipients() {
    return bccRecipients;
  }

  public void setRecipients(LinkedHashMap<String, Set<String>> recipients) {
    this.recipients = recipients;
  }
  public void setCcRecipients(LinkedHashMap<String, Set<String>> ccRecipients) {
    this.ccRecipients = ccRecipients;
  }
  public void setBccRecipients(LinkedHashMap<String, Set<String>> bccRecipients) {
    this.bccRecipients = bccRecipients;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public String getTemplate() {
    return template;
  }

  public void setTemplate(String template) {
    this.template = template;
  }

  public LinkedHashMap<String, LinkedHashMap<String, String>> getFieldMapping() {
    return fieldMapping;
  }

  public void setFieldMapping(LinkedHashMap<String, LinkedHashMap<String, String>> fieldMapping) {
    this.fieldMapping = fieldMapping;
  }

  public String getUpdateFieldJson() {
    return updateFieldJson;
  }

  public void setUpdateFieldJson(String updateFieldJson) {
    this.updateFieldJson = updateFieldJson;
  }

  public Set<String> getEmailAddress() {
    return emailAddress;
  }

  public void setEmailAddress(Set<String> emailAddress) {
    this.emailAddress = emailAddress;
  }

  public Map<String, Object> getTriggerParam() {
    return triggerParam;
  }

  public void setTriggerParam(Map<String, Object> triggerParam) {
    this.triggerParam = triggerParam;
  }

  public Object getUpdateFieldObject() {
    return updateFieldObject;
  }

  public void setUpdateFieldObject(Object updateFieldObject) {
    this.updateFieldObject = updateFieldObject;
  }

  public Map<String, String> getWorkflowMap() {
    return workflowMap;
  }

  public void setWorkflowMap(Map<String, String> workflowMap) {
    this.workflowMap = workflowMap;
  }

  public String getAfterActionDefinitionId() {
    return afterActionDefinitionId;
  }

  public void setAfterActionDefinitionId(String afterActionDefinitionId) {
    this.afterActionDefinitionId = afterActionDefinitionId;
  }

  public String getAfterActionMappingId() {
    return afterActionMappingId;
  }

  public void setAfterActionMappingId(String afterActionMappingId) {
    this.afterActionMappingId = afterActionMappingId;
  }

  public Map<String, Object> getActionMapping() {
    return actionMapping;
  }

  public void setActionMapping(Map<String, Object> actionMapping) {
    this.actionMapping = actionMapping;
  }

  public List<ActionParam> getActionParams() {
    return actionParams;
  }

  public void setActionParams(List<ActionParam> actionParams) {
    this.actionParams = actionParams;
  }

  public int getRowNo() {
    return rowNo;
  }

  public void setRowNo(int rowNo) {
    this.rowNo = rowNo;
  }

  public String getExecutionState() {
    return executionState;
  }

  public void setExecutionState(String executionState) {
    if("success".equals(executionState)){
      setActionErrorMsg(null);
    }
    this.executionState = executionState;
  }

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public long getModifyTime() {
    return modifyTime;
  }

  public void setModifyTime(long modifyTime) {
    this.modifyTime = modifyTime;
  }

  public long getCreateTime() {
    return createTime;
  }

  public void setCreateTime(long createTime) {
    this.createTime = createTime;
  }

  public String getActionErrorMsg() {
    return actionErrorMsg;
  }

  public void setActionErrorMsg(String actionErrorMsg) {
    this.actionErrorMsg = actionErrorMsg;
  }

  public String getActivityId() {
    return activityId;
  }

  public void setActivityId(String activityId) {
    this.activityId = activityId;
  }

  public String getRequestId() {
    return requestId;
  }

  public void setRequestId(String requestId) {
    this.requestId = requestId;
  }

  public Boolean getAsync() {
    return async;
  }

  public void setAsync(Boolean async) {
    this.async = async;
  }

  public List getCustomVariables() {
    return customVariables;
  }

  public void setCustomVariables(List customVariables) {
    this.customVariables = customVariables;
  }
}
