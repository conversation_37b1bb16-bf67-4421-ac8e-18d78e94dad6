package com.fxiaoke.erpdss.ipaas.common.exception;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * 异常基类<br/>
 * 内部不处理多语，msg应该是多语转换后放进来。
 * <AUTHOR> (^_−)☆
 */
public abstract class IPaaSException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = -6459824760173783118L;
    @Getter
    @Setter
    private String code;

    public IPaaSException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public IPaaSException(String code, String msg, Throwable cause) {
        super(msg, cause);
        this.code = code;
    }
}
