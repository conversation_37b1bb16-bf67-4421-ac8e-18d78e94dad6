package com.effektif.mongo;

public interface WorkflowFields {

  String _ID = "_id";
  String NAME = "name";
  String DESC = "description";
  String SOURCE_WORKFLOW_ID = "sourceWorkflowId";
  String ENABLE = "enable";//是否启用 true,false
  String SUB_PROCESS = "subProcess";//是否启用 true,false
  String REMOVE_HISTORY_JOB = "removeHistoryJob";//是否启用 true,false
  String TYPE = "type";//类型 审批流/工作流
  String TENANT_ID = "tenantId";
  String APP_ID = "appId";
  String ENTITY_ID = "entityId";
  String ENTITY_NAME = "entityName";
  String OBJECT_ID = "objectId";
  String PRIORITY = "priority";
  String CREATOR = "creator";
  String CREATE_TIME = "createTime";
  String MODIFIER = "modifier";
  String MODIFY_TIME = "modifyTime";
  String DELETED = "deleted";
  String REMIND = "remind";
  String REMIND_LATENCY = "remindLatency";
  String ACTIVITIES = "activities";
  String VARIABLES = "variables";
  String TRANSITIONS = "transitions";
  String EXECUTION = "execution";
  String TRIGGER_TYPES = "triggerTypes";
  String MERGE_FROM = "mergeFrom";
  String SINGLE_INSTANCE_FLOW ="singleInstanceFlow";
  String DATA_LOCK_TYPE = "dataLockType";
  String EXTERNAL_FLOW ="externalFlow";
  String APPLICANT_CONFIG ="applicantConfig";
  String HISTORY = "history";
  String LINK_APP_ENABLE = "linkAppEnable";
  String LINK_APP = "linkApp";
  String LINK_APP_NAME = "linkAppName";
  String LINK_APP_TYPE = "linkAppType";
  String MODIFY_RECORD = "modifyRecord";
  String TASK_TYPE = "taskType";
  String CUSTOM_VARIABLE_TABLE = "customVariableTable";
  //定义是否受到管控
  String CONTROL_STATUS = "controlStatus";
  String ALLOW_AUTO_COMPLETED_RETRIEVE = "allowAutoCompletedRetrieve";
  String APPLICANT_WHEN_SYSTEM_USER = "applicantWhenSystemUser";
  /**
   * one flow 时添加
   */
  String INPUTS = "inputs";
  String OUTPUTS = "outputs";
  String subType= "subType";

  interface Versions {
    String WORKFLOW_NAME = "workflowName";
    String VERSION_IDS = "versionIds";
    String LOCK = "lock";
  }


  interface VersionsLock {
    String OWNER = "owner";
    String TIME = "time";
  }
}
