/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.api.model;

import com.effektif.workflow.api.workflow.ParseIssues;


/**
 * Wrapper for a deployed {@link com.effektif.workflow.api.workflow.ExecutableWorkflow}.
 *
 * <AUTHOR>
 */
public class Deployment extends ParseIssues {

  protected WorkflowId workflowId;

  public Deployment() {
  }

  public Deployment(WorkflowId workflowId, ParseIssues parseIssues) {
    this.workflowId = workflowId;
    this.issues = parseIssues.getIssues();
  }
  
  public WorkflowId getWorkflowId() {
    return this.workflowId;
  }
  public void setWorkflowId(WorkflowId workflowId) {
    this.workflowId = workflowId;
  }

  @Override
  public Deployment checkNoErrors() {
    super.checkNoErrors();
    return this;
  }

  @Override
  public Deployment checkNoErrorsAndNoWarnings() {
    super.checkNoErrorsAndNoWarnings();
    return this;
  }
}
