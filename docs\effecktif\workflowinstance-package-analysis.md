# Effektif WorkflowInstance 包详细分析

## 概述

本文档详细分析 `effektif-parent/effektif-workflow-api/src/main/java/com/effektif/workflow/api/workflowinstance` 包中的所有类文件，解释它们在 Effektif 工作流实例生命周期中的核心作用和功能。

## 1. 包中的所有类文件

该包包含以下5个核心类：

1. **WorkflowInstance.java** - 工作流实例主类
2. **ScopeInstance.java** - 作用域实例抽象基类
3. **ActivityInstance.java** - 活动实例类
4. **VariableInstance.java** - 变量实例类
5. **TimerInstance.java** - 定时器实例类

## 2. 核心类详细分析

### 2.1 WorkflowInstance - 工作流实例主类

```java
@JsonPropertyOrder({"id", "workflowId", "start", "end", "duration", "activityInstances", "variableInstances", "timerInstances"})
public class WorkflowInstance extends ScopeInstance implements Serializable {
  protected WorkflowInstanceId id;
  protected WorkflowId workflowId;
  protected String tenantId;
  protected String businessKey;
  protected String creatorId;
  protected WorkflowInstanceId callingWorkflowInstanceId;
  protected String callingActivityInstanceId;
  protected List<TimerInstance> jobs;
}
```

**核心职责**：

- **实例标识管理**：维护工作流实例的唯一标识 (id, workflowId)
- **多租户支持**：内置 tenantId 字段，支持多租户数据隔离
- **业务关联**：通过 businessKey 关联业务对象
- **调用链管理**：支持子流程调用 (callingWorkflowInstanceId, callingActivityInstanceId)
- **定时器管理**：管理工作流级别的定时器任务
- **生命周期跟踪**：继承 ScopeInstance 的时间和状态管理

**在工作流引擎中的角色**：

- 作为工作流执行的顶层容器
- 提供工作流实例的完整状态视图
- 支持工作流实例的序列化和持久化

### 2.2 ScopeInstance - 作用域实例抽象基类

```java
public abstract class ScopeInstance extends Extensible {
  protected Long start;
  protected Long end;
  protected String endState;
  protected Long duration;
  protected List<ActivityInstance> activityInstances;
  protected List<VariableInstance> variableInstances;
  public static final String ENDSTATE_CANCELED = "canceled";
}
```

**核心职责**：

- **生命周期管理**：管理作用域的开始、结束时间和持续时间
- **状态管理**：维护结束状态 (endState)，支持取消等状态
- **容器功能**：包含活动实例和变量实例的集合
- **变量访问**：提供变量值的查询和类型转换功能
- **活动查找**：支持按活动ID查找开放的活动实例

**关键方法分析**：

```java
// 查找开放的活动实例
public ActivityInstance findOpenActivityInstance(String activityId) {
  if (activityId!=null && activityInstances!=null) {
    for (ActivityInstance activityInstance: activityInstances) {
      ActivityInstance theOne = activityInstance.findOpenActivityInstance(activityId);
      if (theOne!=null) {
        return theOne;
      }
    }
  }
  return null;
}

// 获取变量值并进行类型转换
public <T> T getVariableValue(String variableId, Type type) {
  Object value = getVariableValue(variableId);
  if (value==null) {
    return (T) value;
  }
  if (type instanceof Class && ((Class)type).isAssignableFrom(value.getClass())) {
    return (T) value;
  }
  return ValueConverter.shoehorn(value, type);
}
```

**在工作流引擎中的角色**：

- 为 WorkflowInstance 和 ActivityInstance 提供共同的基础功能
- 实现作用域的层次化管理
- 提供统一的变量访问接口

### 2.3 ActivityInstance - 活动实例类

```java
@JsonPropertyOrder({"id", "activityId", "start", "end", "duration", "activityInstances", "variableInstances", "timerInstances"})
public class ActivityInstance extends ScopeInstance implements Serializable {
  protected String id;
  protected String activityId;
  protected WorkflowInstanceId calledWorkflowInstanceId;
  protected String activityName;
}
```

**核心职责**：

- **活动执行跟踪**：记录具体活动的执行状态和结果
- **子流程支持**：通过 calledWorkflowInstanceId 支持子流程调用
- **作用域继承**：继承 ScopeInstance 的所有功能
- **活动查找**：重写查找方法，支持递归查找

**关键实现**：

```java
public ActivityInstance findOpenActivityInstance(String activityId) {
  if ( activityId!=null 
       && activityId.equals(this.activityId)
       && !isEnded()) {
    return this;
  }
  return super.findOpenActivityInstance(activityId);
}
```

**在工作流引擎中的角色**：

- 表示工作流中每个活动的执行实例
- 支持嵌套的活动结构（子活动、并行分支等）
- 提供活动级别的变量作用域

### 2.4 VariableInstance - 变量实例类

```java
public class VariableInstance implements Serializable {
  protected String id;
  protected String variableId;
  protected Object value;
  protected DataType type;
}
```

**核心职责**：

- **变量值存储**：存储运行时的变量值
- **类型管理**：维护变量的数据类型信息
- **变量标识**：通过 variableId 关联变量定义

**在工作流引擎中的角色**：

- 实现工作流执行过程中的数据存储
- 支持不同作用域的变量管理
- 提供类型安全的变量访问

### 2.5 TimerInstance - 定时器实例类

```java
public class TimerInstance {
  protected LocalDateTime dueDate;
}
```

**核心职责**：

- **定时任务管理**：记录定时器的到期时间
- **调度支持**：为工作流调度器提供时间信息

**在工作流引擎中的角色**：

- 支持工作流中的定时等待和调度功能
- 与 JobStore 配合实现定时任务执行

## 3. 类之间的关系和依赖

### 3.1 类关系图

```plantuml
@startuml
!define RECTANGLE class

abstract class Extensible {
  +Map<String, Object> properties
}

abstract class ScopeInstance {
  +Long start
  +Long end
  +String endState
  +Long duration
  +List<ActivityInstance> activityInstances
  +List<VariableInstance> variableInstances
  --
  +findOpenActivityInstance(String activityId)
  +getVariableValue(String variableId)
  +isEnded()
  +isOpen()
}

class WorkflowInstance {
  +WorkflowInstanceId id
  +WorkflowId workflowId
  +String tenantId
  +String businessKey
  +String creatorId
  +List<TimerInstance> jobs
  +WorkflowInstanceId callingWorkflowInstanceId
  +String callingActivityInstanceId
}

class ActivityInstance {
  +String id
  +String activityId
  +String activityName
  +WorkflowInstanceId calledWorkflowInstanceId
  --
  +findOpenActivityInstance(String activityId)
}

class VariableInstance {
  +String id
  +String variableId
  +Object value
  +DataType type
}

class TimerInstance {
  +LocalDateTime dueDate
}

Extensible <|-- ScopeInstance
ScopeInstance <|-- WorkflowInstance
ScopeInstance <|-- ActivityInstance

WorkflowInstance *-- "0..*" ActivityInstance : contains
WorkflowInstance *-- "0..*" VariableInstance : contains
WorkflowInstance *-- "0..*" TimerInstance : contains

ActivityInstance *-- "0..*" ActivityInstance : nested activities
ActivityInstance *-- "0..*" VariableInstance : local variables

note right of WorkflowInstance
  顶层工作流实例
  - 管理整个工作流的执行
  - 支持多租户隔离
  - 处理子流程调用
end note

note right of ActivityInstance
  活动执行实例
  - 表示单个活动的执行
  - 支持嵌套活动结构
  - 管理活动级变量作用域
end note

note bottom of ScopeInstance
  作用域抽象基类
  - 提供变量管理功能
  - 实现生命周期管理
  - 支持层次化结构
end note
@enduml
```

### 3.2 实例层次结构图

```plantuml
@startuml

package "工作流实例层次结构" {

  object "WorkflowInstance" as WI {
    id = "wf-001"
    tenantId = "tenant-A"
    businessKey = "order-123"
  }

  object "ActivityInstance 1" as AI1 {
    id = "act-001"
    activityId = "startEvent"
    workState = "completed"
  }

  object "ActivityInstance 2" as AI2 {
    id = "act-002"
    activityId = "userTask"
    workState = "starting"
  }

  object "ActivityInstance 3" as AI3 {
    id = "act-003"
    activityId = "subProcess"
    calledWorkflowInstanceId = "wf-002"
  }

  object "VariableInstance 1" as VI1 {
    variableId = "orderId"
    value = "12345"
    type = "TextType"
  }

  object "VariableInstance 2" as VI2 {
    variableId = "amount"
    value = "1000.0"
    type = "NumberType"
  }

  object "TimerInstance 1" as TI1 {
    dueDate = "2024-01-15T10:00:00"
  }

  object "VariableInstance 3" as VI3 {
    variableId = "taskAssignee"
    value = "user1"
    type = "TextType"
  }

  object "ActivityInstance 4" as AI4 {
    id = "act-004"
    activityId = "nestedTask"
    workState = "starting"
  }

  WI ||--o{ AI1 : contains
  WI ||--o{ AI2 : contains
  WI ||--o{ AI3 : contains
  WI ||--o{ VI1 : contains
  WI ||--o{ VI2 : contains
  WI ||--o{ TI1 : contains

  AI2 ||--o{ VI3 : local variables
  AI3 ||--o{ AI4 : nested activities
}

note right of WI
  工作流实例是顶层容器
  包含所有活动实例、变量实例和定时器实例
end note

note right of AI3
  子流程活动实例
  通过 calledWorkflowInstanceId
  关联到另一个工作流实例
end note
@enduml
```

### 3.3 变量作用域层次图

```plantuml
@startuml

package "WorkflowInstance Scope" as WS {
  package "Global Variables" as GV {
    object "orderId" {
      value = "12345"
      type = "TextType"
    }
    object "customerId" {
      value = "67890"
      type = "TextType"
    }
  }

  package "ActivityInstance 1 Scope" as AS1 {
    package "Local Variables" as LV1 {
      object "taskAssignee" {
        value = "user1"
        type = "TextType"
      }
      object "taskDueDate" {
        value = "2024-01-15"
        type = "DateType"
      }
    }

    package "Nested ActivityInstance Scope" as NAS1 {
      package "Loop Variables" as LoopV {
        object "currentItem" {
          value = "item1"
          type = "TextType"
        }
        object "index" {
          value = "0"
          type = "NumberType"
        }
      }
    }
  }

  package "ActivityInstance 2 Scope" as AS2 {
    package "Local Variables" as LV2 {
      object "approvalResult" {
        value = "approved"
        type = "TextType"
      }
      object "comments" {
        value = "looks good"
        type = "TextType"
      }
    }
  }
}

note right of WS
  工作流级变量
  - 全局可访问
  - 跨活动传递数据
end note

note right of AS1
  活动级变量
  - 局部作用域
  - 可访问父级变量
end note

note right of NAS1
  嵌套活动变量
  - 最内层作用域
  - 可访问所有上级变量
end note

note bottom of WS
  变量查找顺序：
  1. 当前作用域
  2. 父级作用域
  3. 工作流级作用域
end note
@enduml
```

## 4. 在 Effektif 工作流引擎架构中的作用

### 4.1 与实现层的映射


| API 类           | 实现类               | 主要功能               |
| ---------------- | -------------------- | ---------------------- |
| WorkflowInstance | WorkflowInstanceImpl | 执行引擎、工作队列管理 |
| ScopeInstance    | ScopeInstanceImpl    | 变量管理、作用域控制   |
| ActivityInstance | ActivityInstanceImpl | 活动执行、状态转换     |
| VariableInstance | VariableInstanceImpl | 值存储、类型转换       |
| TimerInstance    | Job + TimerImpl      | 定时调度、任务执行     |

### 4.2 生命周期支持

这些类共同支持工作流实例的完整生命周期：

1. **创建阶段**：WorkflowInstance 创建，初始化基本信息
2. **执行阶段**：ActivityInstance 创建和执行，VariableInstance 管理数据流
3. **等待阶段**：TimerInstance 管理定时等待
4. **结束阶段**：ScopeInstance 管理结束状态和清理

### 4.3 数据处理支持

- **变量作用域**：ScopeInstance 提供层次化的变量管理
- **数据传递**：VariableInstance 支持活动间的数据传递
- **类型安全**：通过 DataType 确保数据类型的正确性

## 5. 关键设计模式和原则

### 5.1 组合模式

- ScopeInstance 作为容器，包含 ActivityInstance 和 VariableInstance
- 支持嵌套的活动结构和变量作用域

### 5.2 模板方法模式

- ScopeInstance 定义通用的变量访问模板
- 子类可以重写特定的查找和访问逻辑

### 5.3 序列化支持

- 所有类都实现 Serializable 接口
- 支持工作流实例的持久化和网络传输

## 6. 总结

workflowinstance 包中的类构成了 Effektif 工作流实例的核心数据模型，它们：

1. **提供完整的实例表示**：从工作流实例到活动实例，再到变量和定时器实例
2. **支持层次化管理**：通过 ScopeInstance 实现作用域的嵌套和继承
3. **确保数据一致性**：通过类型系统和序列化机制保证数据的正确性
4. **支持复杂场景**：包括子流程调用、并行执行、定时等待等高级功能
5. **提供 API 抽象**：为上层应用提供清晰、一致的编程接口

这些类的设计充分体现了 Effektif 工作流引擎的成熟性和完整性，为构建企业级工作流应用提供了坚实的基础。

## 7. 实现层详细分析

### 7.1 WorkflowInstanceImpl 核心功能

```java
public class WorkflowInstanceImpl extends ScopeInstanceImpl {
  public WorkflowInstanceId id;
  public String businessKey;
  public LockImpl lock;
  public Queue<ActivityInstanceImpl> work;
  public Queue<ActivityInstanceImpl> workAsync;
  public Long nextActivityInstanceId;
  public Long nextVariableInstanceId;
  public List<Job> jobs;
}
```

**关键功能**：

- **工作队列管理**：维护同步 (work) 和异步 (workAsync) 工作队列
- **ID 生成器**：为活动实例和变量实例生成唯一ID
- **锁定机制**：通过 LockImpl 实现并发控制
- **作业管理**：管理定时器和异步任务

**执行流程**：

```java
public void executeWork() {
  while (hasWork()) {
    ActivityInstanceImpl activityInstance = getNextWork();

    if (STATE_STARTING.equals(activityInstance.workState)) {
      activityInstance.execute();
    } else if (STATE_PROPAGATE_TO_PARENT.equals(activityInstance.workState)) {
      activityInstance.propagateToParent();
    }
  }
}
```

### 7.2 ScopeInstanceImpl 变量管理

```java
public VariableInstanceImpl createVariableInstanceLocal(VariableImpl variable) {
  String variableInstanceId = workflowInstance.generateNextVariableInstanceId();
  VariableInstanceImpl variableInstance = new VariableInstanceImpl(this, variable, variableInstanceId);
  variableInstance.configuration = configuration;
  variableInstance.workflowInstance = workflowInstance;
  variableInstance.type = variable.type;
  variableInstance.setValue(variable.defaultValue);

  if (updates != null) {
    variableInstance.updates = new VariableInstanceUpdates(true);
    updates.isVariableInstancesChanged = true;
  }
  addVariableInstance(variableInstance);
  return variableInstance;
}
```

**变量查找机制**：

```java
public VariableInstanceImpl findVariableInstance(String variableId) {
  if (variableInstances != null) {
    VariableInstanceImpl variableInstance = getVariableInstanceLocal(variableId);
    if (variableInstance != null) {
      return variableInstance;
    }
  }
  // 向上查找父作用域
  if (parent != null) {
    return parent.findVariableInstance(variableId);
  }
  return null;
}
```

### 7.3 ActivityInstanceImpl 状态管理

```java
public class ActivityInstanceImpl extends ScopeInstanceImpl {
  public String id;
  public ActivityImpl activity;
  public String workState;
  public WorkflowInstanceId calledWorkflowInstanceId;

  // 工作状态常量
  public static final String STATE_STARTING = "starting";
  public static final String STATE_PROPAGATE_TO_PARENT = "propagateToParent";
  public static final String STATE_JOINING = "joining";
}
```

**状态转换机制**：

```java
public void setWorkState(String workState) {
  this.workState = workState;
  if (updates!=null) {
    getUpdates().isWorkStateChanged = true;
    if (parent!=null) {
      parent.propagateActivityInstanceChange();
    }
  }
}

public void takeTransition(TransitionImpl transition) {
  if (transition.id!=null || activity.activityType.saveTransitionsTaken()) {
    addTransitionTaken(transition.id);
  }
  // 启动目标活动
  parent.start(transition.to);
}
```

### 7.4 VariableInstanceImpl 值管理

```java
public class VariableInstanceImpl extends BaseInstanceImpl {
  public String id;
  public Object value;
  public VariableImpl variable;
  public DataTypeImpl type;
  public VariableInstanceUpdates updates;

  public void setValue(Object value) {
    this.value = value;
    if (updates!=null) {
      updates.isValueChanged = true;
      if (parent instanceof ActivityInstanceImpl) {
        parent.propagateActivityInstanceChange();
      }
    }
  }
}
```

## 8. 使用场景和最佳实践

### 8.1 工作流实例生命周期流程图

```plantuml
@startuml
!define RECTANGLE class

start

:创建 TriggerInstance;
note right
  包含 tenantId 和触发数据
end note

:创建 WorkflowInstanceImpl;
note right
  new WorkflowInstanceImpl(
    configuration, workflow,
    workflowInstanceId, triggerInstance,
    lock, transientData)
end note

:初始化作用域;
note right
  workflowInstance.initializeScopeInstance()
  - 创建全局变量实例
  - 初始化定时器
end note

:启动执行;
note right
  workflowInstance.start()
  - 创建起始活动实例
  - 添加到工作队列
end note

while (hasWork()?) is (yes)
  :获取下一个工作项;
  note right
    ActivityInstanceImpl activityInstance =
    workflowInstance.getNextWork()
  end note

  if (workState?) then (starting)
    :执行活动;
    note right
      activityInstance.execute()
      - 调用 ActivityType.execute()
      - 更新活动状态
    end note
  elseif (propagateToParent)
    :传播到父级;
    note right
      activityInstance.propagateToParent()
      - 结束当前活动
      - 启动后续活动
    end note
  endif

  :更新实例状态;
endwhile (no)

:工作流实例结束;
note right
  workflowInstance.end()
  - 设置结束时间
  - 清理资源
  - 通知监听器
end note

stop
@enduml
```

### 8.2 活动实例状态转换图

```plantuml
@startuml
!define RECTANGLE class

[*] --> starting : createActivityInstance()

starting --> executing : execute()
note on link
  activity.activityType.execute(this)
end note

executing --> propagateToParent : 活动完成
note on link
  takeTransition()
  启动后续活动
end note

executing --> joining : 等待合并
note on link
  并行分支等待
  其他分支完成
end note

joining --> propagateToParent : 合并完成
note on link
  所有分支都完成
  继续执行流程
end note

propagateToParent --> [*] : end()
note on link
  活动实例结束
  从工作队列移除
end note

executing --> error : 执行异常
error --> propagateToParent : 错误处理完成

note right of starting
  STATE_STARTING
  - 活动实例已创建
  - 等待执行
  - 已添加到工作队列
end note

note right of executing
  活动正在执行
  - 调用具体的活动类型
  - 可能产生异步工作
  - 可能等待外部事件
end note

note right of joining
  STATE_JOINING
  - 并行网关合并点
  - 等待其他分支完成
  - 检查合并条件
end note
@enduml
```

### 8.3 变量查找机制流程图

```plantuml
@startuml
!define RECTANGLE class

start

:调用 getVariableValue(variableId);

:在当前作用域查找;
note right
  检查 variableInstances 列表
  匹配 variableId
end note

if (找到变量?) then (yes)
  :返回变量值;
  note right
    variableInstance.getValue()
  end note

  if (需要类型转换?) then (yes)
    :执行类型转换;
    note right
      ValueConverter.shoehorn(value, type)
    end note
  endif

  :返回结果;
  stop
else (no)
  if (有父作用域?) then (yes)
    :递归查找父作用域;
    note right
      parent.findVariableInstance(variableId)
    end note
  else (no)
    :返回 null;
    stop
  endif
endif
@enduml
```

### 8.4 实际使用示例

#### 8.4.1 创建工作流实例

```java
// 1. 准备触发数据
TriggerInstance triggerInstance = new TriggerInstance("tenant-A");
triggerInstance.sourceWorkflowId("order-process");
triggerInstance.data("orderId", "12345");
triggerInstance.data("customerId", "67890");

// 2. 创建工作流实例
WorkflowInstanceImpl workflowInstance = new WorkflowInstanceImpl(
  configuration, workflow, workflowInstanceId, triggerInstance, lock, null);

// 3. 初始化作用域
workflowInstance.initializeScopeInstance();

// 4. 启动执行
workflowInstance.start();
```

#### 8.4.2 活动实例管理

```java
// 创建活动实例
ActivityInstanceImpl activityInstance = scopeInstance.createActivityInstance(activity);

// 设置工作状态
activityInstance.setWorkState(STATE_STARTING);

// 添加到工作队列
workflowInstance.addWork(activityInstance);

// 执行活动
activityInstance.execute();

// 完成后传播到父级
activityInstance.propagateToParent();
```

#### 8.4.3 变量操作示例

```java
// 创建变量实例
VariableInstanceImpl variableInstance = scopeInstance.createVariableInstanceLocal(variable);

// 设置变量值
scopeInstance.setVariableValue(variableInstance, "new value");

// 查找变量（支持作用域层次查找）
VariableInstanceImpl found = scopeInstance.findVariableInstance("orderId");

// 获取类型化值
String orderId = scopeInstance.getVariableValue("orderId", String.class);
Double amount = scopeInstance.getVariableValue("amount", Double.class);
LocalDateTime dueDate = scopeInstance.getVariableValueDate("dueDate");
```

#### 8.4.4 定时器管理示例

```java
// 创建定时器作业
Job job = timer.createJob(scopeInstance);
job.workflowInstanceId(workflowInstance.getId());
job.activityInstanceId(activityInstance.getId());
job.dueDate(LocalDateTime.now().plusHours(24));

// 添加到工作流实例
workflowInstance.addJob(job);

// 保存到作业存储
JobStore jobStore = configuration.get(JobStore.class);
jobStore.saveJob(job);
```
