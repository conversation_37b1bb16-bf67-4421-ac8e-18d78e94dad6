package com.fxiaoke.erpdss.ipaas.all;

import com.fxiaoke.erpdss.ipaas.IPaaS;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;

/**
 * iPaaS聚合应用主类
 * 集成base和web模块的所有功能
 *
 * <AUTHOR> (^_−)☆
 */
@SpringBootApplication(
        scanBasePackageClasses = IPaaS.class,
        exclude = {MongoAutoConfiguration.class}
)
public class IPaaSAllApp {
    public static void main(String[] args) {
        SpringApplication.run(IPaaSAllApp.class, args);
    }
}
