package com.effektif.workflow.impl.activity.types.userTask.handler.create;

import com.effektif.workflow.api.FlowVersion;
import com.effektif.workflow.api.activities.UserTask;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.model.StageTaskStateChangeRecord;
import com.effektif.workflow.api.workflow.Activity;
import com.effektif.workflow.impl.activity.types.UserTaskImpl;
import com.effektif.workflow.impl.ext.CandidateParseResult;
import com.effektif.workflow.impl.ext.Task;
import com.effektif.workflow.impl.workflow.WorkflowImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2023/10/1
 * @apiNote
 **/
@Slf4j
public class UserTaskCreateStageHandler extends UserTaskCreateHandler{
    public static volatile String STAGE_SERVICE_SUPPORT_CLASS_NAME = "com.facishare.paas.workflow.kernel.support.StageServiceSupport";
    @Override
    @SneakyThrows
    public Task generateTask(UserTaskImpl userTaskImpl, ActivityInstanceImpl activityInstance, Task task) {
        WorkflowInstanceImpl workflowInstance = activityInstance.workflowInstance;
        //如果是终结态，直接进入结束节点
        String terminalId = (String) workflowInstance.getProperty("terminalId");
        if (terminalId != null) {
            activityInstance.onwards();
        }

        WorkflowImpl workflow = activityInstance.workflow;
        UserTask activity=userTaskImpl.activity;
        task.setBeforeSkipOutApprovalId(userTaskImpl.activity.getBeforeSkipOutApprovalId());
        task.setSkip((Boolean) activityInstance.activity.getProperty(WorkflowBindingEnum.skip.toString()));
        /**
         * 阶段不用解析人
         * 下面的很多属性也有不需要的todo
         */
        setStageId(task, userTaskImpl.activity, terminalId);
        if (terminalId != null) {
            task.setState(WorkflowConstants.UserTaskStatus.PASS);
        }
        task.setStageFieldApiName(getWorkflowProperty(workflow, "stageFieldApiName"));
        task.setTerminal(activity.getTerminal() != null && activity.getTerminal());
        task.setSequence(Boolean.TRUE.equals(activity.getProperty("sequence")));
        userTaskImpl.insertTask(task);
        Class<?> stageServiceExtension = Class.forName(STAGE_SERVICE_SUPPORT_CLASS_NAME);
        //处理插入后的定时，提醒，飘相关事宜
        Method afterMethod = stageServiceExtension.getMethod("afterTaskInsert", String.class, String.class);
        afterMethod.invoke(stageServiceExtension.newInstance(), task.getTenantId(), task.getId().toString());

        /**
         * 阶段任务，生成子任务
         */
        //1 自己创建task,不需要审批人，所以不需要提醒
        List<Activity> taskItemList = activity.getTaskItemList();
        createStageItemTask(userTaskImpl,task, taskItemList, activityInstance);
        return task;
    }

    @SneakyThrows
    public static void createStageItemTask(UserTaskImpl userTaskImpl, Task masterTask, List<Activity> taskItemList, ActivityInstanceImpl activityInstance){
        if (taskItemList != null && taskItemList.size() > 0) {
            Class<?> stageServiceExtension = Class.forName(STAGE_SERVICE_SUPPORT_CLASS_NAME);
            List<Task> taskItemInstanceList = getTaskItemInstanceListWithRule(userTaskImpl,masterTask, taskItemList, activityInstance, stageServiceExtension);
            if (taskItemInstanceList.size() > 0) {
                //{taskId:{"errorMsg":"异常信息", "candidateIds":["1002", "1003"]}}
                Map<String, CandidateParseResult> candidateIdsAndErrorMsg = Maps.newHashMap();
                try {
                    Method method = stageServiceExtension.getMethod("getCandidateIds", List.class);
                    candidateIdsAndErrorMsg = (Map<String, CandidateParseResult>) method.invoke(stageServiceExtension.newInstance(), taskItemInstanceList);
                } catch (Throwable e) {
                    log.error("parse candidateIds error", e);
                }

                Map<String, List<String>> candidateIds = Maps.newHashMap();
                candidateIdsAndErrorMsg.forEach((taskId, candidateParseResult) -> {
                    candidateIds.put(taskId, candidateParseResult.getCandidateIds());
                });
                Function<Task, Consumer<Task>> batchDelegateCandidateAndLogsResult = setBatchDelegateCandidateAndLogs(masterTask, candidateIds);

                for (Task taskIns : taskItemInstanceList) {
                    List<String> taskItemCandidateIds = candidateIdsAndErrorMsg.get(taskIns.getId().toString()).getCandidateIds();
                    if (taskItemCandidateIds == null || taskItemCandidateIds.isEmpty()) {
                        taskIns.setState(WorkflowConstants.UserTaskStatus.ERROR);
                        taskIns.setErrMsg(candidateIdsAndErrorMsg.get(taskIns.getId().toString()).getMessage());
                        taskIns.setErrMsgI18N(candidateIdsAndErrorMsg.get(taskIns.getId().toString()).getErrMsgI18N());
                    }
                    appendTaskStateRecord(taskIns);
                    Consumer<Task> recordDelegateLogConsumer = batchDelegateCandidateAndLogsResult.apply(taskIns);
                    userTaskImpl.insertTask(taskIns);
                    Method afterTaskItemMethod = stageServiceExtension.getMethod("afterTaskItemInsert", String.class, String.class);
                    afterTaskItemMethod.invoke(stageServiceExtension.newInstance(), masterTask.getTenantId(), taskIns.getId().toString());
                    recordDelegateLogConsumer.accept(taskIns);
                }
            }
        }
    }

    /**
     * 追加任务状态变更记录
     * @param taskIns
     */
    private static void appendTaskStateRecord(Task taskIns) {
        //添加任务状态变更记录
        String taskRecord = WorkflowConstants.UserTaskStatus.ERROR.equals(taskIns.getState()) ? StageTaskStateChangeRecord.ERROR_IN : taskIns.getState();
        taskIns.setStateChangeRecord(Lists.newArrayList(new StageTaskStateChangeRecord(taskRecord)));
    }

    /**
     * 870添加任务生成条件
     *
     * @param userTaskImpl
     * @param masterTask
     * @param taskItemList
     * @param activityInstance
     * @param stageServiceExtension
     * @return
     */
    @SneakyThrows
    private static List<Task> getTaskItemInstanceListWithRule(UserTaskImpl userTaskImpl, Task masterTask, List<Activity> taskItemList, ActivityInstanceImpl activityInstance, Class<?> stageServiceExtension) {
        List<Task> taskItemInstanceList = Lists.newArrayList();
        Method matchCreateRuleMethod = stageServiceExtension.getMethod("matchStageTaskItemCreateRule", String.class, String.class, List.class, Boolean.class);
        List<String> matchActivityIds = (List<String>)matchCreateRuleMethod.invoke(stageServiceExtension.newInstance(), masterTask.getTenantId(), masterTask.getId().toString(), taskItemList, masterTask.getSequence());
        for (Activity taskItem : taskItemList) {
            if(matchActivityIds.contains(taskItem.getId())){
                Task taskItemInstance = createTaskItem(userTaskImpl,(UserTask) taskItem, activityInstance, masterTask.getId().getInternal());
                taskItemInstance.setStageName(masterTask.getName());
                taskItemInstance.setStageId(masterTask.getStageId());
                taskItemInstance.setStageFieldApiName(masterTask.getStageFieldApiName());
                taskItemInstanceList.add(taskItemInstance);
            }
        }
        return taskItemInstanceList;
    }

    private static Task createTaskItem(UserTaskImpl userTaskImpl, UserTask userTaskActivityConfig, ActivityInstanceImpl parentActivityInstance, String parentTaskId) {
        Task task = setTaskBasicProperties(userTaskImpl,userTaskActivityConfig,parentActivityInstance);
        task.setOrderId(userTaskActivityConfig.getOrderId());//--独有
        task.setParentTaskId(parentTaskId);
        task.setVersion(FlowVersion.VERSION);
        if (userTaskActivityConfig.getProperties() != null) {
            task.setExtension((Map<String, Object>) userTaskActivityConfig.getProperties().get("extension"));
        }
        return task;
    }
    /**
     * 终结态是比较特殊的点
     * @param task
     * @param activity
     * @param terminalId
     */
    private void setStageId(Task task, UserTask activity, String terminalId) {
        if(StringUtils.isNotBlank(terminalId)&&activity.isTerminal()&&activity.getTerminalConfigs()!=null){
            activity.getTerminalConfigs().forEach(config->{
                if(terminalId.equals(config.getId())){
                    task.setStageId(config.getStageId());
                }
            });
        }else{
            task.setStageId(activity.getStageId());
        }
    }
    @Override
    public String getType(){
        return WorkflowConstants.WorkflowType.STAGE;
    }
}
