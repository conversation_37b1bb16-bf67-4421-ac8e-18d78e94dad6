package com.effektif.mongo;

import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.configuration.Brewable;
import com.effektif.workflow.impl.configuration.Brewery;
import com.effektif.workflow.impl.ext.blockTask.BlockTask;
import com.effektif.workflow.impl.ext.blockTask.BlockTaskStore;
import com.mongodb.BasicDBObject;
import org.bson.types.ObjectId;
import org.slf4j.Logger;

/**
 * 阻塞的自动节点 ， 与对象无关 ； 节点可以停止
 * 会支持 各种操作 及 对象操作的屏幕流 ， 与 SF 的流相对应
 * 910-920 专项
 * <AUTHOR>
 */
public class MongoBlockTaskStore implements BlockTaskStore, Brewable {

  public static final Logger log = MongoDb.log;

  protected MongoObjectMapper mongoMapper;
  private MongoDb mongoDb;
  private MongoConfiguration mongoConfiguration;

  @Override
  public void brew(Brewery brewery) {
    mongoDb = brewery.get(MongoDb.class);
    mongoConfiguration = brewery.get(MongoConfiguration.class);
    this.mongoMapper = brewery.get(MongoObjectMapper.class);
  }

  public BasicDBObject taskToMongo(BlockTask task) {
    return mongoMapper.write(task);
  }

  public <T extends BlockTask> T mongoToTask(BasicDBObject dbTask) {
    return mongoMapper.read(dbTask, BlockTask.class);
  }

  @Override
  public TaskId generateTaskId() {
    return new TaskId(new ObjectId().toString());
  }

  @Override
  public void insertTask(BlockTask task) {
    task.setModifyTime(System.currentTimeMillis());
    BasicDBObject dbTask = taskToMongo(task);
    this.getTasksCollection(task.getTenantId()).insert("insert-BlockTask", dbTask);
  }

  @Override
  public BlockTask completeTask(String tenantId,String taskId, String state) {
    BasicDBObject query = new Query()._id(taskId).get();
    BasicDBObject update = new Update().set(TaskFields.COMPLETED, true)
      .set(TaskFields.MODIFY_TIME, System.currentTimeMillis())
      .set(TaskFields.STATE, state)
      .get();
    BasicDBObject dbTask = this.getTasksCollection(tenantId).findAndModify("complete-latencyTask", query, update, null, null, false, false, false);
    return mongoToTask(dbTask);
  }

  private MongoCollection getTasksCollection(String tenantId) {
    return mongoDb.createCollection(tenantId,mongoConfiguration.getBlockTasksCollectionName());
  }
}
