package com.effektif.workflow.test.timer;

import java.util.Map;

import com.effektif.workflow.api.bpmn.BpmnElement;

import com.effektif.workflow.api.json.TypeName;

import com.effektif.workflow.api.workflow.Activity;
import com.effektif.workflow.impl.activity.AbstractActivityType;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;

import lombok.Data;

import com.effektif.workflow.api.bpmn.BpmnElement;
import com.effektif.workflow.api.json.TypeName;
import com.effektif.workflow.api.workflow.Activity;


@TypeName("JCActivityParam")
@BpmnElement("JCActivityParam")
@Data 
public class JCActivityParam extends Activity {
   /**
     * auto,task
     */
    protected String executeType;
    /**
     * 对已经支持的执行项进行兼容
     */
    protected Map itemMap;
    /**
     * 屏幕操作节点相关配置信息
     */    
 
}
