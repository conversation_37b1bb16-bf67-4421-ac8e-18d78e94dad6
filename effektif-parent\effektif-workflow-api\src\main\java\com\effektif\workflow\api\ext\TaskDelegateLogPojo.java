package com.effektif.workflow.api.ext;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


public class TaskDelegateLogPojo implements Serializable {

    private static final long serialVersionUID = 3694316149376916854L;


    /**
     * 委托人
     */
    protected String delegatePersonId;
    /**
     * 受托人
     */
    protected List<String> trusteePersonId;

    /**
     * 创建任务 还是重新解析
     * <p>
     * 0: 创建任务
     * 1: 刷新
     * 2: 更换处理人
     */
    protected int delegateActionType;

    protected long delegateTime;

    protected String userId;

    public String getDelegatePersonId() {
        return delegatePersonId;
    }

    public void setDelegatePersonId(String delegatePersonId) {
        this.delegatePersonId = delegatePersonId;
    }

    public List<String> getTrusteePersonId() {
        return trusteePersonId;
    }

    public void setTrusteePersonId(List<String> trusteePersonId) {
        this.trusteePersonId = trusteePersonId;
    }

    public static TaskDelegateLogPojo create(String delegatePersonId, List<String> trusteePersonId,int delegateActionType,String userId) {
        TaskDelegateLogPojo entity = new TaskDelegateLogPojo();
        entity.setDelegatePersonId(delegatePersonId);
        entity.setTrusteePersonId(trusteePersonId);
        entity.setDelegateActionType(delegateActionType);
        entity.setDelegateTime(System.currentTimeMillis());
        entity.setUserId(userId);
        return entity;
    }


    public static List<TaskDelegateLogPojo> transfer(Map<String, String> trusteeUserId,int delegateActionType,String userId) {
        List<TaskDelegateLogPojo> taskDelegateLogEntities = new ArrayList<>();
        if (trusteeUserId == null || trusteeUserId.isEmpty()) {
            return taskDelegateLogEntities;
        }
        trusteeUserId.forEach((key, value) -> {
            taskDelegateLogEntities.add(TaskDelegateLogPojo.create(key, Collections.singletonList(value),delegateActionType,userId));
        });
        return taskDelegateLogEntities;
    }

    public int getDelegateActionType() {
        return delegateActionType;
    }

    public void setDelegateActionType(int delegateActionType) {
        this.delegateActionType = delegateActionType;
    }

    public long getDelegateTime() {
        return delegateTime;
    }

    public void setDelegateTime(long delegateTime) {
        this.delegateTime = delegateTime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
