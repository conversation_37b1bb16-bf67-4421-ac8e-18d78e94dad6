// MongoDB 测试数据库初始化脚本

// 切换到测试数据库
db = db.getSiblingDB('ipaas_db_proxy_test');

// 创建测试用户
db.createUser({
  user: 'test_user',
  pwd: 'test_password',
  roles: [
    {
      role: 'readWrite',
      db: 'ipaas_db_proxy_test'
    }
  ]
});

// 创建测试集合
db.createCollection('test_documents');

// 插入一些初始测试数据
db.test_documents.insertMany([
  {
    name: 'Initial Test Document 1',
    description: 'This is the first test document for database connection testing',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Initial Test Document 2',
    description: 'This is the second test document for database connection testing',
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// 创建索引
db.test_documents.createIndex({ name: 1 });
db.test_documents.createIndex({ createdAt: 1 });

print('MongoDB test database initialized successfully!');
