package com.fxiaoke.erpdss.ipaas.dbproxy.dao;

import com.github.mongo.support.DatastoreExt;
import com.mongodb.MongoClient;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> (^_−)☆
 */
@Service
public class ErpIPaaSMongoDao {
    public static final String DB_NAME = "erp_ipaas";
    public static final String COMMON_TENANT_ID = "1";

    private DatastoreExt mongoDatastore;

    @Autowired
    public void setDatastoreExt(DatastoreExt mongoDatastore) {
        this.mongoDatastore = mongoDatastore;
    }

    public Set<String> listCollNames(String tenantId) {
        MongoClient mongo = getMongoClient(tenantId);
        return mongo.listDatabaseNames().into(new HashSet<>());
    }

    private MongoClient getMongoClient(String tenantId) {
        MongoClient mongo = mongoDatastore.setTenantId(tenantId).getMongo();
        return mongo;
    }
}
