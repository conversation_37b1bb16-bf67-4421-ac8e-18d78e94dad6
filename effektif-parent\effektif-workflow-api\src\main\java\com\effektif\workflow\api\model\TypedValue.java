/* Copyright (c) 2014, Effektif GmbH.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License. */
package com.effektif.workflow.api.model;

import com.effektif.workflow.api.json.JsonFieldName;
import com.effektif.workflow.api.types.DataType;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
public class TypedValue implements Serializable{

  private static final long serialVersionUID = 3356629924311054029L;
  protected Object value;
  @JsonFieldName("type")
  protected DataType dataType;

  public TypedValue() {
  }
  
  public TypedValue(Object value) {
    this.value = value;
  }
  
  public TypedValue(Object value, DataType dataType) {
    this.value = value;
    this.dataType = dataType;
  }

  public Object getValue() {
    return this.value;
  }
  public void setValue(Object value) {
    this.value = value;
  }

  public DataType getDataType() {
    return this.dataType;
  }
  public void setDataType(DataType dataType) {
    this.dataType = dataType;
  }

}
