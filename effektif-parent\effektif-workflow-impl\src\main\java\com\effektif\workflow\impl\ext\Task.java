package com.effektif.workflow.impl.ext;

import com.effektif.workflow.api.activities.ErrMsgI18N;
import com.effektif.workflow.api.activities.TimeoutExecution;
import com.effektif.workflow.api.ext.ExecutionPojo;
import com.effektif.workflow.api.ext.TaskDelegateLogPojo;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.model.StageTaskStateChangeRecord;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.api.workflow.Extensible;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * zhengh<PERSON>bo
 * 16/6/12 14:36
 */
public class Task extends Extensible implements Serializable {
  private static final long serialVersionUID = -237118055514253051L;
  protected TaskId id;
  protected String name;
  protected String description;
  protected String applicantId;
  /**
   * 外部提交人
   */
  protected String outerSubmitter;
  protected String applicantAccount;
  protected Long createTime;
  protected Long modifyTime;
  protected Long endTime;
  protected String assigneeId;
  protected List<String> assigneeIds;
  protected List<String> candidateIds;
  //是否在实例中更换过审批人
  protected Boolean assigneeChanged;
  //已废弃
  protected List<List<String>> assigneeChangeLog;
  //是否为审批例外人
  protected Boolean candidateEditable;
  protected Long dueDate;
  protected Boolean completed;
  protected Boolean canceled;

  protected String activityId;
  protected String activityInstanceId;
  protected Boolean activityNotify;
  protected String workflowInstanceId;
  protected String sourceWorkflowId;
  protected String workflowId;

  protected String tenantId;
  protected String appId;
  protected String entityId;
  protected String objectId;
  protected String deptId;
  protected Map<String, List<String>> assignee;
  /**
   * 函数解析人
   */
  protected Map<String,Object> assigneeFunction;
  protected String assigneeFunctionRequestId;
  protected String assigneeType;
  protected List<ApprovalOpinion> opinions;
  protected String actionType;
  protected Integer version;

  /**
   * 数据提交人(或上游对接人)
   */
  private String submitter;

  /**
   * 1:表示可以指定下一个审批人
   * 0或空：不可以
   */

  private Integer assignNextTask;

  /**
   * 1:是外部节点
   * 0或空：不是外部节点
   */

  private Integer externalApplyTask;

  /**
   * 1:表示该节点的审批人由上一节点指定
   * 0或空：不需要
   */

  private Integer candidateByPreTask;

  /**
   * 审批人员类别
   *
   * @see com.effektif.workflow.api.ext.WorkflowConstants.AssigneeType
   */
  protected String assignType;
  /**
   * task审批类型
   *
   * @see com.effektif.workflow.api.ext.WorkflowConstants.UserTaskType
   */
  protected String taskType;
  protected String state;
  protected Boolean remind;
  protected Object remindLatency;
  protected Integer latencyUnit;

  protected String workflowName;
  protected String workflowDescription;
  protected String errMsg;
  protected List<ErrMsgI18N> errMsgI18N;
  protected Map<String, Object> bpmExtension;
  //task提醒
  protected Object reminders;
  //860新版超时提醒
  protected Object remindersV2;
  //是否需要上一级审批
  protected Boolean demandSuperior;

  private String outerTenantField;

  /**
   * 是否允许加签
   */
  protected Boolean tagAllow;

  //0：上级审批；1：流程终止；2：指定审批人
  protected Integer demandBeyondAssignee;

  //是否启用企业互联
  protected boolean linkAppEnable;
  //企业互联应用id
  protected String linkApp;
  //企业互联应用名称
  protected String linkAppName;
  //企业互联应用的类型
  protected int linkAppType;
  //企业互联外部企业id
  protected String outerTenantId;
  /**
   * 是否允许跳过（阶段推进器使用）
   */
  protected Boolean skip;
  /**
   * 是否按照处理人列表顺序执行
   */
  protected Boolean sequence;

  /**
   * 任务所属流程类型
   */
  private String type;

  /**
   * 被加签节点任务id   task source Id
   */
  private TaskId masterTaskId;

  /**
   * 驳回之后再次提交是否从当前节点开始，开关
   */
  private Boolean enableMoveToCurrentActivityWhenReject;

  /**
   * 880 业务扩展人工元素
   */
  protected boolean custom=false;
  protected boolean customCandidateIds = false;
  protected String elementApiName;
  protected Map<String,Object> customExtension;

  protected String fromTaskId;
  protected String fromActivityId;
  protected String fromTaskName;

  protected String subState;

  protected boolean importObject = false;

  /**
   * 驳回并结束流程，重新提交后跳回至原节点
   */
  protected boolean parallelRejectedEndedThenResubmitAndReturn;

  /**
   * 驳回至非网关节点，处理后跳回至原节点
   */
  protected boolean parallelRejectedNonGatewayAndReturn;

  /**
   * 并行内被迫驳回的节点  之前的任务状态
   */
  protected String parallelRejectedBeforeState;

  /**
   * 是从哪个任务驳回到当前节点 910新增  只有并行内的节点 驳回到并行外的时候记录一下
   */
  protected String parallelRejectedToCurrentNodeTaskId;

  protected Map<String,Object> countersignStrategy;

  public boolean isCustom() {
    return custom;
  }

  public void setCustom(boolean custom) {
    this.custom = custom;
  }

  public boolean isCustomCandidateIds() {
    return customCandidateIds;
  }

  public void setCustomCandidateIds(boolean customCandidateIds) {
    this.customCandidateIds = customCandidateIds;
  }

  public void setImportObject(boolean importObject) {
    this.importObject = importObject;
  }

  public String getElementApiName() {
    return elementApiName;
  }

  public void setElementApiName(String elementApiName) {
    this.elementApiName = elementApiName;
  }

  public Map<String, Object> getCustomExtension() {
    return customExtension;
  }

  public void setCustomExtension(Map<String, Object> customExtension) {
    this.customExtension = customExtension;
  }

  public Integer getAllPassType() {
    return allPassType;
  }

  public void setAllPassType(Integer allPassType) {
    this.allPassType = allPassType;
  }

  /**
   * 会签时：
   * allPassType = 1 表示所有人会签人员都要进行一次操作，即使第一个人执行了reject，后面的人也要执行
   * allPassType = 0 如果第一个人执行了reject，则流程就终止，会签的其他人不需要执行。
   */

  private Integer allPassType;

  /**
   * 针对demandBeyondAssignee==2的情况
   */

  protected Map<String, List<String>> beyondAssignee;

  protected Map<String,List<String>> nextTaskAssigneeScope;

  protected Integer externalFlow;

  /**
   * 节点超时 执行动作
   */
  private List<TimeoutExecution> timeoutExecution;

  /**
   * 与发起人一致时,任务自动通过,审批流专用
   */
  private Boolean autoAgreeWhenEqualsWithApplicant;
  /**
   * 历史处理人与当前处理人一致 自动通过
   */
  private Boolean autoAgreeWhenHistorySame;

  private List<StageTaskStateChangeRecord> stateChangeRecord;

  public List<StageTaskStateChangeRecord> getStateChangeRecord() {
    return stateChangeRecord;
  }

  public void setStateChangeRecord(List<StageTaskStateChangeRecord> stateChangeRecord) {
    this.stateChangeRecord = stateChangeRecord;
  }

  public void setNextTaskAssigneeScope(Map<String, List<String>> nextTaskAssigneeScope) {
    this.nextTaskAssigneeScope = nextTaskAssigneeScope;
  }

  public Map<String, List<String>> getBeyondAssignee() {
    return beyondAssignee;
  }

  public void setBeyondAssignee(Map<String, List<String>> beyondAssignee) {
    this.beyondAssignee = beyondAssignee;
  }


  public Integer getDemandBeyondAssignee() {
    return demandBeyondAssignee;
  }

  public void setDemandBeyondAssignee(Integer demandBeyondAssignee) {
    this.demandBeyondAssignee = demandBeyondAssignee;
  }

  public Long getEndTime() {
    return endTime;
  }

  public void setEndTime(Long endTime) {
    this.endTime = endTime;
  }

  protected Map<String, List<ExecutionPojo>> execution;
  protected Object rule;
  protected Object rejectRule;
  protected Object functionRule;
  protected Object rejectFunctionRule;
  protected Map sourceTransition;

  /**
   * 任务审核通过之后需要进入的activityId
   */
  public String moveToActivityIdWhenComplete;

  /**
   * 委托记录
   */
  protected List<TaskDelegateLogPojo> taskDelegateLog;

  protected Boolean existsDataChangeLog;

  public String getMoveToActivityIdWhenComplete() {
    return moveToActivityIdWhenComplete;
  }

  public void setMoveToActivityIdWhenComplete(String moveToActivityIdWhenComplete) {
    this.moveToActivityIdWhenComplete = moveToActivityIdWhenComplete;
  }

  public Task name(String name) {
    this.name = name;
    return this;
  }

  public Task assigneeId(String assigneeId) {
    this.assigneeId = assigneeId;
    return this;
  }

  public String getEntityId() {
    return entityId;
  }

  public void setEntityId(String entityId) {
    this.entityId = entityId;
  }

  public Task candidateId(String candidateId) {
    if (this.candidateIds == null) {
      this.candidateIds = new ArrayList<>();
    }
    this.candidateIds.add(candidateId);
    return this;
  }

  public String getTenantId() {
    return tenantId;
  }

  public String getSubmitter(){
    return submitter;
  }


  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }

  public TaskId getId() {
    return id;
  }

  public void setId(TaskId id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public String getApplicantId() {
    return applicantId;
  }

  public void setApplicantId(String applicantId) {
    this.applicantId = applicantId;
  }
  public String getOuterSubmitter() {
    return outerSubmitter;
  }

  public void setOuterSubmitter(String outerSubmitter) {
    this.outerSubmitter = outerSubmitter;
  }
  public String getApplicantAccount() {
    return applicantAccount;
  }

  public void setApplicantAccount(String applicantAccount) {
    this.applicantAccount = applicantAccount;
  }

  public Long getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Long createTime) {
    this.createTime = createTime;
  }

  public String getAssigneeId() {
    return assigneeId;
  }

  public void setAssigneeId(String assigneeId) {
    this.assigneeId = assigneeId;
  }

  public List<String> getCandidateIds() {
    return candidateIds;
  }

  public void setCandidateIds(List<String> candidateIds) {
    this.candidateIds = candidateIds;
  }

  public Long getDueDate() {
    return dueDate;
  }

  public void setDueDate(Long dueDate) {
    this.dueDate = dueDate;
  }

  public Long getModifyTime() {
    return modifyTime;
  }

  public void setModifyTime(Long modifyTime) {
    this.modifyTime = modifyTime;
  }

  public Boolean getCanceled() {
    return canceled;
  }

  public void setCanceled(Boolean canceled) {
    this.canceled = canceled;
  }

  public Boolean getCompleted() {
    return completed;
  }

  public void setCompleted(Boolean completed) {
    this.completed = completed;
  }

  public String getActivityId() {
    return activityId;
  }

  public void setActivityId(String activityId) {
    this.activityId = activityId;
  }

  public String getActivityInstanceId() {
    return activityInstanceId;
  }

  public void setActivityInstanceId(String activityInstanceId) {
    this.activityInstanceId = activityInstanceId;
  }

  public Boolean getActivityNotify() {
    return activityNotify;
  }

  public void setActivityNotify(Boolean activityNotify) {
    this.activityNotify = activityNotify;
  }

  public String getWorkflowInstanceId() {
    return workflowInstanceId;
  }

  public void setWorkflowInstanceId(String workflowInstanceId) {
    this.workflowInstanceId = workflowInstanceId;
  }

  public String getSourceWorkflowId() {
    return sourceWorkflowId;
  }

  public void setSourceWorkflowId(String sourceWorkflowId) {
    this.sourceWorkflowId = sourceWorkflowId;
  }

  public String getWorkflowId() {
    return workflowId;
  }

  public void setWorkflowId(String workflowId) {
    this.workflowId = workflowId;
  }

  public String getAssignType() {
    return assignType;
  }

  public void setAssignType(String assignType) {
    this.assignType = assignType;
  }

  public String getDeptId() {
    return deptId;
  }

  public void setDeptId(String deptId) {
    this.deptId = deptId;
  }

  public String getObjectId() {
    return objectId;
  }

  public void setObjectId(String objectId) {
    this.objectId = objectId;
  }

  public String getActionType() {
    return actionType;
  }

  public void setActionType(String actionType) {
    this.actionType = actionType;
  }

  public Integer getVersion() {
    return version;
  }

  public void setVersion(Integer version) {
    this.version = version;
  }

  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public String getState() {
    return state;
  }

  public void setState(String state) {
    this.state = state;
  }

  public String getTaskType() {
    return taskType;
  }

  public void setTaskType(String taskType) {
    this.taskType = taskType;
  }

  public Map<String, List<String>> getAssignee() {
    return assignee;
  }

  public void setAssignee(Map<String, List<String>> assignee) {
    this.assignee = assignee;
  }

  public List<ApprovalOpinion> getOpinions() {
    return opinions;
  }

  public void setOpinions(List<ApprovalOpinion> opinions) {
    this.opinions = opinions;
  }

  public Boolean getRemind() {
    return remind;
  }

  public void setRemind(Boolean remind) {
    this.remind = remind;
  }

  public Object getRemindLatency() {
    return remindLatency;
  }

  public void setRemindLatency(Object remindLatency) {
    this.remindLatency = remindLatency;
  }

  public Integer getLatencyUnit() {
    return latencyUnit;
  }

  public void setLatencyUnit(Integer latencyUnit) {
    this.latencyUnit = latencyUnit;
  }

  public Map<String, Object> getBpmExtension() {
    return bpmExtension;
  }

  public void setBpmExtension(Map<String, Object> bpmExtension) {
    this.bpmExtension = bpmExtension;
  }

  public String getWorkflowName() {
    return workflowName;
  }

  public void setWorkflowName(String workflowName) {
    this.workflowName = workflowName;
  }

  public String getWorkflowDescription() {
    return workflowDescription;
  }

  public void setWorkflowDescription(String workflowDescription) {
    this.workflowDescription = workflowDescription;
  }

  public String getErrMsg() {
    return errMsg;
  }

  public void setErrMsg(String errMsg) {
    this.errMsg = errMsg;
  }

  public List<String> getAssigneeIds() {
    return assigneeIds;
  }

  public void setAssigneeIds(List<String> assigneeIds) {
    this.assigneeIds = assigneeIds;
  }

  public Boolean getAssigneeChanged() {
    return assigneeChanged;
  }

  public void setAssigneeChanged(Boolean assigneeChanged) {
    this.assigneeChanged = assigneeChanged;
  }

  public Object getReminders() {
    return reminders;
  }

  public void setReminders(Object reminders) {
    this.reminders = reminders;
  }

  public Object getRemindersV2() {
    return remindersV2;
  }

  public void setRemindersV2(Object remindersV2) {
    this.remindersV2 = remindersV2;
  }

  public List<List<String>> getAssigneeChangeLog() {
    return assigneeChangeLog;
  }

  public void setAssigneeChangeLog(List<List<String>> assigneeChangeLog) {
    this.assigneeChangeLog = assigneeChangeLog;
  }

  public Map<String, List<ExecutionPojo>> getExecution() {
    return execution;
  }

  public void setExecution(Map<String, List<ExecutionPojo>> execution) {
    this.execution = execution;
  }

  public Object getRule() {
    return rule;
  }

  public void setRule(Object rule) {
    this.rule = rule;
  }

  public Object getRejectRule() {
    return rejectRule;
  }

  public void setRejectRule(Object rejectRule) {
    this.rejectRule = rejectRule;
  }

  public Boolean getCandidateEditable() {
    return candidateEditable;
  }

  public void setCandidateEditable(Boolean candidateEditable) {
    this.candidateEditable = candidateEditable;
  }

  public Map getSourceTransition() {
    return sourceTransition;
  }

  public void setSourceTransition(Map sourceTransition) {
    this.sourceTransition = sourceTransition;
  }

  public Boolean getDemandSuperior() {
    return demandSuperior;
  }

  public void setDemandSuperior(Boolean demandSuperior) {
    this.demandSuperior = demandSuperior;
  }

  public Boolean getTagAllow(){
    return tagAllow;
  }

  public void setTagAllow(Boolean tagAllow){
    this.tagAllow = tagAllow;
  }

  public Integer getAssignNextTask() {
    return assignNextTask;
  }

  public void setAssignNextTask(Integer assignNextTask) {
    this.assignNextTask = assignNextTask;
  }

  public Integer getExternalApplyTask() {
    return externalApplyTask;
  }

  public Integer getCandidateByPreTask() {
    return candidateByPreTask;
  }

  public void setCandidateByPreTask(Integer candidateByPreTask) {
    this.candidateByPreTask = candidateByPreTask;
  }

  public void setExternalApplyTask(Integer externalApplyTask) {
    this.externalApplyTask = externalApplyTask;
  }

  public void setSubmitter(String submitter){
    this.submitter=submitter;
  }

  protected  String parentTaskId;

  protected  String stageId;

  public String getParentTaskId() {
    return parentTaskId;
  }

  public void setParentTaskId(String parentTaskId) {
    this.parentTaskId = parentTaskId;
  }

  public String getStageId() {
    return stageId;
  }

  public void setStageId(String stageId) {
    this.stageId = stageId;
  }

  private Map<String, Object> extension;

  public Map<String, Object> getExtension() {
    return extension;
  }

  public void setExtension(Map<String, Object> extension) {
    this.extension = extension;
  }

  protected String stageFieldApiName;

  public String getStageFieldApiName() {
    return stageFieldApiName;
  }

  public void setStageFieldApiName(String stageFieldApiName) {
    this.stageFieldApiName = stageFieldApiName;
  }

  private Integer orderId;

  public Integer getOrderId() {
    return orderId;
  }

  public void setOrderId(Integer orderId) {
    this.orderId = orderId;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  private String stageName;

  public String getStageName() {
    return stageName;
  }

  public void setStageName(String stageName) {
    this.stageName = stageName;
  }

  private boolean terminal;

  public boolean isTerminal() {
    return terminal;
  }

  public void setTerminal(boolean terminal) {
    this.terminal = terminal;
  }

  private String nodeType;

  public String getNodeType() {
    return nodeType;
  }

  public void setNodeType(String nodeType) {
    this.nodeType = nodeType;
  }

  private String beforeSkipOutApprovalId;

  public String getBeforeSkipOutApprovalId() {
    return beforeSkipOutApprovalId;
  }

  public void setBeforeSkipOutApprovalId(String beforeSkipOutApprovalId) {
    this.beforeSkipOutApprovalId = beforeSkipOutApprovalId;
  }

  public Integer isAutoAgree;

  public Integer getIsAutoAgree() {
    return isAutoAgree;
  }

  public void setIsAutoAgree(Integer isAutoAgree) {
    this.isAutoAgree = isAutoAgree;
  }

  public boolean isLinkAppEnable() {
    return linkAppEnable;
  }

  public void setLinkAppEnable(boolean linkAppEnable) {
    this.linkAppEnable = linkAppEnable;
  }

  public String getLinkApp() {
    return linkApp;
  }

  public void setLinkApp(String linkApp) {
    this.linkApp = linkApp;
  }

  public String getLinkAppName() {
    return linkAppName;
  }

  public void setLinkAppName(String linkAppName) {
    this.linkAppName = linkAppName;
  }

  public int getLinkAppType() {
    return linkAppType;
  }

  public void setLinkAppType(int linkAppType) {
    this.linkAppType = linkAppType;
  }

  public Boolean getSkip() {
    return skip;
  }

  public void setSkip(Boolean skip) {
    this.skip = skip;
  }

  public void setSequence(Boolean sequence){
    this.sequence = sequence;
  }

  public Boolean getSequence(){
    return Boolean.TRUE.equals(sequence);
  }

  public TaskId getMasterTaskId() {
    return masterTaskId;
  }

  public void setMasterTaskId(TaskId masterTaskId) {
    this.masterTaskId = masterTaskId;
  }

  public String getOuterTenantField() {
    return outerTenantField;
  }

  public void setOuterTenantField(String outerTenantField) {
    this.outerTenantField = outerTenantField;
  }

  public Object getFunctionRule() {
    return functionRule;
  }

  public void setFunctionRule(Object functionRule) {
    this.functionRule = functionRule;
  }

  public Object getRejectFunctionRule() {
    return rejectFunctionRule;
  }

  public void setRejectFunctionRule(Object rejectFunctionRule) {
    this.rejectFunctionRule = rejectFunctionRule;
  }

  public Boolean getEnableMoveToCurrentActivityWhenReject() {
    return enableMoveToCurrentActivityWhenReject;
  }

  public void setEnableMoveToCurrentActivityWhenReject(Boolean enableMoveToCurrentActivityWhenReject) {
    this.enableMoveToCurrentActivityWhenReject = enableMoveToCurrentActivityWhenReject;
  }

  public Integer getExternalFlow() {
    return externalFlow;
  }

  public void setExternalFlow(Integer externalFlow) {
    this.externalFlow = externalFlow;
  }

  public List<TimeoutExecution> getTimeoutExecution() {
    return timeoutExecution;
  }

  public void setTimeoutExecution(List<TimeoutExecution> timeoutExecution) {
    this.timeoutExecution = timeoutExecution;
  }

  public boolean allProcessed(){
    boolean allProcessed = true;
    if(WorkflowConstants.UserTaskType.ALL_PASS.equals(taskType)){
      List<String> personList = assignee.get(WorkflowConstants.AssigneeType.PERSON_LIST);
      List<String> processedUsers =opinions.stream()
              .map(ApprovalOpinion::getUserId)
              .collect(Collectors.toList());
      allProcessed  = processedUsers.containsAll(personList);
    }
    return allProcessed;
  }

  public Boolean getAutoAgreeWhenEqualsWithApplicant() {
    return autoAgreeWhenEqualsWithApplicant;
  }

  public void setAutoAgreeWhenEqualsWithApplicant(Boolean autoAgreeWhenEqualsWithApplicant) {
    this.autoAgreeWhenEqualsWithApplicant = autoAgreeWhenEqualsWithApplicant;
  }

  public Boolean getAutoAgreeWhenHistorySame() {
    return autoAgreeWhenHistorySame;
  }

  public void setAutoAgreeWhenHistorySame(Boolean autoAgreeWhenHistorySame) {
    this.autoAgreeWhenHistorySame = autoAgreeWhenHistorySame;
  }

  public Map<String, Object> getAssigneeFunction() {
    return assigneeFunction;
  }

  public void setAssigneeFunction(Map<String, Object> assigneeFunction) {
    this.assigneeFunction = assigneeFunction;
  }

  public String getAssigneeType() {
    return assigneeType;
  }

  public void setAssigneeType(String assigneeType) {
    this.assigneeType = assigneeType;
  }
  public boolean isAssigneeFunction(){
    return WorkflowConstants.AssigneeType.ASSIGNEE_FUNCTION.equals(assigneeType);
  }

  public Boolean needCreateAfterTagTask(){
    if(Objects.isNull(this.opinions)) {
      return Boolean.FALSE;
    }
    return this.opinions.stream().anyMatch(item -> WorkflowConstants.NodeType.TAG_AFTER.equals(item.getActionType()) && Boolean.TRUE.equals(item.getNeedAfterTag()));
  }

  public List<TaskDelegateLogPojo> getTaskDelegateLog() {
    return taskDelegateLog;
  }

  public void setTaskDelegateLog(List<TaskDelegateLogPojo> taskDelegateLog) {
    this.taskDelegateLog = taskDelegateLog;
  }

  public List<ErrMsgI18N> getErrMsgI18N() {
    return errMsgI18N;
  }

  public void setErrMsgI18N(List<ErrMsgI18N> errMsgI18N) {
    this.errMsgI18N = errMsgI18N;
  }

  public Map<String, List<String>> getNextTaskAssigneeScope() {
    return nextTaskAssigneeScope;
  }

  public Boolean getExistsDataChangeLog() {
    return existsDataChangeLog;
  }

  public void setExistsDataChangeLog(Boolean existsDataChangeLog) {
    this.existsDataChangeLog = existsDataChangeLog;
  }

  public String getFromTaskId() {
    return fromTaskId;
  }

  public void setFromTaskId(String fromTaskId) {
    this.fromTaskId = fromTaskId;
  }

  public String getFromActivityId() {
    return fromActivityId;
  }

  public void setFromActivityId(String fromActivityId) {
    this.fromActivityId = fromActivityId;
  }

  public String getFromTaskName() {
    return fromTaskName;
  }

  public void setFromTaskName(String fromTaskName) {
    this.fromTaskName = fromTaskName;
  }

  public String getSubState() {
    return subState;
  }

  public void setSubState(String subState) {
    this.subState = subState;
  }

  public boolean isParallelRejectedEndedThenResubmitAndReturn() {
    return parallelRejectedEndedThenResubmitAndReturn;
  }

  public void setParallelRejectedEndedThenResubmitAndReturn(boolean parallelRejectedEndedThenResubmitAndReturn) {
    this.parallelRejectedEndedThenResubmitAndReturn = parallelRejectedEndedThenResubmitAndReturn;
  }

  public boolean isParallelRejectedNonGatewayAndReturn() {
    return parallelRejectedNonGatewayAndReturn;
  }

  public void setParallelRejectedNonGatewayAndReturn(boolean parallelRejectedNonGatewayAndReturn) {
    this.parallelRejectedNonGatewayAndReturn = parallelRejectedNonGatewayAndReturn;
  }

  public String getParallelRejectedBeforeState() {
    return parallelRejectedBeforeState;
  }

  public void setParallelRejectedBeforeState(String parallelRejectedBeforeState) {
    this.parallelRejectedBeforeState = parallelRejectedBeforeState;
  }

  public String getParallelRejectedToCurrentNodeTaskId() {
    return parallelRejectedToCurrentNodeTaskId;
  }

  public void setParallelRejectedToCurrentNodeTaskId(String parallelRejectedToCurrentNodeTaskId) {
    this.parallelRejectedToCurrentNodeTaskId = parallelRejectedToCurrentNodeTaskId;
  }

  public String getOuterTenantId() {
    return outerTenantId;
  }

  public void setOuterTenantId(String outerTenantId) {
    this.outerTenantId = outerTenantId;
  }

  /**
   * 判断当前任务是否在并行内,前提需要先设置extension,否则返回false
   *
   * @return
   */
  public boolean inParallelGateWay() {
    return StringUtils.isNotEmpty(getParallelGateWayId());
  }

  public String getParallelGateWayId() {
    Map<String, Object> extension = getExtension();
    if (Objects.nonNull(extension)) {
      return (String) extension.get(WorkflowConstants.PARALLEL_GATEWAY_ID);
    }
    return null;
  }

  public String getAssigneeFunctionRequestId() {
    return assigneeFunctionRequestId;
  }

  public void setAssigneeFunctionRequestId(String assigneeFunctionRequestId) {
    this.assigneeFunctionRequestId = assigneeFunctionRequestId;
  }

  public Map<String, Object> getCountersignStrategy() {
    return countersignStrategy;
  }

  public void setCountersignStrategy(Map<String, Object> countersignStrategy) {
    this.countersignStrategy = countersignStrategy;
  }
}
