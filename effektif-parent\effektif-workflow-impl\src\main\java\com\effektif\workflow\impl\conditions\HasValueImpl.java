/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.impl.conditions;

import com.effektif.workflow.api.condition.Condition;
import com.effektif.workflow.api.condition.HasValue;

import java.util.List;


/**
 * <AUTHOR>
 */
public class HasValueImpl extends SingleBindingConditionImpl<HasValue> {
  
  @Override
  public Class< ? extends Condition> getApiType() {
    return HasValue.class;
  }
  
  @Override
  public boolean eval(Object value) {
    if (value instanceof String) {
      return value != null && !"".equals(value);
    }
    if(value instanceof List){
      return ((List) value).size()!=0;
    }
    return value!=null;
  }

  @Override
  public String getComparatorSymbol() {
    return "<hasValue>";
  }
}
