---
type: "agent_requested"
description: "service interface 定义"
---

# iPaaS 项目开发规则

## Service Interface 定义规则

- 必须返回 `com.fxiaoke.erpdss.ipaas.common.module.Result`

## 异常处理规范

- 捕获未知类型异常必须打印堆栈
- 捕获异常后，如果不是可以忽略的，或者继续向上抛出异常的，都必须打印日志方便定位
- 优先使用项目定义的业务异常类（如 `IPaaSBizException`、`IPaaSSystemException`）
- 异常信息应该清晰描述问题和上下文
- 使用国际化消息

## 日志记录规范

- 使用 SLF4J 进行日志记录
- 合理选择日志级别
- 避免在循环中打印大量日志
- 使用占位符而不是字符串拼接
- 日志使用英文描述

## 代码示例

### 推荐的异常处理

```java
try {
    // 业务逻辑
} catch (JsonProcessingException e) {
    log.error("json process error: {}", data, e);
    throw new IPaaSSystemException("数据序列化失败", e);
}
```

### 推荐的日志记录

```java
log.info("user{}login success，IP地址：{}", username, ipAddress);
log.error("An error occurred while processing order {}", orderId, e);
```

### 推荐的异常抛出

```java
if (user == null) {
    throw new IPaaSBizException(ResultCode.PARAM_ILLEGAL.getI18nMsg());
}
```

## 测试规范

### 测试类型选择

- **默认生成单元测试**: 除非特别说明，生成代码时只需要生成单元测试（`*Test.java`），不需要生成集成测试
- **单元测试**: 测试单个类或方法的功能，使用模拟对象隔离依赖
- **集成测试**: 仅在明确要求时生成，用于测试多个组件之间的交互

### 测试命名规范

- **单元测试**: `*Test.java` (如 `UserServiceTest.java`)
- **集成测试**: `*IT.java` (如 `CheckControllerIT.java`)

### 测试技术栈

- **JUnit 5**: 测试框架
- **Mockito**: 用于创建模拟对象
- **AssertJ**: 提供流畅的断言API
- **Spring Boot Test**: 集成测试时使用

### 测试编写规范

- 遵循 AAA (Arrange-Act-Assert) 模式
- 每个测试方法只测试一个功能点
- 使用描述性的测试方法命名
- 包含正常流程、边界条件和异常流程测试
- 使用 `@Mock` 和 `@InjectMocks` 注解进行依赖注入

### 测试示例

#### 单元测试示例

```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private UserService userService;

    @Test
    void shouldReturnUserWhenValidId() {
        // Arrange
        Long userId = 1L;
        User expectedUser = new User(userId, "testUser");
        when(userRepository.findById(userId)).thenReturn(Optional.of(expectedUser));

        // Act
        Result<User> result = userService.getUserById(userId);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo(expectedUser);
    }

    @Test
    void shouldThrowExceptionWhenUserNotFound() {
        // Arrange
        Long userId = 999L;
        when(userRepository.findById(userId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThatThrownBy(() -> userService.getUserById(userId))
            .isInstanceOf(IPaaSBizException.class)
            .hasMessageContaining("用户不存在");
    }
}
```
