/*
 * Copyright 2014 Effektif GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.effektif.workflow.test.impl;

import com.effektif.workflow.api.condition.*;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.types.*;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.impl.WorkflowEngineImpl;
import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.conditions.ConditionImpl;
import com.effektif.workflow.impl.conditions.ConditionService;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import com.effektif.workflow.test.WorkflowTest;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR> Baeyens
 */
public class ConditionsTest extends WorkflowTest {

  private static final ChoiceType CHOICE = new ChoiceType().option("a").option("b").option("c");

  @Test
  public void testBooleanIsTrue() {
    assertTrue(evaluate(BooleanType.INSTANCE, "p", true, new IsTrue().leftExpression("p")));
    assertFalse(evaluate(BooleanType.INSTANCE, "p", false, new IsTrue().leftExpression("p")));
    assertFalse(evaluate(BooleanType.INSTANCE, "p", null, new IsTrue().leftExpression("p")));
  }

  @Test
  public void testBooleanIsFalse() {
    assertFalse(evaluate(BooleanType.INSTANCE, "p", true, new IsFalse().leftExpression("p")));
    assertTrue(evaluate(BooleanType.INSTANCE, "p", false, new IsFalse().leftExpression("p")));
    assertFalse(evaluate(BooleanType.INSTANCE, "p", null, new IsFalse().leftExpression("p")));
  }

  @Test
  public void testChoiceHasValue() {
    assertTrue(evaluate(CHOICE, "v", "b", new HasValue().leftExpression("v")));
    assertFalse(evaluate(CHOICE, "v", null, new HasValue().leftExpression("v")));
  }

  @Test
  public void testChoiceHasNoValue() {
    assertFalse(evaluate(CHOICE, "v", "b", new HasNoValue().leftExpression("v")));
    assertTrue(evaluate(CHOICE, "v", null, new HasNoValue().leftExpression("v")));
  }


  @Test
  public void testWorkflowChoiceIsChanged() {
    assertFalse(evaluate(CHOICE, "v", "b", new IsChanged().leftExpression("v")));
    assertFalse(evaluate(CHOICE, "v", null, new IsChanged().leftExpression("v")));
    assertFalse(evaluate(CHOICE, "c", null, new IsChanged().leftExpression("c"),null));

    Map<String,Object> data = Maps.newHashMap();
    data.put("v","333");
    Map<String,Object> beforeData = Maps.newHashMap();
    beforeData.put("before",data);

    Map<String,Object> workflowInstanceBinding = Maps.newHashMap();
    workflowInstanceBinding.put(WorkflowBindingEnum.type.name(), WorkflowConstants.WorkflowType.WORKFLOW);

    assertTrue(evaluate(CHOICE, "v", null, new IsChanged().leftExpression("v"),beforeData,workflowInstanceBinding));
  }


  @Test
  public void testApprovalChoiceIsChanged() {
    assertFalse(evaluate(CHOICE, "v", "b", new IsChanged().leftExpression("v")));
    assertFalse(evaluate(CHOICE, "v", null, new IsChanged().leftExpression("v")));
    assertFalse(evaluate(CHOICE, "c", null, new IsChanged().leftExpression("c"), null));

    Map<String, Object> data = Maps.newHashMap();
    data.put("UDImg3__c", "333");
    data.put("UDAttach3__c", "243423");

    Map<String, Object> workflowInstanceBinding = Maps.newHashMap();
    workflowInstanceBinding.put(WorkflowBindingEnum.type.name(), WorkflowConstants.WorkflowType.APPROVAL_FLOW);

    assertFalse(evaluate(CHOICE, "v", null, new IsChanged().leftExpression("v"), data, workflowInstanceBinding));
    assertTrue(evaluate(CHOICE, "UDImg3__c", null, new IsChanged().leftExpression("UDImg3__c"), data, workflowInstanceBinding));
    assertTrue(evaluate(CHOICE, "UDAttach3__c", null, new IsChanged().leftExpression("UDAttach3__c"), data, workflowInstanceBinding));

  }

  @Test
  public void testChoiceEquals() {
    assertFalse(evaluate(CHOICE, "v", "b", new Equals().leftExpression("v").rightValue("a")));
    assertTrue(evaluate(CHOICE, "v", "b", new Equals().leftExpression("v").rightValue("b")));
    assertFalse(evaluate(CHOICE, "v", "b", new Equals().leftExpression("v").rightValue(null)));
  }

  @Test
  public void testChoiceNotEquals() {
    assertTrue(evaluate(CHOICE, "v", "b", new NotEquals().leftExpression("v").rightValue("a")));
    assertFalse(evaluate(CHOICE, "v", "b", new NotEquals().leftExpression("v").rightValue("b")));
    assertTrue(evaluate(CHOICE, "v", "b", new NotEquals().leftExpression("v").rightValue(null)));
  }

  @Test
  public void checkOnlineData() {
    //1648180228640 张兵  (field_7ao15__c=欧洲区)
    Or or = new Or();
    or.condition(new Equals().leftExpression("field_7ao15__c").rightValue("欧洲区"));
    or.condition(new Equals().leftExpression("field_cgc02__c").rightValue("Instant Brands Inc"));
    or.condition(new Equals().leftExpression("field_cgc02__c").rightValue("Best Buy China Ltd"));
    or.condition(new Equals().leftExpression("field_cgc02__c").rightValue("Instant Brands LLC"));
    or.condition(new Equals().leftExpression("field_cgc02__c").rightValue("STANLEY BLACK & DECKER ASIA HOLDINGS, LLC,MACAO BRANCH"));
    or.condition(new Equals().leftExpression("field_cgc02__c").rightValue("Caliber Brands FZE"));


    System.out.println(evaluate(Lists.newArrayList(
            VariableData.of(TextType.INSTANCE, "field_cgc02__c", "Instant Brands Inc"),
            VariableData.of(TextType.INSTANCE, "field_7ao15__c", "美洲区")
    ), or, Maps.newHashMap(), Maps.newHashMap()));


    //1648083579447 马炫  (field_7ao15__c=美洲区)
    And and = new And();
    and.condition(new Equals().leftExpression("field_7ao15__c").rightValue("美洲区"));
    and.condition(new NotEquals().leftExpression("field_cgc02__c").rightValue("Instant Brands Inc"));
    and.condition(new NotEquals().leftExpression("field_cgc02__c").rightValue("Best Buy China Ltd"));
    and.condition(new NotEquals().leftExpression("field_cgc02__c").rightValue("Etekcity Corporation"));
    and.condition(new NotEquals().leftExpression("field_cgc02__c").rightValue("YOOWO CO.,LIMITED"));
    and.condition(new NotEquals().leftExpression("field_cgc02__c").rightValue("Instant Brands LLC"));


    Or or_ = new Or();
    or_.condition(and);

    System.out.println((evaluate(Lists.newArrayList(
            VariableData.of(TextType.INSTANCE, "field_cgc02__c", "Instant Brands Inc"),
            VariableData.of(TextType.INSTANCE, "field_7ao15__c", "美洲区")
    ), or_, Maps.newHashMap(), Maps.newHashMap())));
  }

  @Test
  public void testComparatorInvalidNumbers() {
    assertFalse(evaluate(NumberType.INSTANCE, "n", 42, new GreaterThan().leftExpression("n").rightValue("x")));
    assertFalse(evaluate(NumberType.INSTANCE, "n", 42, new GreaterThan().leftExpression("n").rightValue(null)));
  }

  @Test
  public void testNumberEquals() {
    assertTrue(evaluateNumberExpression(new Equals(), 42, 42));
    assertFalse(evaluateNumberExpression(new Equals(), 42, 0));
    assertFalse(evaluateNumberExpression(new Equals(), null, 42));
  }

  @Test
  public void testNumberNotEquals() {
    assertFalse(evaluateNumberExpression(new NotEquals(), 42, 42));
    assertTrue(evaluateNumberExpression(new NotEquals(), 42, 0));
    assertTrue(evaluateNumberExpression(new NotEquals(), null, 42));
  }

  @Test
  public void testNumberGreaterThanExpression() {
    assertFalse(evaluateNumberExpression(new GreaterThan(), 99.0, 100.0));
    assertFalse(evaluateNumberExpression(new GreaterThan(), 99.0, 99.0));
    assertTrue(evaluateNumberExpression(new GreaterThan(), 99.0, 98.0));
  }

  @Test
  public void testNumberGreaterThanOrEqualExpression() {
    assertFalse(evaluateNumberExpression(new GreaterThanOrEqual(), 99.0, 100.0));
    assertTrue(evaluateNumberExpression(new GreaterThanOrEqual(), 99.0, 99.0));
    assertTrue(evaluateNumberExpression(new GreaterThanOrEqual(), 99.0, 98.0));
  }

  @Test
  public void testNumberHasValue() {
    assertTrue(evaluate(NumberType.INSTANCE, "n", 42, new HasValue().leftExpression("n")));
    assertFalse(evaluate(NumberType.INSTANCE, "n", null, new HasValue().leftExpression("n")));
  }

  @Test
  public void testNumberHasNoValue() {
    assertFalse(evaluate(NumberType.INSTANCE, "n", 42, new HasNoValue().leftExpression("n")));
    assertTrue(evaluate(NumberType.INSTANCE, "n", null, new HasNoValue().leftExpression("n")));
  }

  @Test
  public void testNumberLessThanExpression() {
    assertTrue(evaluateNumberExpression(new LessThan(), 99.0, 100.0));
    assertFalse(evaluateNumberExpression(new LessThan(), 99.0, 99.0));
    assertFalse(evaluateNumberExpression(new LessThan(), 99.0, 98.0));
  }

  @Test
  public void testNumberLessThanOrEqualExpression() {
    assertTrue(evaluateNumberExpression(new LessThanOrEqual(), 99.0, 100.0));
    assertTrue(evaluateNumberExpression(new LessThanOrEqual(), 99.0, 99.0));
    assertFalse(evaluateNumberExpression(new LessThanOrEqual(), 99.0, 98.0));
  }

  @Test
  public void testTextContains() {
    assertTrue(evaluateTextExpression(new Contains(), "hello", "hello"));
    assertFalse(evaluateTextExpression(new Contains(), "hello", "Hello"));
    assertFalse(evaluateTextExpression(new Contains(), "hello", "by"));
    assertTrue(evaluateTextExpression(new Contains(), "hello", "ell"));
    assertTrue(evaluateTextExpression(new Contains(), "hello", "hell"));
    assertTrue(evaluateTextExpression(new Contains(), "hello", "ello"));
    assertTrue(evaluateTextExpression(new Contains(), "Saudi Arabia", "Saudi Arabia"));
    assertFalse(evaluateTextExpression(new Contains(), null, "hello"));
  }

  @Test
  public void testTextContainsMultiLine() {
    assertTrue(evaluateTextExpression(new Contains(), "hello\nworld", "hello"));
    assertFalse(evaluateTextExpression(new Contains(), "hello\nworld", "Hello"));
    assertTrue(evaluateTextExpression(new Contains(), "hello\nworld\nGOTO 10", "hello\nworld"));
    assertFalse(evaluateTextExpression(new Contains(), "hello\nworld\nGOTO 10", "Hello\nworld"));
  }

  @Test
  public void testTextContainsIgnoreCase() {
    assertTrue(evaluateTextExpression(new ContainsIgnoreCase(), "HELLO", "hello"));
    assertTrue(evaluateTextExpression(new ContainsIgnoreCase(), "HELLO", "Hello"));
    assertFalse(evaluateTextExpression(new ContainsIgnoreCase(), "HELLO", "by"));
    assertTrue(evaluateTextExpression(new ContainsIgnoreCase(), "HELLO", "ell"));
    assertTrue(evaluateTextExpression(new ContainsIgnoreCase(), "HELLO", "hell"));
    assertTrue(evaluateTextExpression(new ContainsIgnoreCase(), "HELLO", "ello"));
    assertFalse(evaluateTextExpression(new ContainsIgnoreCase(), null, "hello"));
  }

  @Test
  public void testTextContainsIgnoreCaseMultiLine() {
    assertTrue(evaluateTextExpression(new ContainsIgnoreCase(), "hello\nworld", "hello"));
    assertTrue(evaluateTextExpression(new ContainsIgnoreCase(), "hello\nworld", "Hello"));
    assertTrue(evaluateTextExpression(new ContainsIgnoreCase(), "hello\nworld\nGOTO 10", "Hello\nworld"));
    assertTrue(evaluateTextExpression(new ContainsIgnoreCase(), "hello\nworld\nGOTO 10", "Hello\nworld"));
  }

  @Test
  public void testTextEquals() {
    assertTrue(evaluateTextExpression(new Equals(), "hello", "hello"));
    assertFalse(evaluateTextExpression(new Equals(), "hello", "Hello"));
    assertFalse(evaluateTextExpression(new Equals(), "hello", "by"));
    assertFalse(evaluateTextExpression(new Equals(), null, "hello"));
  }

  @Test
  public void testTextEqualsMultiLine() {
    assertTrue(evaluateTextExpression(new Equals(), "hello\nworld", "hello\nworld"));
    assertFalse(evaluateTextExpression(new Equals(), "hello\nworld", "Hello\nWorld"));
    assertFalse(evaluateTextExpression(new Equals(), "hello\nworld", "hello world"));
    assertFalse(evaluateTextExpression(new Equals(), "hello\nworld", "helloworld"));
  }

  @Test
  public void testTextEqualsIgnoreCase() {
    assertTrue(evaluateTextExpression(new EqualsIgnoreCase(), "hello", "hello"));
    assertTrue(evaluateTextExpression(new EqualsIgnoreCase(), "hello", "Hello"));
    assertFalse(evaluateTextExpression(new EqualsIgnoreCase(), "hello", "by"));
    assertFalse(evaluateTextExpression(new EqualsIgnoreCase(), null, "hello"));
  }

  @Test
  public void testTextEqualsIgnoreCaseMultiLine() {
    assertTrue(evaluateTextExpression(new EqualsIgnoreCase(), "hello\nworld", "hello\nworld"));
    assertTrue(evaluateTextExpression(new EqualsIgnoreCase(), "hello\nworld", "Hello\nWorld"));
    assertFalse(evaluateTextExpression(new EqualsIgnoreCase(), "hello\nworld", "hello world"));
    assertFalse(evaluateTextExpression(new EqualsIgnoreCase(), "hello\nworld", "helloworld"));
  }

  @Test
  public void testTextHasValue() {
    assertTrue(evaluate(TextType.INSTANCE, "s", "42", new HasValue().leftExpression("s")));
    assertFalse(evaluate(TextType.INSTANCE, "s", null, new HasValue().leftExpression("s")));
  }

  @Test
  public void testListHasValue() {
    assertTrue(evaluate(new ListType(), "s", Lists.newArrayList("12"), new HasValue().leftExpression("s")));
    assertFalse(evaluate(new ListType(), "s", null, new HasValue().leftExpression("s")));
    assertFalse(evaluate(new ListType(), "s", Lists.newArrayList(), new HasValue().leftExpression("s")));
  }

  @Test
  public void testListHasNoValue() {
    assertTrue(evaluate(new ListType(), "s", Lists.newArrayList(), new HasNoValue().leftExpression("s")));
    assertTrue(evaluate(new ListType(), "s", null, new HasNoValue().leftExpression("s")));
    assertFalse(evaluate(new ListType(), "s", Lists.newArrayList("12"), new HasNoValue().leftExpression("s")));

  }

  @Test
  public void testListWithTriggerData() {
    //变更后
    Map metadata = new HashMap() {{
      put("type", "after");
    }};

    Map<String, Object> triggerData = Maps.newHashMap();

    Comparator contains = new HasAnyOf().leftExpression("s").rightValue(Lists.newArrayList("a"));
    contains.getLeft().setMetadata(metadata);
    triggerData.put("s", Lists.newArrayList("a"));
    // leftValue:[b,c] ;s = a
    assertFalse(evaluate(new ListType(), "s", Lists.newArrayList("b", "c"), contains, triggerData));

    // leftValue:[b,c] ;s = b
    triggerData.put("s", Lists.newArrayList("b"));
    assertFalse(evaluate(new ListType(), "s", Lists.newArrayList("b", "c"), contains, triggerData));

    triggerData.clear();
    // leftValue:[b,c] ;
    assertFalse(evaluate(new ListType(), "s", Lists.newArrayList("b", "c"), contains, triggerData));

  }


  /**
   * 工作流变更时
   */
  @Test
  public void testWithWorkflowIsChangedData() {
    Map<String, Object> before = Maps.newHashMap();
    before.put("s", "333");
    Map<String, Object> triggerData = Maps.newHashMap();
    //变更前
    triggerData.put("before", before);
    //not set value
    SingleBindingCondition isChanged = new IsChanged().leftExpression("s");

    Map<String,Object> workflowInstanceBinding = Maps.newHashMap();
    workflowInstanceBinding.put("type","workflow");
    assertTrue(evaluate(new ListType(), "s",null, isChanged, triggerData,workflowInstanceBinding));
  }


  /**
   * 审批流变更时
   */
  @Test
  public void testWithApprovalFlowIsChangedData() {
    Map<String, Object> triggerData = Maps.newHashMap();
    //变更前
    triggerData.put("s", "333");
    //not set value
    SingleBindingCondition isChanged = new IsChanged().leftExpression("s");

    Map<String,Object> workflowInstanceBinding = Maps.newHashMap();
    workflowInstanceBinding.put("type","approvalflow");
    assertTrue(evaluate(new ListType(), "s",null, isChanged, triggerData,workflowInstanceBinding));
  }

  @Test
  public void testListEquals(){
    Comparator equals = new Equals().leftExpression("s").rightValue(Lists.newArrayList("a"));
    assertTrue(evaluate(new ListType(), "s", Lists.newArrayList("a"), equals,null));
    assertFalse(evaluate(new ListType(), "s", Lists.newArrayList("b","a"), equals,null));
    equals = new Equals().leftExpression("s").rightValue(Lists.newArrayList("a","b"));
    assertTrue(evaluate(new ListType(), "s", Lists.newArrayList("b","a"), equals,null));

    equals = new Equals().leftExpression("s").rightValue(Lists.newArrayList("a","b"));
    assertFalse(evaluate(new ListType(), "s", Lists.newArrayList(), equals,null));

    equals = new Equals().leftExpression("s").rightValue(null);
    assertFalse(evaluate(new ListType(), "s", Lists.newArrayList(), equals,null));

    equals = new Equals().leftExpression("s").rightValue(Lists.newArrayList());
    assertFalse(evaluate(new ListType(), "s", null, equals,null));
  }

  @Test
  public void testEnglish(){

    Or or = new Or();

    List<VariableData> variableDatas = Lists.newArrayList(VariableData.of(TextType.INSTANCE, "field_xj3Da__c", "Saudi Arabia"));

    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Turkey")));
    assertFalse(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));

    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Saudi Arabia")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Egypt")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));

    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Libya")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Algeria")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Morocco")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Mauritania")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Mali")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Niger")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));

    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Chad")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Sudan")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Ethiopia")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Central African Republic")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Democratic Republic of the Congo")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Kenya")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Somalia")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Tanzania")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Angola")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Zambia")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Namibia")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Botswana")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("South Africa")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Mozamboque")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Madagascar")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("United Arab Emirates(UAE)")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Bahrain")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Mauritius")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Mozambique")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Senegal")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Nigeria")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Egypt")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Yemen")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));


    or.condition(new And().condition(new Contains().leftExpression("field_xj3Da__c").rightValue("Cyprus")));
    assertTrue(evaluate(variableDatas,or,Maps.newHashMap(),Maps.newHashMap()));



  }


  @Test
  public void testListNotEquals() {
    Comparator notEquals = new NotEquals().leftExpression("s").rightValue(Lists.newArrayList("a"));
    assertFalse(evaluate(new ListType(), "s", Lists.newArrayList("a"), notEquals, null));
    assertTrue(evaluate(new ListType(), "s", Lists.newArrayList("b", "a"), notEquals, null));
    assertTrue(evaluate(new ListType(), "s", Lists.newArrayList("a", "b"), notEquals, null));
    notEquals = new NotEquals().leftExpression("s").rightValue(Lists.newArrayList("a", "b"));
    assertFalse(evaluate(new ListType(), "s", Lists.newArrayList("a", "b"), notEquals, null));
    assertFalse(evaluate(new ListType(), "s", Lists.newArrayList("b", "a"), notEquals, null));
  }

  @Test
  public void testTextHasNoValue() {
    assertFalse(evaluate(TextType.INSTANCE, "s", "42", new HasNoValue().leftExpression("s")));
    assertTrue(evaluate(TextType.INSTANCE, "s", null, new HasNoValue().leftExpression("s")));
  }

  @Test
  public void testTextNotContains() {
    assertTrue(evaluateTextExpression(new NotContains(), "hello", "by"));
    assertFalse(evaluateTextExpression(new NotContains(), "hello", "hello"));
    assertFalse(evaluateTextExpression(new NotContains(), "hello", "hell"));
    assertFalse(evaluateTextExpression(new NotContains(), "hello", "ell"));
    assertFalse(evaluateTextExpression(new NotContains(), "hello", "ello"));
    assertTrue(evaluateTextExpression(new NotContains(), "hello", "El"));
    assertTrue(evaluateTextExpression(new NotContains(), "hello", "Hello"));
    assertTrue(evaluateTextExpression(new NotContains(), null, "hello"));
  }

  @Test
  public void testTextNotContainsMultiLine() {
    assertFalse(evaluateTextExpression(new NotContains(), "hello\nworld", "hello"));
    assertTrue(evaluateTextExpression(new NotContains(), "hello\nworld", "Hello"));
    assertTrue(evaluateTextExpression(new NotContains(), "goodbye\nworld", "hello"));
    assertFalse(evaluateTextExpression(new NotContains(), "hello\nworld\nGOTO 10", "hello\nworld"));
    assertTrue(evaluateTextExpression(new NotContains(), "hello\nworld\nGOTO 10", "Hello\nworld"));
    assertTrue(evaluateTextExpression(new NotContains(), "goodbye\nworld\nGOTO 10", "hello\nworld"));
  }

  @Test
  public void testTextNotContainsIgnoreCase() {
    assertTrue(evaluateTextExpression(new NotContainsIgnoreCase(), "HELLO", "by"));
    assertFalse(evaluateTextExpression(new NotContainsIgnoreCase(), "HELLO", "hello"));
    assertFalse(evaluateTextExpression(new NotContainsIgnoreCase(), "HELLO", "hell"));
    assertFalse(evaluateTextExpression(new NotContainsIgnoreCase(), "HELLO", "ell"));
    assertFalse(evaluateTextExpression(new NotContainsIgnoreCase(), "HELLO", "ello"));
    assertFalse(evaluateTextExpression(new NotContainsIgnoreCase(), "hello", "ell"));
    assertTrue(evaluateTextExpression(new NotContainsIgnoreCase(), null, "hello"));
  }

  @Test
  public void testTextNotContainsIgnoreCaseMultiLine() {
    assertFalse(evaluateTextExpression(new NotContainsIgnoreCase(), "hello\nworld", "hello"));
    assertTrue(evaluateTextExpression(new NotContainsIgnoreCase(), "hello\nworld", "by"));
    assertFalse(evaluateTextExpression(new NotContainsIgnoreCase(), "hello\nworld", "Hello"));
    assertFalse(evaluateTextExpression(new NotContainsIgnoreCase(), "hello\nworld\nGOTO 10", "hello\nworld"));
    assertTrue(evaluateTextExpression(new NotContainsIgnoreCase(), "hello\nworld\nGOTO 10", "Goodbye\nWorld"));
    assertFalse(evaluateTextExpression(new NotContainsIgnoreCase(), "hello\nworld\nGOTO 10", "Hello\nWorld"));
  }

  @Test
  public void testTextNotEquals() {
    assertFalse(evaluateTextExpression(new NotEquals(), "hello", "hello"));
    assertTrue(evaluateTextExpression(new NotEquals(), "hello", "Hello"));
    assertTrue(evaluateTextExpression(new NotEquals(), "hello", "by"));
    assertTrue(evaluateTextExpression(new NotEquals(), null, "hello"));
  }

  @Test
  public void testTextNotEqualsMultiLine() {
    assertFalse(evaluateTextExpression(new NotEquals(), "hello\nworld", "hello\nworld"));
    assertTrue(evaluateTextExpression(new NotEquals(), "hello\nworld", "Hello\nWorld"));
    assertTrue(evaluateTextExpression(new NotEquals(), "hello\nworld", "hello world"));
    assertTrue(evaluateTextExpression(new NotEquals(), "hello\nworld", "helloworld"));
  }

  @Test
  public void testTextNotEqualsIgnoreCase() {
    assertFalse(evaluateTextExpression(new NotEqualsIgnoreCase(), "hello", "hello"));
    assertFalse(evaluateTextExpression(new NotEqualsIgnoreCase(), "hello", "Hello"));
    assertTrue(evaluateTextExpression(new NotEqualsIgnoreCase(), "hello", "by"));
    assertTrue(evaluateTextExpression(new NotEqualsIgnoreCase(), null, "hello"));
  }

  @Test
  public void testTextNotEqualsIgnoreCaseMultiLine() {
    assertFalse(evaluateTextExpression(new NotEqualsIgnoreCase(), "hello\nworld", "hello\nworld"));
    assertFalse(evaluateTextExpression(new NotEqualsIgnoreCase(), "hello\nworld", "Hello\nWorld"));
    assertTrue(evaluateTextExpression(new NotEqualsIgnoreCase(), "hello\nworld", "hello world"));
    assertTrue(evaluateTextExpression(new NotEqualsIgnoreCase(), "hello\nworld", "helloworld"));
  }

  private boolean evaluateNumberExpression(Comparator condition, Number leftValue, Number rightValue) {
    return evaluate(NumberType.INSTANCE, "n", leftValue, condition.leftExpression("n").rightValue(rightValue));
  }

  protected boolean evaluateTextExpression(Comparator condition, String leftValue, String rightValue) {
    return evaluate(TextType.INSTANCE, "s", leftValue, condition.leftExpression("s").rightValue(rightValue));
  }
  private boolean evaluate(DataType type, String variableId, Object value, Condition condition) {
    return evaluate(type,variableId,value,condition,null);
  }

  private boolean evaluate(DataType type, String variableId, Object lifeValue, Condition condition,Map<String,Object> data) {
    return evaluate(type,variableId,lifeValue,condition,data,null);
  }

  private boolean evaluate(DataType type, String variableId, Object value, Condition condition,Map<String,Object> data,Map<String,Object> workflowInstanceBinding) {
    ExecutableWorkflow workflow = new ExecutableWorkflow()
            .variable(variableId, type);
    if(Objects.nonNull(workflowInstanceBinding)&&workflowInstanceBinding.containsKey("type")){
      workflow.type(workflowInstanceBinding.get("type").toString());
    }
    deploy(workflow);

    TriggerInstance triggerInstance = new TriggerInstance("")
            .data(variableId, value)
            .workflowId(workflow.getId());

    if (Objects.nonNull(workflowInstanceBinding)) {
      triggerInstance.setTransientData(workflowInstanceBinding);
    }
    WorkflowEngineImpl workflowEngineImpl = (WorkflowEngineImpl) workflowEngine;
    WorkflowInstanceImpl workflowInstance = workflowEngineImpl.startInitialize(triggerInstance);
    Map<String,Object> triggerData= Maps.newHashMap();
    triggerData.put("data",data);
    workflowInstance.setProperty("triggerData",triggerData);
    ConditionService conditionService = configuration.get(ConditionService.class);
    WorkflowParser workflowParser = new WorkflowParser(configuration);
    workflowParser.pushContext("condition", null, null, null);
    ConditionImpl conditionImpl = conditionService.compile(condition, workflowParser);
    return conditionImpl.eval(workflowInstance);
  }

  /**
   * @param variableDatas 条件变量,类型,值
   * @param condition 条件
   * @param data   approvalflow  data=triggerData.callbackData   ||    workflow  data=before || after
   * @param transientData
   * @return
   */
  private boolean evaluate(List<VariableData> variableDatas, Condition condition, Map<String, Object> data, Map<String, Object> transientData) {
    ExecutableWorkflow workflow = new ExecutableWorkflow();
    TriggerInstance triggerInstance = new TriggerInstance("");

    variableDatas.forEach(item -> {
      workflow.variable(item.getVariableId(), item.getType());
      triggerInstance.data(item.getVariableId(), item.getValue());
    });
    deploy(workflow);
    triggerInstance.workflowId(workflow.getId());

    if (Objects.nonNull(transientData)) {
      triggerInstance.setTransientData(transientData);
    }

    WorkflowEngineImpl workflowEngineImpl = (WorkflowEngineImpl) workflowEngine;
    WorkflowInstanceImpl workflowInstance = workflowEngineImpl.startInitialize(triggerInstance);
    Map<String, Object> triggerData = Maps.newHashMap();
    triggerData.put("data", data);
    workflowInstance.setProperty("triggerData", triggerData);
    ConditionService conditionService = configuration.get(ConditionService.class);
    WorkflowParser workflowParser = new WorkflowParser(configuration);
    workflowParser.pushContext("condition", null, null, null);
    ConditionImpl conditionImpl = conditionService.compile(condition, workflowParser);
    return conditionImpl.eval(workflowInstance);
  }


}

class VariableData {
  private DataType type;
  private String variableId;
  private Object value;

  public static VariableData of(TextType type, String variableId, Object value) {
    VariableData variableData = new VariableData();
    variableData.setType(type);
    variableData.setVariableId(variableId);
    variableData.setValue(value);
    return variableData;
  }


  public DataType getType() {
    return type;
  }

  public void setType(DataType type) {
    this.type = type;
  }

  public String getVariableId() {
    return variableId;
  }

  public void setVariableId(String variableId) {
    this.variableId = variableId;
  }

  public Object getValue() {
    return value;
  }

  public void setValue(Object value) {
    this.value = value;
  }
}