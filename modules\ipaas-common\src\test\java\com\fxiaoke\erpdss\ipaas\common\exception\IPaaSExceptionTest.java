package com.fxiaoke.erpdss.ipaas.common.exception;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * IPaaSException 单元测试
 * 
 * <AUTHOR> (^_−)☆
 */
class IPaaSExceptionTest {

    /**
     * 测试异常实现类 - 用于测试抽象基类
     */
    private static class TestIPaaSException extends IPaaSException {
        public TestIPaaSException(String code, String msg) {
            super(code, msg);
        }

        public TestIPaaSException(String code, String msg, Throwable cause) {
            super(code, msg, cause);
        }
    }

    @Test
    void testConstructorWithCodeAndMessage() {
        // Given
        String code = "TEST001";
        String message = "测试异常消息";

        // When
        TestIPaaSException exception = new TestIPaaSException(code, message);

        // Then
        assertThat(exception.getCode()).isEqualTo(code);
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getCause()).isNull();
    }

    @Test
    void testConstructorWithCodeMessageAndCause() {
        // Given
        String code = "TEST002";
        String message = "测试异常消息";
        Throwable cause = new RuntimeException("原因异常");

        // When
        TestIPaaSException exception = new TestIPaaSException(code, message, cause);

        // Then
        assertThat(exception.getCode()).isEqualTo(code);
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getCause()).isEqualTo(cause);
    }

    @Test
    void testSetCode() {
        // Given
        TestIPaaSException exception = new TestIPaaSException("ORIGINAL", "原始消息");
        String newCode = "NEW_CODE";

        // When
        exception.setCode(newCode);

        // Then
        assertThat(exception.getCode()).isEqualTo(newCode);
    }

    @Test
    void testCodeWithNullValue() {
        // Given
        TestIPaaSException exception = new TestIPaaSException(null, "消息");

        // When & Then
        assertThat(exception.getCode()).isNull();
    }

    @Test
    void testMessageWithNullValue() {
        // Given
        TestIPaaSException exception = new TestIPaaSException("CODE", null);

        // When & Then
        assertThat(exception.getMessage()).isNull();
    }

    @Test
    void testExceptionInheritance() {
        // Given
        TestIPaaSException exception = new TestIPaaSException("CODE", "消息");

        // When & Then
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(exception).isInstanceOf(IPaaSException.class);
    }

    @Test
    void testSerialVersionUID() {
        // Given
        TestIPaaSException exception1 = new TestIPaaSException("CODE", "消息");
        TestIPaaSException exception2 = new TestIPaaSException("CODE", "消息");

        // When & Then
        // 验证序列化版本ID存在（通过反射检查）
        assertThat(exception1.getClass().getSuperclass().getDeclaredFields())
                .anyMatch(field -> "serialVersionUID".equals(field.getName()));
    }

    @Test
    void testExceptionChaining() {
        // Given
        RuntimeException rootCause = new RuntimeException("根本原因");
        IllegalArgumentException intermediateCause = new IllegalArgumentException("中间原因", rootCause);
        TestIPaaSException exception = new TestIPaaSException("CHAIN_CODE", "链式异常", intermediateCause);

        // When & Then
        assertThat(exception.getCause()).isEqualTo(intermediateCause);
        assertThat(exception.getCause().getCause()).isEqualTo(rootCause);

        // 验证异常链
        Throwable current = exception;
        int chainLength = 0;
        while (current != null) {
            chainLength++;
            current = current.getCause();
        }
        assertThat(chainLength).isEqualTo(3); // exception -> intermediateCause -> rootCause
    }

    @Test
    void testExceptionWithLongMessage() {
        // Given
        StringBuilder longMessage = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longMessage.append("这是一个很长的错误消息 ");
        }
        String message = longMessage.toString();

        // When
        TestIPaaSException exception = new TestIPaaSException("LONG_MSG", message);

        // Then
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getMessage().length()).isGreaterThan(10000);
    }

    @Test
    void testExceptionWithSpecialCharacters() {
        // Given
        String code = "SPECIAL_CHARS_测试_123";
        String message = "特殊字符测试: @#$%^&*()_+{}|:<>?[]\\;'\",./ 中文 🚀 emoji";

        // When
        TestIPaaSException exception = new TestIPaaSException(code, message);

        // Then
        assertThat(exception.getCode()).isEqualTo(code);
        assertThat(exception.getMessage()).isEqualTo(message);
    }

    @Test
    void testExceptionSerialization() throws Exception {
        // Given
        TestIPaaSException originalException = new TestIPaaSException("SERIAL_CODE", "序列化测试");

        // When - 序列化
        java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
        java.io.ObjectOutputStream oos = new java.io.ObjectOutputStream(baos);
        oos.writeObject(originalException);
        oos.close();

        // 反序列化
        java.io.ByteArrayInputStream bais = new java.io.ByteArrayInputStream(baos.toByteArray());
        java.io.ObjectInputStream ois = new java.io.ObjectInputStream(bais);
        TestIPaaSException deserializedException = (TestIPaaSException) ois.readObject();
        ois.close();

        // Then
        assertThat(deserializedException.getCode()).isEqualTo(originalException.getCode());
        assertThat(deserializedException.getMessage()).isEqualTo(originalException.getMessage());
        assertThat(deserializedException.getClass()).isEqualTo(originalException.getClass());
    }

    @Test
    void testExceptionStackTrace() {
        // Given
        TestIPaaSException exception = new TestIPaaSException("STACK_CODE", "堆栈测试");

        // When
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        exception.printStackTrace(pw);
        String stackTrace = sw.toString();

        // Then
        assertThat(stackTrace).contains("TestIPaaSException");
        assertThat(stackTrace).contains("堆栈测试");
        assertThat(stackTrace).contains("testExceptionStackTrace");
    }

    @Test
    void testMultipleCodeChanges() {
        // Given
        TestIPaaSException exception = new TestIPaaSException("ORIGINAL", "原始消息");

        // When
        exception.setCode("CHANGED_1");
        String firstChange = exception.getCode();

        exception.setCode("CHANGED_2");
        String secondChange = exception.getCode();

        exception.setCode(null);
        String nullChange = exception.getCode();

        // Then
        assertThat(firstChange).isEqualTo("CHANGED_1");
        assertThat(secondChange).isEqualTo("CHANGED_2");
        assertThat(nullChange).isNull();
    }
}
