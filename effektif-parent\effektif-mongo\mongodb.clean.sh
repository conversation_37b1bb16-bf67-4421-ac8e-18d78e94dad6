#!/bin/bash

set -x

MONGODB_VERSION=${MONGODB_VERSION-2.6.5}
MONGODB_HOME=${MONGODB_HOME-$HOME/.effektif/mongodb-$MONGODB_VERSION}

DATABASES_HOME=$HOME/.effektif

DATABASE_DIR=$DATABASES_HOME/shard
CONFIGSVR_DIR=$DATABASES_HOME/cfg
MONGOS_DIR=$DATABASES_HOME/mongos

rm -rf ${DATABASE_DIR}1
rm -rf ${DATABASE_DIR}2
rm -rf ${DATABASE_DIR}3
rm -rf ${CONFIGSVR_DIR}1
rm -rf ${CONFIGSVR_DIR}2
rm -rf ${CONFIGSVR_DIR}3
rm -rf ${MONGOS_DIR}
