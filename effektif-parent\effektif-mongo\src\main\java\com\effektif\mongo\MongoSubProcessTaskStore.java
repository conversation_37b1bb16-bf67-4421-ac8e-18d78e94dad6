package com.effektif.mongo;

import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.configuration.Brewable;
import com.effektif.workflow.impl.configuration.Brewery;
import com.effektif.workflow.impl.ext.SubProcessTask;
import com.effektif.workflow.impl.ext.SubProcessTaskStore;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCursor;
import org.bson.types.ObjectId;
import org.slf4j.Logger;



public class MongoSubProcessTaskStore implements SubProcessTaskStore, Brewable {
    public static final Logger log = MongoDb.log;

    protected MongoObjectMapper mongoMapper;
    private MongoDb mongoDb;
    private MongoConfiguration mongoConfiguration;

    @Override
    public void brew(Brewery brewery) {
        mongoDb = brewery.get(MongoDb.class);
        mongoConfiguration = brewery.get(MongoConfiguration.class);
        this.mongoMapper = brewery.get(MongoObjectMapper.class);
    }

    public BasicDBObject taskToMongo(SubProcessTask task) {
        return (BasicDBObject) mongoMapper.write(task);
    }


    @Override
    public TaskId generateTaskId() {
        return new TaskId(new ObjectId().toString());
    }

    @Override
    public void insertTask(SubProcessTask task) {
        task.setModifyTime(System.currentTimeMillis());
        BasicDBObject dbTask = taskToMongo(task);
        this.getTasksCollection(task.getTenantId()).insert("insert-subProcessTask", dbTask);
    }


    /**
     * 更新某个子流程的状态为pass
     */
    @Override
    public SubProcessTask updateState(String tenantId, String superWorkflowInstanceId, String subProcessInstanceId, String actionType, Long end) {
        Query mongoQuery = new Query();
        mongoQuery.equal(TaskFields.TENANT_ID, tenantId);
        mongoQuery.equal(TaskFields.SUB_WORKFLOW_INSTANCE_ID, subProcessInstanceId);
        mongoQuery.equal(TaskFields.WORKFLOW_INSTANCE_ID, superWorkflowInstanceId);
        BasicDBObject update = new Update()
                .set(TaskFields.STATE, actionType)
                .set(TaskFields.MODIFY_TIME, end)
                .get();
        BasicDBObject dbTask = this.getTasksCollection(tenantId)
                .findAndModify("complete-subProcessTask", mongoQuery.get(), update, null, null, false, false, false);
        return mongoToTask(dbTask);
    }

    @Override
    public SubProcessTask findById(String tenantId, String taskId) {
        Query mongoQuery = new Query();
        mongoQuery.equal(TaskFields.TENANT_ID, tenantId);
        mongoQuery.equal(TaskFields._ID, new ObjectId(taskId));

        BasicDBObject dbTask = this.getTasksCollection(tenantId).findOne("subProcess-findOne", mongoQuery.get());
        return mongoToTask(dbTask);
    }


    public <T extends SubProcessTask> T mongoToTask(BasicDBObject dbTask) {
        return mongoMapper.read(dbTask, SubProcessTask.class);
    }


    private MongoCollection getTasksCollection(String tenantId) {
        return mongoDb.createCollection(tenantId, mongoConfiguration.getSubProcessTasksCollectionName());
    }
}
