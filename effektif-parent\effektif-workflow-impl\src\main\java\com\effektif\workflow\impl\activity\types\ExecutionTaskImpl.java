package com.effektif.workflow.impl.activity.types;

import com.effektif.workflow.api.activities.ExecutionItem;
import com.effektif.workflow.api.activities.ExecutionTask;
import com.effektif.workflow.api.ext.ExecutionPojo;
import com.effektif.workflow.api.ext.WorkflowBindingEnum;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.WorkflowInstanceStore;
import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.activity.AbstractActivityType;
import com.effektif.workflow.impl.ext.TaskConfigSupportPojo;
import com.effektif.workflow.impl.ext.latencyTask.AutoTask;
import com.effektif.workflow.impl.ext.latencyTask.AutoTaskStore;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflow.WorkflowImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.VariableInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import com.facishare.paas.workflow.bus.EngineEventBus;
import com.facishare.paas.workflow.bus.api.AutoTaskCreateEvent;
import com.facishare.paas.workflow.bus.api.CreateTaskEvent;
import com.facishare.paas.workflow.bus.api.DelayTaskCreateEvent;
import com.facishare.paas.workflow.bus.api.TaskHandledEvent;
import com.facishare.paas.workflow.bus.api.type.FlowTag;
import com.facishare.paas.workflow.bus.api.type.TaskState;
import com.facishare.paas.workflow.bus.model.BusConstant;
import com.facishare.paas.workflow.bus.model.MQContext;
import com.google.common.collect.Maps;

import java.lang.reflect.Method;
import java.util.*;

/**
 * zhenghaibo
 * 16/7/1 19:50
 */
public class ExecutionTaskImpl extends AbstractActivityType<ExecutionTask> {

  protected WorkflowInstanceStore workflowInstanceStore;

  protected AutoTaskStore autoTaskStore;

  public ExecutionTaskImpl() {
    super(ExecutionTask.class);
  }

  @Override
  public void parse(ActivityImpl activityImpl, ExecutionTask executionTask, WorkflowParser parser) {
    super.parse(activityImpl, executionTask, parser);
    this.workflowInstanceStore = parser.getConfiguration(WorkflowInstanceStore.class);
    this.autoTaskStore = parser.getConfiguration(AutoTaskStore.class);
  }

  @Override
  public void execute(ActivityInstanceImpl activityInstance) {
    try {
      /**
       * 是否生成后续节点
       * 自动节点增加重试机制，节点上的任务执行失败时引擎应停止流转
       */
      WorkflowImpl workflow = activityInstance.workflow;
      WorkflowInstanceImpl workflowInstance = activityInstance.workflowInstance;
      String type= workflow.getType();
      String appId= workflow.getAppId();

      String applicantId = this.getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.applicantId.toString());
      Map<String, String> extMap = new HashMap<>();
      String tenantId=workflow.getTenantId();
      extMap.put(WorkflowBindingEnum.tenantId.toString(), tenantId);
      extMap.put(WorkflowBindingEnum.appId.toString(), appId);
      extMap.put(WorkflowBindingEnum.eventId.toString(), this.getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.eventId.toString()));
      extMap.put("workflowId", workflow.getId().toString());
      extMap.put("activityId", activity.getId());
      extMap.put("applicantId", applicantId);
      extMap.put("sourceWorkflowId", workflow.getSourceWorkflowId());
      extMap.put("type",type);
      extMap.put(WorkflowBindingEnum.applicantId.toString(), this.getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.applicantId.toString()));
      extMap.put("workflowInstanceId", activityInstance.workflowInstance.getId().toString());
      extMap.put("workflowName", workflow.getName());
      extMap.put(WorkflowBindingEnum.triggerWay.toString(), this.getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.triggerWay.toString()));
      Map<String, Object> variableMap = new HashMap<>();
      Map<String, VariableInstanceImpl> variableInstancesMap = workflowInstance.variableInstancesMap;
      if (null != variableInstancesMap) {
          variableInstancesMap.keySet().forEach(key -> {
              if (null != variableInstancesMap.get(key) && null != variableInstancesMap.get(key).value) {
                  variableMap.put(key, variableInstancesMap.get(key).value);
              }
          });
      }
      String entityId,objectId;
      if (type.equals(WorkflowConstants.WorkflowType.BPM)) {
        TaskConfigSupportPojo.BpmEntityIdAndObjectId bpmEntityIdAndObjectId = getBpmEntityIdAndObjectId(activityInstance.activity.getProperties(), variableMap);
        entityId = bpmEntityIdAndObjectId.getEntityId();
        objectId = bpmEntityIdAndObjectId.getObjectId();
        variableMap.put("activity_" + activity.getId() + "##" + entityId, objectId);
      } else {
        //工作流 审批流均适用
        entityId = this.getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.entityId.toString());
        objectId = this.getWorkflowInstanceProperty(workflowInstance, WorkflowBindingEnum.objectId.toString());
      }
      extMap.put(WorkflowBindingEnum.entityId.toString(), entityId);
      extMap.put(WorkflowBindingEnum.objectId.toString(),objectId);
      List<ExecutionPojo> pojoList = this.convert(activity, extMap,variableMap);
      // 执行后动作前保存自动节点信息，执行后动作后更新后动作的执行状态
      TaskId taskId = autoTaskStore.generateTaskId();

      //是否开启了延时等待
      boolean delay = Objects.nonNull(activity.getDelay()) && activity.getDelay();

      AutoTask autoTask = new AutoTask(taskId, activityInstance.getId()).buildExecutionTask(activity, pojoList, extMap);
      //扩展自动节点 支持延时
      if (type.equals(WorkflowConstants.WorkflowType.BPM) || type.equals(WorkflowConstants.WorkflowType.APPROVAL_FLOW)) {
        autoTaskStore.insertTask(autoTask);
        MQContext mqContext = MQContext.create(workflow.getTenantId(), workflow.getAppId(), "-10000", null, type);

        // 810 增加延时等待,此处发送 eventbus listener 判断如果是 delay ,则不发送 mq,在实际执行时,发送 mq消息
        if (delay) {
          //等待节点并非真正执行
          EngineEventBus.post(CreateTaskEvent.create(
                  mqContext,
                  FlowTag.type(type),
                  taskId.toString(),
                  null,
                  null,
                  activityInstance.workflowInstance.getId().toString(),
                  BusConstant.DELAY_TASK_TYPE,
                  entityId,
                  objectId,
                  activity.getLinkApp())
          );
          EngineEventBus.post(DelayTaskCreateEvent.create(
                  mqContext,
                  FlowTag.type(type),
                  null,
                  taskId.toString(),
                  autoTask.getCreateTime(),
                  autoTask.getModifyTime(),
                  autoTask.getName(),
                  autoTask.getEntityId(),
                  autoTask.getObjectId(),
                  autoTask.getApplicantId(),
                  mqContext.getUserId(),
                  autoTask.getWorkflowInstanceId(),
                  TaskState.valueOfSkipNotFound(autoTask.getState()),
                  autoTask.getErrMsg(),
                  WorkflowConstants.DELAY,
                  autoTask.getActivityId(),
                  autoTask.getWorkflowId(),
                  workflow.getSourceWorkflowId())
          );
        } else {
          //业务流自动节点执行完后,不管执行成功或者失败,都需要告诉业务流,让业务流去更新下实例信息
          // 最后一个节点为自动节点,且自动节点执行时间过长,需要当自动节点执行完后,更新下实例的状态,不然实例状态是in_progress
          EngineEventBus.post(
                  TaskHandledEvent.createBpm(
                          mqContext,
                          FlowTag.type(type),
                          "",
                          taskId.toString(),
                          null,
                          "",
                          BusConstant.AUTO_TASK_TYPE,
                          "",
                          activityInstance.workflowInstance.getId().toString(),
                          activity.getLinkApp(),
                          null,
                          entityId,
                          objectId
                  )
          );
          EngineEventBus.post(AutoTaskCreateEvent.create(
                  mqContext,
                  FlowTag.type(type),
                  null,
                  taskId.toString(),
                  autoTask.getCreateTime(),
                  autoTask.getModifyTime(),
                  autoTask.getName(),
                  autoTask.getEntityId(),
                  autoTask.getObjectId(),
                  autoTask.getApplicantId(),
                  mqContext.getUserId(),
                  autoTask.getWorkflowInstanceId(),
                  TaskState.valueOfSkipNotFound(autoTask.getState()),
                  autoTask.getErrMsg(),
                  WorkflowConstants.AUTO,
                  autoTask.getActivityId(),
                  autoTask.getWorkflowId(),
                  workflow.getSourceWorkflowId())
          );
        }
      } else {
        Date expireDate=null;
        if(WorkflowConstants.AppId.APP_ID_CRM.equals(workflowInstance.getAppId())&&FlowTag.workflow.name().equals(workflowInstance.getType())){
          //增加工作流过期索引时间
          expireDate=workflowInstance.calculateExpireDate();
        }
        autoTaskStore.insertWorkflowTask(new AutoTask(taskId, activityInstance.getId()).expireDate(expireDate).buildExecutionTask(activity, pojoList, extMap));
      }
      //非延时等待节点 && 执行执行项成功,再向后执行
      if (!delay && execute(pojoList, autoTask)) {
        activityInstance.onwards();
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private boolean execute(List pojoList, AutoTask autoTask) throws Exception {
    Class<?> bean = Class.forName("com.facishare.paas.workflow.kernel.support.ExecutionTaskSupport");
    Method method = bean.getMethod("execute",List.class, AutoTask.class);
    return (boolean) method.invoke(bean.newInstance(),pojoList, autoTask);
  }

  private String getObjectHelper(Object obj) {
    String result = null;
    if (obj != null) {
      result = obj.toString();
    }
    return result;
  }

  private String getWorkflowInstanceProperty(WorkflowInstanceImpl workflowInstance, String key) {
    if (workflowInstance == null || key == null) {
      return null;
    }
    if (workflowInstance.getProperty(key) != null) {
      return this.getObjectHelper(workflowInstance.getProperty(key));
    }
    return this.getObjectHelper(workflowInstance.getTransientProperty(key));
  }

  private List<ExecutionPojo> convert(ExecutionTask task, Map<String, String> extMap,Map<String, Object> variables) {
    List<ExecutionPojo> pojoList = new ArrayList<>();
    if (task.getItemList() != null && task.getItemList().size() > 0) {
      for (ExecutionItem item : task.getItemList()) {
        ExecutionPojo pojo = new ExecutionPojo();
        pojo.setTaskType(item.getTaskType());
        pojo.setSender(item.getSender());
        pojo.setRecipients(Objects.nonNull(item.getRecipients()) ? deepCopyMap(item.getRecipients()) : item.getRecipients());
        pojo.setCcRecipients(Objects.nonNull(item.getCcRecipients()) ? deepCopyMap(item.getCcRecipients()) : item.getCcRecipients());
        pojo.setBccRecipients(Objects.nonNull(item.getBccRecipients()) ? deepCopyMap(item.getBccRecipients()) : item.getBccRecipients());
        pojo.setEmailAddress(item.getEmailAddress());
        pojo.setTitle(item.getTitle());
        pojo.setContent(item.getContent());
        pojo.setTemplate(item.getTemplate());
        pojo.setAsync(item.getAsync());
        pojo.setFieldMapping(item.getFieldMapping());
        pojo.setUpdateFieldJson(item.getUpdateFieldJson());
        pojo.setUpdateFieldObject(item.getUpdateFieldObject());
        pojo.setTriggerParam(item.getTriggerParam());
        pojo.setTenantId(extMap.get(WorkflowBindingEnum.tenantId.toString()));
        pojo.setAppId(extMap.get(WorkflowBindingEnum.appId.toString()));
        pojo.setWorkflowMap(extMap);
        pojo.setAfterActionDefinitionId(item.getAfterActionDefinitionId());
        pojo.setAfterActionMappingId(item.getAfterActionMappingId());
        pojo.setActionMapping(item.getActionMapping());
        pojo.setActionParams(item.getActionParams());
        pojo.setTask(activity);
        pojo.setType(extMap.get("type"));
        pojo.setVariables(variables);
        pojo.setCustomVariables(item.getCustomVariables());
        pojo.setUseRelated(item.getUseRelated());
        pojo.setRelatedEntityId(item.getRelatedEntityId());
        pojo.setRelatedObjectId(item.getRelatedObjectId());
        pojoList.add(pojo);
      }
    }
    return pojoList;
  }
  private LinkedHashMap<String, Set<String>> deepCopyMap(LinkedHashMap<String, Set<String>> originalMap) {
    LinkedHashMap<String, Set<String>> copiedMap = Maps.newLinkedHashMap();
    for (Map.Entry<String, Set<String>> entry : originalMap.entrySet()) {
      Set<String> copiedSet = new HashSet<>(entry.getValue());
      copiedMap.put(entry.getKey(), copiedSet);
    }
    return copiedMap;
  }
}
