package com.effektif.workflow.api.activities;

import com.effektif.workflow.api.bpmn.BpmnElement;
import com.effektif.workflow.api.json.TypeName;
import com.effektif.workflow.api.workflow.Activity;

import java.util.ArrayList;
import java.util.List;

/**
 * zhenghaibo
 * 16/7/1 16:56
 */
@TypeName("executionTask")
@BpmnElement("executionTask")
public class ExecutionTask extends Activity {

    private static final long serialVersionUID = 6109121899856655719L;

    private List<ExecutionItem> itemList;
    private String taskId;
    /**
     * true:延时执行
     */
    private Boolean delay;
    /**
     * condition:条件等待;delay:延时等待
     */
    private String delayStrategy;

    /**
     * 1-天、2-小时、3-分钟
     */
    private Integer latencyUnit;

    /**
     * 支持公式,也支持常量
     */
    private Object remindLatency;

    /**
     * 条件
     */
    protected Object rule;
    /**
     * 函数条件
     */
    protected Object functionRule;

    //企业互联应用id
    private String linkApp;
    //是否启用企业互联
    protected boolean linkAppEnable;
    //企业互联应用名称
    protected String linkAppName;
    //企业互联应用的类型
    protected int linkAppType;

    protected String outerTenantField;
    //企业互联外部企业id
    protected String outerTenantId;

    public List<ExecutionItem> getItemList() {
        return itemList;
    }

    public void setItemList(List<ExecutionItem> itemList) {
        this.itemList = itemList;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public ExecutionTask item(ExecutionItem item) {
        if (itemList == null) {
            itemList = new ArrayList<>();
        }
        itemList.add(item);
        return this;
    }

    public Boolean getDelay() {
        return delay;
    }

    public void setDelay(Boolean delay) {
        this.delay = delay;
    }

    public String getDelayStrategy() {
        return delayStrategy;
    }

    public void setDelayStrategy(String delayStrategy) {
        this.delayStrategy = delayStrategy;
    }

    public Integer getLatencyUnit() {
        return latencyUnit;
    }

    public void setLatencyUnit(Integer latencyUnit) {
        this.latencyUnit = latencyUnit;
    }

    public Object getRemindLatency() {
        return remindLatency;
    }

    public void setRemindLatency(Object remindLatency) {
        this.remindLatency = remindLatency;
    }

    public Object getRule() {
        return rule;
    }

    public void setRule(Object rule) {
        this.rule = rule;
    }

    public Object getFunctionRule() {
        return functionRule;
    }

    public void setFunctionRule(Object functionRule) {
        this.functionRule = functionRule;
    }

    public String getLinkApp() {
        return linkApp;
    }

    public void setLinkApp(String linkApp) {
        this.linkApp = linkApp;
    }
    public boolean isLinkAppEnable() {
        return linkAppEnable;
    }

    public void setLinkAppEnable(boolean linkAppEnable) {
        this.linkAppEnable = linkAppEnable;
    }

    public String getLinkAppName() {
        return linkAppName;
    }

    public void setLinkAppName(String linkAppName) {
        this.linkAppName = linkAppName;
    }

    public int getLinkAppType() {
        return linkAppType;
    }

    public void setLinkAppType(int linkAppType) {
        this.linkAppType = linkAppType;
    }

    public String getOuterTenantField() {
        return outerTenantField;
    }

    public void setOuterTenantField(String outerTenantField) {
        this.outerTenantField = outerTenantField;
    }

    public String getOuterTenantId() {
        return outerTenantId;
    }

    public void setOuterTenantId(String outerTenantId) {
        this.outerTenantId = outerTenantId;
    }
}
