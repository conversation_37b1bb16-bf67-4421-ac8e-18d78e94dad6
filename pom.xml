<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.fxiaoke.cloud</groupId>
        <artifactId>fxiaoke-spring-cloud-parent</artifactId>
        <version>2.7.0-SNAPSHOT</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.fxiaoke.erpdss</groupId>
    <artifactId>fs-erp-ipaas</artifactId>
    <version>2.0-SNAPSHOT</version>

    <modules>
        <module>ipaas-app-all</module>
        <module>ipaas-app-core</module>
        <module>ipaas-app-web</module>
        <module>ipaas-flow-engine</module>

        <module>modules/ipaas-common</module>
        <module>modules/ipaas-api</module>
        <module>modules/ipaas-spring-common</module>

        <module>modules/ipaas-db-proxy</module>
        <module>modules/ipaas-connector-proxy</module>
        <module>modules/ipaas-limiter</module>
        <module>modules/ipaas-log</module>
        <module>modules/ipaas-monitor</module>
        <module>modules/ipaas-quota</module>
        <module>modules/ipaas-template</module>

        <module>effektif-parent</module>
    </modules>
    <properties>
        <java.version>21</java.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <hutool.version>5.8.39</hutool.version>
        <effektif.version>9.4.0-SNAPSHOT</effektif.version>
        <ipaas.version>2.0-SNAPSHOT</ipaas.version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>26.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>effektif-workflow-api</artifactId>
                <version>${effektif.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>effektif-mongo</artifactId>
                <version>${effektif.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>effektif-workflow-impl</artifactId>
                <version>${effektif.version}</version>
            </dependency>


            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>ipaas-flow-engine</artifactId>
                <version>${ipaas.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>ipaas-api</artifactId>
                <version>${ipaas.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>ipaas-spring-common</artifactId>
                <version>${ipaas.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>ipaas-common</artifactId>
                <version>${ipaas.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>ipaas-db-proxy</artifactId>
                <version>${ipaas.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>ipaas-connector-proxy</artifactId>
                <version>${ipaas.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>ipaas-limiter</artifactId>
                <version>${ipaas.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>ipaas-log</artifactId>
                <version>${ipaas.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>ipaas-monitor</artifactId>
                <version>${ipaas.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>ipaas-quota</artifactId>
                <version>${ipaas.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.erpdss</groupId>
                <artifactId>ipaas-template</artifactId>
                <version>${ipaas.version}</version>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>1.21.3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <!-- 源码插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>

            <!-- 禁止使用指定的依赖 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>ban-unwanted-deps</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <bannedDependencies>
                                    <excludes>
                                        <exclude>org.springframework.data:spring-data-mongodb</exclude>
                                    </excludes>
                                    <searchTransitive>true</searchTransitive>
                                </bannedDependencies>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>