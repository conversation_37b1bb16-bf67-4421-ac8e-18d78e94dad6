package com.effektif.workflow.impl.activity.types;

import com.effektif.workflow.api.activities.BlockExecutionTask;
import com.effektif.workflow.api.activities.ExecutionItem;
import com.effektif.workflow.api.ext.ExecutionPojo;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.effektif.workflow.api.model.TaskId;
import com.effektif.workflow.impl.WorkflowInstanceStore;
import com.effektif.workflow.impl.WorkflowParser;
import com.effektif.workflow.impl.activity.AbstractActivityType;
import com.effektif.workflow.impl.ext.blockTask.BlockTask;
import com.effektif.workflow.impl.ext.blockTask.BlockTaskStore;
import com.effektif.workflow.impl.workflow.ActivityImpl;
import com.effektif.workflow.impl.workflow.WorkflowImpl;
import com.effektif.workflow.impl.workflowinstance.ActivityInstanceImpl;
import com.effektif.workflow.impl.workflowinstance.WorkflowInstanceImpl;
import com.facishare.paas.workflow.bus.EngineEventBus;
import com.facishare.paas.workflow.bus.api.BlockTaskEndEvent;
import com.facishare.paas.workflow.bus.api.BlockTaskCreateEvent;
import com.facishare.paas.workflow.bus.api.type.FlowTag;
import com.facishare.paas.workflow.bus.model.MQContext;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

/**
 * wansong
 * 服务编排中的阻塞节点
 * 920
 */
public class BlockExecutionTaskImpl extends AbstractActivityType<BlockExecutionTask> {

  protected WorkflowInstanceStore workflowInstanceStore;

  protected BlockTaskStore blockTaskStore;

  public BlockExecutionTaskImpl() {
    super(BlockExecutionTask.class);
  }

  @Override
  public void parse(ActivityImpl activityImpl, BlockExecutionTask executionTask, WorkflowParser parser) {
    super.parse(activityImpl, executionTask, parser);
    this.workflowInstanceStore = parser.getConfiguration(WorkflowInstanceStore.class);
    this.blockTaskStore = parser.getConfiguration(BlockTaskStore.class);
  }

  @Override
  public void execute(ActivityInstanceImpl activityInstance) {
    /**
     * 是否生成后续节点
     * 自动节点增加重试机制，节点上的任务执行失败时引擎应停止流转
     */
    WorkflowImpl workflow = activityInstance.workflow;
    String appId = workflow.getAppId();
    String type = workflow.getType();
    BlockTask task = saveBlockTask(activityInstance);
    //等待节点并非真正执行
    MQContext mqContext = MQContext.create(workflow.getTenantId(),
            appId, WorkflowConstants.SYSTEM_USER, null, type);
    sendCreateEvent(mqContext,task);
    //执行
    //如果执行时需要Block，这里就暂停， 等待后续需要执行的操作 执行完成后再继续
    //如果顺利执行结束，就执行下面的动作
    boolean isBlockType = BlockTask.ExecutionType.task.name().equals(task.getExecuteType());

    if(!isBlockType&&!execute(activityInstance,task)){
        sendEndEvent(mqContext,task);
        activityInstance.onwards();
    }
  }

  private BlockTask saveBlockTask(ActivityInstanceImpl activityInstance) {
    TaskId taskId = blockTaskStore.generateTaskId();
    //解析 更新对象时， 对象的值
    BlockTask task=new BlockTask(taskId, activityInstance.getId())
            .buildExecutionTask(
                    activityInstance,
                    convert(activity.getItem(), activityInstance.getSimpleVariables()),buildExtension(activityInstance,activity.getExtension())
            );
    WorkflowInstanceImpl workflowInstance = activityInstance.getWorkflowInstance();
    task.setExpireDate(workflowInstance.calculateExpireDate());
    blockTaskStore.insertTask(task);
    return task;
  }
    enum TaskType{
          update,create,ai,query,assign,auto_create,auto_update;
    }

    /**
     * 暂时放这里，后续如果任务比较多，使用工厂将构建任务的方法分离出来
     * 技术方案：
     * link: https://wiki.firstshare.cn/pages/viewpage.action?pageId=422936692
     */
    private Map buildExtension(ActivityInstanceImpl activityInstance, Map<String, Object> extension) {
      if(MapUtils.isNotEmpty(extension)){
          Map extensionMap = new HashMap(extension);
          TaskType taskType = TaskType.valueOf(extension.get("taskType").toString());
          switch(taskType){
              case update:{
                  Map object = (Map)activityInstance.getValue((String)extension.get("object"));
                  extensionMap.put("objectId", object.get("_id"));
                  extensionMap.put("object", object);
              }break;
              case create:{
                  extensionMap.put("object", activityInstance.getValue((String)extension.get("object")));
              }break;
          }
          return extensionMap;
      }
      return extension;
    }

    private boolean execute(ActivityInstanceImpl activityInstance,BlockTask blockTask) {
      try {
          return (boolean) method.invoke(bean.getDeclaredConstructor().newInstance(),activityInstance,blockTask);
      } catch (IllegalAccessException | InstantiationException | InvocationTargetException | NoSuchMethodException e) {
          log.error(e.getMessage(), e);
          return true;
      }
  }

  Class<?> bean = null;
  Method method = null;
  public static String SUPPORT = "com.facishare.paas.workflow.kernel.support.BlockExecutionTaskSupport";

    {
        try {
            bean = Class.forName(SUPPORT);
            method = bean.getMethod("execute",ActivityInstanceImpl.class ,BlockTask.class);
        } catch (ClassNotFoundException | NoSuchMethodException e) {
            log.info("如非引擎服务，无影响，忽略: {}",e.getMessage());
        }
    }

  private void sendEndEvent(MQContext mqContext,
                            BlockTask task
  ) {
      EngineEventBus.post(BlockTaskEndEvent.createEnd(
              mqContext,
              FlowTag.type(mqContext.getType()),
              task.getId().getInternal(),
              task.getWorkflowInstanceId(),
              task.getName(),task.getWorkflowName(),
              task.getTaskType(),task.getTriggerSource())
      );
  }

  private void sendCreateEvent(MQContext mqContext,
                               BlockTask task
  ) {
    EngineEventBus.post(BlockTaskCreateEvent.create(
            mqContext,
            FlowTag.type(mqContext.getType()),
            task.getId().getInternal(),
            task.getWorkflowInstanceId(),
            task.getName(),task.getWorkflowName(),task.getTaskType(),
            task.getTriggerSource())
    );
  }

  private ExecutionPojo convert(ExecutionItem item, Map<String, Object> variables) {
    ExecutionPojo pojo=null;
    if (Objects.nonNull(item)) {
        pojo = new ExecutionPojo();
        pojo.setTaskType(item.getTaskType());
        pojo.setSender(item.getSender());
        pojo.setRecipients(item.getRecipients());
        pojo.setCcRecipients(item.getCcRecipients());
        pojo.setBccRecipients(item.getBccRecipients());
        pojo.setEmailAddress(item.getEmailAddress());
        pojo.setTitle(item.getTitle());
        pojo.setContent(item.getContent());
        pojo.setTemplate(item.getTemplate());
        pojo.setFieldMapping(item.getFieldMapping());
        pojo.setUpdateFieldJson(item.getUpdateFieldJson());
        pojo.setUpdateFieldObject(item.getUpdateFieldObject());
        pojo.setTriggerParam(item.getTriggerParam());
        pojo.setAfterActionDefinitionId(item.getAfterActionDefinitionId());
        pojo.setAfterActionMappingId(item.getAfterActionMappingId());
        pojo.setActionMapping(item.getActionMapping());
        pojo.setActionParams(item.getActionParams());
        pojo.setVariables(variables);
        pojo.setCustomVariables(item.getCustomVariables());
    }
    return pojo;
  }

}
