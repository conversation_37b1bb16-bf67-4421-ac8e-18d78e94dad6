package com.fxiaoke.erpdss.ipaas.springcommon.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSSystemException;
import com.fxiaoke.erpdss.ipaas.springcommon.constans.CommonLabel;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * 统一的Json工具类
 * 基于Jackson实现，提供完整的JSON序列化/反序列化功能
 * 可方便切换底层实现
 */
@Slf4j
public class JsonUtil {

    private static final ObjectMapper OBJECT_MAPPER = createObjectMapper();

    /**
     * 创建和配置ObjectMapper实例
     */
    private static ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 时间处理，默认序列化为时间戳
        mapper.setTimeZone(TimeZone.getDefault());
//        mapper.registerModule(new JavaTimeModule());
//        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 序列化配置
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        // 反序列化配置
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.disable(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES);
        //空字符串转成Object时，会转为null；兼容前端js代码的习惯。
        mapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);

        return mapper;
    }

    /**
     * 获取ObjectMapper实例
     */
    public static ObjectMapper get() {
        return OBJECT_MAPPER;
    }

    // ========== 序列化方法 ==========

    /**
     * 对象转JSON字符串
     */
    public static String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("toJson error: {}", obj.getClass().getName(), e);
            throw new IPaaSSystemException(CommonLabel.jsonEncodeError.getI18nMsg(), e);
        }
    }

    /**
     * 对象转JSON字符串（安全版本，异常时返回null）
     */
    @Nullable
    public static String toJsonSafe(@Nullable Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            log.warn("toJsonSafe error: {}", obj.getClass().getName(), e);
            return null;
        }
    }

    /**
     * 对象转格式化的JSON字符串（用于调试）
     */
    public static String toPrettyJson(@Nullable Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("toPrettyJson error: {}", obj.getClass().getName(), e);
            throw new IPaaSSystemException(CommonLabel.jsonEncodeError.getI18nMsg(), e);
        }
    }

    // ========== 反序列化方法 ==========

    /**
     * JSON字符串转对象
     */
    @Nullable
    public static <T> T fromJson(@Nullable String json, Class<T> clazz) {
        if (!StringUtils.hasText(json)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (IOException e) {
            log.error("fromJson error: {} -> {}", json, clazz.getName(), e);
            throw new IPaaSSystemException(CommonLabel.jsonDecodeError.getI18nMsg(), e);
        }
    }

    /**
     * JSON字符串转对象（安全版本，异常时返回null）
     */
    @Nullable
    public static <T> T fromJsonSafe(@Nullable String json, Class<T> clazz) {
        if (!StringUtils.hasText(json)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            log.warn("fromJsonSafe error: {} -> {}", json, clazz.getName(), e);
            return null;
        }
    }

    // ========== 便捷方法 ==========

    /**
     * JSON字符串转List
     */
    @Nullable
    public static <T> List<T> fromJsonToList(@Nullable String json, Class<T> elementClass) {
        if (!StringUtils.hasText(json)) {
            return null;
        }
        JavaType listType = OBJECT_MAPPER.getTypeFactory()
                .constructCollectionType(List.class, elementClass);
        return fromJson(json, listType);
    }

    /**
     * JSON字符串转Map
     */
    @Nullable
    public static Map<String, Object> fromJsonToMap(@Nullable String json) {
        return fromJson(json, new TypeReference<Map<String, Object>>() {
        });
    }

    /**
     * JSON字符串转指定类型的Map
     */
    @Nullable
    public static <K, V> Map<K, V> fromJsonToMap(@Nullable String json,
                                                 Class<K> keyClass,
                                                 Class<V> valueClass) {
        if (!StringUtils.hasText(json)) {
            return null;
        }
        JavaType mapType = OBJECT_MAPPER.getTypeFactory()
                .constructMapType(Map.class, keyClass, valueClass);
        return fromJson(json, mapType);
    }


    /**
     * JSON字符串转对象（处理一层嵌套）
     */
    @Nullable
    public static <T> T fromJson(@Nullable String json, Class<?> rawType, Class<?>... parameterClasses) {
        if (!StringUtils.hasText(json)) {
            return null;
        }
        try {
            JavaType javaType = constructParametricType(rawType, parameterClasses);
            return OBJECT_MAPPER.readValue(json, javaType);
        } catch (IOException e) {
            log.error("fromJson error: {} -> {}", json, rawType.getTypeName(), e);
            throw new IPaaSSystemException(CommonLabel.jsonDecodeError.getI18nMsg(), e);
        }
    }


    /**
     * JSON字符串转对象（使用TypeReference处理泛型）
     */
    @Nullable
    public static <T> T fromJson(@Nullable String json, TypeReference<T> typeReference) {
        if (!StringUtils.hasText(json)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("fromJson error: {} -> {}", json, typeReference.getType(), e);
            throw new IPaaSSystemException(CommonLabel.jsonDecodeError.getI18nMsg(), e);
        }
    }

    /**
     * JSON字符串转对象（使用JavaType处理复杂泛型）
     */
    @Nullable
    public static <T> T fromJson(@Nullable String json, JavaType javaType) {
        if (!StringUtils.hasText(json)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, javaType);
        } catch (IOException e) {
            log.error("fromJson error: {} -> {}", json, javaType.getTypeName(), e);
            throw new IPaaSSystemException(CommonLabel.jsonDecodeError.getI18nMsg(), e);
        }
    }


    /**
     * 构建参数化类型（类似Jackson的constructParametricType）
     */
    public static JavaType constructParametricType(Class<?> rawType, Class<?>... parameterClasses) {
        return OBJECT_MAPPER.getTypeFactory().constructParametricType(rawType, parameterClasses);
    }

    /**
     * 构建参数化类型（支持JavaType参数）
     */
    public static JavaType constructParametricType(Class<?> rawType, JavaType... parameterTypes) {
        return OBJECT_MAPPER.getTypeFactory().constructParametricType(rawType, parameterTypes);
    }

    // ========== 对象转换 ==========

    /**
     * 对象深拷贝
     */
    @Nullable
    public static <T> T deepCopy(@Nullable T src, Class<T> clazz) {
        if (src == null) {
            return null;
        }
        return fromJson(toJson(src), clazz);
    }

    /**
     * 对象转换（通过Jackson的convertValue实现）
     */
    @Nullable
    public static <T> T convertValue(@Nullable Object src, Class<T> targetClass) {
        if (src == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(src, targetClass);
        } catch (IllegalArgumentException e) {
            log.error("convertValue error: {} -> {}", src.getClass().getName(), targetClass.getName(), e);
            throw new IPaaSSystemException("Object conversion failed", e);
        }
    }

    /**
     * 对象转换（使用TypeReference）
     */
    @Nullable
    public static <T> T convertValue(@Nullable Object src, TypeReference<T> typeReference) {
        if (src == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(src, typeReference);
        } catch (IllegalArgumentException e) {
            log.error("convertValue error: {} -> {}", src.getClass().getName(), typeReference.getType(), e);
            throw new IPaaSSystemException("Object conversion failed", e);
        }
    }

    /**
     * 对象转换（使用JavaType）
     */
    @Nullable
    public static <T> T convertValue(@Nullable Object src, JavaType javaType) {
        if (src == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(src, javaType);
        } catch (IllegalArgumentException e) {
            log.error("convertValue error: {} -> {}", src.getClass().getName(), javaType.getTypeName(), e);
            throw new IPaaSSystemException("Object conversion failed", e);
        }
    }
}
